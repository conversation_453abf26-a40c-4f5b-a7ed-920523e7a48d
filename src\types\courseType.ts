export interface CourseSession {
  id?: number;
  key: string;
  value: string;
}

export interface SkillsCovered {
  id?: number;
  key: string;
  value: string;
}

export interface ProgramOutline {
  id?: number;
  key: string;
  value: string;
}

export interface Course {
  id?: string;
  name: string;
  description: string;
  coverImage: string;
  mainTopic: string;
  subTopic: string;
  learningObjective: string;
  courseLink: string;
  deliveryMode: string;
  address: string;
  city: string;
  state: string;
  country: string;
  duration: string;
  durationUnit: string;
  isSelfPaced: boolean;
  isFlexible: boolean;
  experienceLevel: string;
  certificationType: string;
  startDate: string;
  endDate: string;
  courseFee: number;
  createdById?: string;
  lastModifiedById?: string;
  createdOn?: string;
  modifiedOn?: string;
  status?: 'Active' | 'Pending' | 'Draft' | 'Inactive';
  sessions: CourseSession[];
  programOutlines: ProgramOutline[];
  skillsCovered: SkillsCovered[];
  partner?: {
    id?: string;
    name?: string;
    email?: string;
    partnerId?: string;
    logo?: string;
    phone?: null;
  };
}

export interface CourseRespones {
  data: Course;
}

export type CourseFormStep = 'summary' | 'details' | 'skills' | 'review';

export const MAIN_TOPICS = [
  'Technology',
  'Business',
  'Design',
  'Marketing',
  'Finance',
  'Healthcare',
  'Education',
  'Other',
];

export const DELIVERY_MODES = ['In Person', 'Online', 'Hybrid'];

export const DURATION_UNITS = ['Hours', 'Days', 'Weeks', 'Months', 'Years'];

export const EXPERIENCE_LEVELS = ['Beginner', 'Intermediate', 'Advanced'];

export const CERTIFICATION_TYPES = [
  'University Degree',
  'Professional Certificate',
  'None',
];

export const CITIES = [
  'New York',
  'San Francisco',
  'London',
  'Tokyo',
  'Sydney',
  'Singapore',
  'Berlin',
  'Paris',
  'Toronto',
  'Other',
];

export interface CourseFilters {
  data?: Course[];
  search?: string;
  mainTopic?: string;
  deliveryMode?: 'All' | 'In Person' | 'Online' | 'Hybrid';
  city?: string;
  experienceLevel?: 'Beginner' | 'Intermediate' | 'Advanced' | 'All';
  dateType?: 'startDate' | 'createdOn' | 'modifiedOn';
  startDate?: string;
  endDate?: string;
  status?: 'Active' | 'Pending' | 'Draft' | 'Inactive';
  createdById?: string;
  sortField?: string;
  ascending?: boolean;
  page?: number;
  pageSize?: number;
  name?: string;
  duration?: string;
  courseFee?: number;
}

export interface CourseListResponse {
  data: Course[];
  total: number;
  page: number;
  pageSize: number;
  totalPages?: number;
}

export interface ProgramOutline {
  id?: number;
  key: string;
  value: string;
}

export interface CourseSkill {
  name: string;
  category?: string;
}

export interface CourseListResponse {
  data: Course[];
  total: number;
  page: number;
  pageSize: number;
}

export interface Learner {
  id: string;
  name: string;
  email: string;
  enrolledDate: string;
  status: 'Enrolled' | 'Completed';
}

export interface LearnerFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: 'Enrolled' | 'Completed' | 'All';
  sortField?: string;
  ascending?: boolean;
}

export interface LearnerListResponse {
  data: Learner[];
  total: number;
  page: number;
  pageSize: number;
}

export interface CourseEnrollment {
  id: string;
  userId: string;
  userName: string;
  email: string;
  enrollmentDate: string;
  status: 'Enrolled' | 'Completed';
  courseId: string;
}

export interface EnrollmentFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: 'Enrolled' | 'Completed' | 'All';
  sortField?: string;
  ascending?: boolean;
}

export interface EnrollmentListResponse {
  success: boolean;
  message: string;
  data: CourseEnrollment[];
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  statusCounts: {
    totalEnrolled: number;
    completed: number;
    notCompleted: number;
  };
}

export interface StatsResponse {
  data: {
    totalCourses: number;
    activeCourses: number;
    completedEnrollments: number;
    totalEnrolledUsers: number;
  };
}

export interface TrainingProviderRequest {
  name: string;
  description: string;
  website: string;
  categories: string[];
  accreditations: string[];
  partnerId: string;
  createdById: string;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

export interface MainTopic {
  id: string;
  name: string;
}
