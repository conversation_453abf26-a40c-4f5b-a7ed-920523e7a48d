import ArrowIcon from '@/components/icons/ArrowRight';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import React from 'react';

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick?: () => void;
}

export const ActionCard: React.FC<ActionCardProps> = ({
  icon,
  title,
  description,
  onClick,
}) => {
  return (
    <Card
      onClick={onClick}
      className="border flex flex-row bg-white rounded-lg shadow-md p-5  space-y-2 border-neutral-200 hover:border-primary-500 cursor-pointer"
    >
      <CardContent>
        {icon}
        <div className="mt-4 items-center justify-between">
          <CardTitle className="font-semibold text-[--headingTextColor] text-[20px] max-w-[80%] mb-2">
            {title}
          </CardTitle>
          <p className="text-[16px] font-normal text-neutral-500">
            {description}
          </p>
        </div>
      </CardContent>
      <div className="relative right-0 items-end justify-end flex">
        <ArrowIcon />
      </div>
    </Card>
  );
};
