'use client';

import React from 'react';
import { footerData, socialRoute } from '@/constants';

import Image from 'next/image';
import Logo from '@/constants/CAREER_LOGO';
import type { NavItemType } from '@/interface';

export interface WidgetFooterMenu {
  id: string;
  title: string;
  menus: NavItemType[];
}

const Footer: React.FC = () => {
  return (
    <>
      <footer className="bg-transparent">
        <div
          className="text-black mx-auto p-15 px-6 pb-10"
          style={{
            background: 'rgba(255, 255, 255, 0.50)',
            backdropFilter: 'blur(8px)',
          }}
        >
          <div className="lg:grid lg:grid-cols-12 md:flex md:flex-col gap-40 px-20 py-8 md:py-12 text-[#111827] border-b border-b-slate-200 ">
            {footerData.map((section) => (
              <div
                key={section.title}
                className="sm:col-span-6 md:col-span-3 lg:col-span-2"
              >
                <h6 className="text-[#111827] text-[14px] font-[500] leading-[20px]">
                  {section.title}
                </h6>
                <ul className="text-[14px] mt-4 font-[500] leading-[20px] text-[#6B7280]">
                  {section.links.map((link) => (
                    <li key={link.href} className="mb-2 py-2">
                      <a
                        href={link.href}
                        className="text-[--bodyTextColor] hover:text-[--bodyTextColor] transition duration-150 ease-in-out"
                      >
                        {link.label}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="px-20 mt-8 justify-between flex">
            <Logo className="self-center w-68" defaultRoute={''} />

            <div className="flex gap-6">
              {socialRoute.map((social, index) => (
                <Image
                  // eslint-disable-next-line react/no-array-index-key
                  key={index}
                  src={social.icon}
                  width={20}
                  height={20}
                  alt={`social-icon-${index}`}
                />
              ))}
            </div>

            <h6 className="text-[14px] font-normal leading-[20px] text-[#6B7280]">
              © {new Date().getFullYear()} Career Navigator. All rights
              reserved.
            </h6>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
