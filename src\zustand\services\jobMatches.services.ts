import axiosClient from '@/utils/axiosClient';
import type { ICoverLetterInputs, IJobMatch, IJobMatchBody } from '@/types';

const API_URL = '/ai/JobMatches';

const JobMatchesServices = {
  getJobMatches: async (
    data: IJobMatchBody
  ): Promise<{ data: { jobs: IJobMatch[] } }> => {
    // Send data with both root properties and jobMatchRequest field
    const requestBody = {
      ...data,
      jobMatchRequest: data
    };

    const response = await axiosClient.post<{ data: { jobs: IJobMatch[] } }>(
      `${API_URL}/generate-job-matches`,
      requestBody
    );
    return response?.data;
  },

  generateCoverLetter: async (
    data: ICoverLetterInputs
  ): Promise<{ data: string }> => {
    const response = await axiosClient.post<{ data: string }>(
      `${API_URL}/generate-application-message`,
      data
    );
    return response?.data;
  },
};

export default JobMatchesServices;
