import axiosClient from '@/utils/axiosClient';
import type { ICoverLetterInputs, IJobMatch, IJobMatchBody } from '@/types';

const API_URL = '/ai/JobMatches';

const JobMatchesServices = {
  getJobMatches: async (
    data: IJobMatchBody
  ): Promise<{ data: { jobs: IJobMatch[] } }> => {
    const response = await axiosClient.post<{ data: { jobs: IJobMatch[] } }>(
      `${API_URL}/generate-job-matches`,
      data
    );
    return response?.data;
  },

  generateCoverLetter: async (
    data: ICoverLetterInputs
  ): Promise<{ data: string }> => {
    const response = await axiosClient.post<{ data: string }>(
      `${API_URL}/generate-application-message`,
      data
    );
    return response?.data;
  },
};

export default JobMatchesServices;
