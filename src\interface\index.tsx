import type { ComponentType } from 'react';
import type { Route } from 'next';

export type PathName = Route;

export interface MegamenuItem {
  id: string;
  image: string;
  title: string;
  items: NavItemType[];
}

export interface NavItemType {
  id: string;
  name: string;
  isNew?: boolean;
  href?: PathName;
  targetBlank?: boolean;
  children?: NavItemType[];
  megaMenu?: MegamenuItem[];
  type?: 'dropdown' | 'megaMenu' | 'none';
  component?: ComponentType<unknown>;
}
