'use client';

import { Loader2 } from 'lucide-react';

interface LoadingScreenProps {
  message?: string;
  isVisible: boolean;
}

export function LoadingScreen({
  message = 'Switching versions...',
  isVisible,
}: LoadingScreenProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-[#0000]/40 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-xl max-w-sm w-full mx-4">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <div className="text-center">
            <h3 className="text-lg font-semibold text-neutral-900 dark:text-white">
              {message}
            </h3>
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
              Please wait a moment...
            </p>
          </div>
          <div className="w-full bg-neutral-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full animate-pulse"
              style={{ width: '60%' }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}
