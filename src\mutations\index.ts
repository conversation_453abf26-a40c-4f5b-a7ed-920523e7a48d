import userService from '@/services/user/user.service';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';
import { useNotificationStore } from '@/zustand/store/notificationStore';

export const useUploadCvCustomMutation = (onSuccessCallback?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotificationStore.getState();
  const mutation = useMutation({
    mutationFn: userService.uploadCv,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: () => {
      showNotification('cv uploaded successfully', 'success');
      if (onSuccessCallback) onSuccessCallback();
    },
    onError: (error: {
      response?: { data?: { message?: string } };
      message?: string;
    }) => {
      toast(
        error?.response?.data?.message ||
          error.message ||
          'There was an error uploading cv. Please try again'
      );
    },
  });

  return { uploadCv: mutation.mutate, isLoading, ...mutation };
};
export const useDeleteCvCustomMutation = (onSuccessCallback?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotificationStore.getState();
  const mutation = useMutation({
    mutationFn: userService.deleteCv,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: () => {
      showNotification('cv deleted successfully', 'success');
      if (onSuccessCallback) onSuccessCallback();
    },
    onError: (error: {
      response?: { data?: { message?: string } };
      message?: string;
    }) => {
      toast(
        error?.response?.data?.message ||
          error.message ||
          'There was an error deleting cv. Please try again'
      );
    },
  });

  return { deleteCv: mutation.mutate, isLoading, ...mutation };
};

export const useUpdateUser = (onSuccessCallback?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotificationStore.getState();
  const mutation = useMutation({
    mutationFn: userService.updateUser,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: () => {
      showNotification('Account updated successfully!', 'success');
      if (onSuccessCallback) onSuccessCallback();
    },
    onError: (error: {
      response?: { data?: { message?: string } };
      message?: string;
    }) => {
      toast(
        error?.response?.data?.message ||
          error.message ||
          'There was an error updating details. Please try again'
      );
    },
  });
  return { updateUser: mutation.mutate, isLoading, ...mutation };
};
