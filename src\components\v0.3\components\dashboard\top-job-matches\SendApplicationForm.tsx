'use client';
import type { FC } from 'react';
import { Button } from '@/components/ui/button';
import { Formik, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import Image from 'next/image';
import AiMagicStar from '@/assets/images/dashboard/aiMagicStar.svg';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';

const validationSchema = Yup.object().shape({
  message: Yup.string().max(250).required('Message is required'),
});

interface SendApplicationFormProps {
  handleSendApplication: (_values: { message?: string }) => void;
}

const SendApplicationForm: FC<SendApplicationFormProps> = ({
  handleSendApplication,
}) => {
  const { message } = useJobMatchesStore();

  return (
    <>
      <Formik
        initialValues={{ message }}
        validationSchema={validationSchema}
        enableReinitialize
        onSubmit={async (values, { setSubmitting }) => {
          try {
            handleSendApplication(values);
          } catch (error) {
            console.error('login failed:', error);
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({ handleChange, handleBlur, values, isSubmitting, errors }) => (
          <Form className="grid grid-cols-1 gap-4">
            <div className="rounded-[20px] border-[1px] border-neutral-200 p-4">
              <div
                className="font-bold uppercase leading-tight tracking-wide flex gap-2 pb-1"
                style={{
                  background:
                    'linear-gradient(135deg, #4568DC 0%, #B06AB3 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  color: 'transparent',
                }}
              >
                <Image
                  src={AiMagicStar}
                  alt="ai_magic_star"
                  width={20}
                  height={20}
                />
                Your Message
              </div>

              <div className="space-y-1">
                <textarea
                  name="message"
                  rows={9}
                  value={values.message}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder={`Hi Google hiring team,

I’m excited to apply for the Junior Data Analyst role. With a strong foundation in computer science and a passion for turning data into actionable insights, I’m eager to contribute to meaningful projects and grow within a team that values innovation. I’d love the opportunity to bring my skills to Google and make an impact.

Best regards,Layla`}
                  className="w-full text-[--bodyTextColor] border-none focus:outline-none focus:ring-0 resize-none"
                />
                <div className="flex justify-between items-center w-full">
                  <div>
                    {errors && (
                      <ErrorMessage
                        name={'message'}
                        component="div"
                        className="text-[16px] !text-destructive-500"
                      />
                    )}
                  </div>
                  <p>{values.message.length}/250</p>
                </div>
              </div>
            </div>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="justify-center my-4 bg-[--buttonColor] flex items-center gap-2 text-[16px] py-[14px] px-[28px] h-[48px]"
            >
              Send application
            </Button>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default SendApplicationForm;
