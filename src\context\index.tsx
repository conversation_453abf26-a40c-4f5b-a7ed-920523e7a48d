/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-wrapper-object-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Route as NextRoute } from 'next';
import type { ComponentType } from 'react';
import { createContext } from 'react';

// eslint-disable-next-line no-unused-vars
export type Route<T = string> = NextRoute;
export type PathName = Route<string>;

export interface Page {
  path: PathName;
  exact?: boolean;
  component: ComponentType<Object>;
}

export interface MegamenuItem {
  id: string;
  image: string;
  title: string;
  items: NavItemType[];
}

export interface NavItemType {
  id: string;
  name: string;
  isNew?: boolean;
  href?: PathName;
  targetBlank?: boolean;
  children?: NavItemType[];
  megaMenu?: MegamenuItem[];
  type?: 'dropdown' | 'megaMenu' | 'none';
  component?: any;
}

export type ApplicationContextType = {
  navConfig?: NavItemType[];
  footerNavConfig?: NavItemType[];
  applicationRoutes?: any;
  startIdealTimer?: boolean;
  setNavConfig?: React.Dispatch<React.SetStateAction<NavItemType[]>>;
  setFooterNavConfig?: React.Dispatch<React.SetStateAction<NavItemType[]>>;
  setApplicationRoutes?: React.Dispatch<React.SetStateAction<any>>;
  windowWidth: number;
  setstartIdealTimer?: React.Dispatch<React.SetStateAction<boolean>>;
  isRightNavVisible: boolean;
  setIsRightNavVisible?: (_isVisible: boolean) => void;
  openShareFeedbackModel?: () => void;
  userSnapSpace?: any;
  setUserSnapSpace?: React.Dispatch<React.SetStateAction<any>>;
};

export const ApplicationContext = createContext<ApplicationContextType>({
  windowWidth: 0,
  isRightNavVisible: true,
});
