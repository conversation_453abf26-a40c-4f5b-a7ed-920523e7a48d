'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

import { useAssessmentScores } from '@/queries';
import { Loader } from '@/components/common/Loader';
import AssessmentsResultsPage from './results/page';
import DiscoverCareer from './introduction/page';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';

function Index() {
  const router = useRouter();
  const [testCompleted, setTestCompleted] = useState(false);
  const { data: assessmentScores, isLoading } = useAssessmentScores();

  useEffect(() => {
    if (!isLoading && assessmentScores && assessmentScores.length > 0) {
      const completed = assessmentScores.every((score) => score.score > 0);
      setTestCompleted(completed);
    }
  }, [assessmentScores, isLoading, router]);

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  return (
    <>
      <div className="my-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'Career Guidance',
              href: '/dashboard/career-guidance/career-test',
            },
            {
              label: 'Career Test',
              href: '/dashboard/career-guidance/career-test/test',
            },
          ]}
        />
      </div>
      {testCompleted ? <AssessmentsResultsPage /> : <DiscoverCareer />}
    </>
  );
}

export default Index;
