'use client';

import React from 'react';
import Image from 'next/image';
import { Background } from '@/assets';
import FeatureCard from './components/FeatureCard';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['latin'] });

const cardsData = [
  {
    id: 1,
    title: 'Company Logo',
    description:
      "Search for job openings in different fields. Whether you're new to the workforce or have years of experience, there's something for everyone.",
  },
  {
    id: 2,
    title: 'Company Logo',
    description:
      'Explore courses and training programs to improve your skills or gain real-world experience while earning money.',
  },
  {
    id: 3,
    title: 'Company Logo',
    description:
      "Not sure what's next? Use our career planning tools to find out what jobs match your skills and interests.",
  },
  {
    id: 4,
    title: 'Company Logo',
    description:
      'See what government support is available, including unemployment benefits, training grants, and other financial aid to help you along the way.',
  },
  {
    id: 5,
    title: 'Company Logo',
    description:
      'Find out which jobs are in demand and what skills employers are looking for.',
  },
  {
    id: 6,
    title: 'Company Logo',
    description:
      'Find out which jobs are in demand and what skills employers are looking for.',
  },
];

function Feature() {
  return (
    <section className="relative w-full min-h-[70vh]">
      <Image
        src={Background}
        alt="Feature Background"
        fill
        priority
        className="object-cover object-center -z-10"
      />

      <div className="absolute inset-0 flex flex-col text-center text-black fade-in-animation w-[90%] md:w-[80%] mx-auto py-8">
        <h3
          className={`${montserrat.className} font-semibold leading-[44px] text-[32px] mt-4 text-center mx-auto text-neutral-900 max-w-3xl`}
        >
          Over 100,000+ jobs, apprenticeships and training programs offered by
          leading companies.
        </h3>

        <div className="grid grid-cols-1 mt-10 sm:grid-cols-2 md:grid-cols-3 gap-8">
          {cardsData.map((card) => (
            <FeatureCard key={card.id} title={card.title} />
          ))}
        </div>
      </div>
    </section>
  );
}

export default Feature;
