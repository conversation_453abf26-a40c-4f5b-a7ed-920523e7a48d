/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Info } from 'lucide-react';

type ModalType = 'personal' | 'employment';

interface UpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (_data: any) => void;
  type: ModalType;
}

const UpdateModal: React.FC<UpdateModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  type,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would collect form data
    onSubmit({});
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[35rem]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
            Request Update
          </DialogTitle>
          <DialogClose asChild></DialogClose>
        </DialogHeader>

        <div className="py-4">
          <p className="text-[18px] text-neutral-700 leading-[28px] font-normal">
            Let us know which details are incorrect, and we will get in touch to
            help you update them.
          </p>

          <form onSubmit={handleSubmit}>
            {type === 'personal' ? (
              <>
                <div className="mb-4 mt-4">
                  <h3 className="mb-2 font-semibold text-[16px] text-neutral-900 leading-[24px]">
                    Name
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="first-name" />
                      <label
                        htmlFor="first-name"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        First Name
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="last-name" />
                      <label
                        htmlFor="last-name"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Last Name
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <h3 className="mb-2 font-semibold text-[16px] text-neutral-900 leading-[24px]">
                    Nationality
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="national-id" />
                      <label
                        htmlFor="national-id"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        National ID
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="nationality" />
                      <label
                        htmlFor="nationality"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Nationality
                      </label>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4 mt-4">
                  <h3 className="mb-2 font-semibold text-[16px] text-neutral-900 leading-[24px]">
                    Employment
                  </h3>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="status" />
                      <label
                        htmlFor="status"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Status
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="profession" />
                      <label
                        htmlFor="profession"
                        className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Profession
                      </label>
                    </div>
                  </div>
                </div>
              </>
            )}

            <div className="mb-4">
              <h3 className="mb-2 font-semibold text-[16px] text-neutral-900 leading-[24px]">
                Salary
              </h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="monthly-salary" />
                  <label
                    htmlFor="monthly-salary"
                    className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Monthly salary
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="pensionable-salary" />
                  <label
                    htmlFor="pensionable-salary"
                    className="text-[16px] font-normal leading-[24px] text-neutral-900 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Pensionable monthly salary
                  </label>
                  <Info className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[16px] text-neutral-900 leading-[24px]">
                Details
              </h3>
              <Textarea
                placeholder="Enter details..."
                className="min-h-[120px] text-[16px]"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="text-[16px]">
                Request Update
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateModal;
