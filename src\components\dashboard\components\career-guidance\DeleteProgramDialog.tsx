/* eslint-disable no-unused-vars */
'use client';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface DeleteProgramDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  programToDelete: { id: string; name: string } | null;
  onDelete: () => void;
  isDeleting: boolean;
}

export function DeleteProgramDialog({
  isOpen,
  onOpenChange,
  programToDelete,
  onDelete,
  isDeleting,
}: DeleteProgramDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the program &quot;
            {programToDelete?.name}&quot;?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Program'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
