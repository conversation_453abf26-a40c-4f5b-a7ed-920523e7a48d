import type { ISkillsRecommendation } from '@/types/aiTypes';
import { create } from 'zustand';

interface SkillRecommendationState {
  topSkills: ISkillsRecommendation | null;
  setTopSkills: (_topSkills: ISkillsRecommendation) => void;
  getTopSkills: () => ISkillsRecommendation | null;
  clearTopSkills: () => void;
}

const useAiTopSkillStore = create<SkillRecommendationState>((set, get) => ({
  topSkills: null,
  setTopSkills: (topSkills: ISkillsRecommendation) => set({ topSkills }),
  getTopSkills: () => get().topSkills,
  clearTopSkills: () => set({ topSkills: null }),
}));

export default useAiTopSkillStore;
