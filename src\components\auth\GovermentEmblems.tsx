import React from 'react';
import GovernmentEmblemImage from '@/assets/governmentEmblem.svg';
import Image from 'next/image';
import useSettingsStore from '@/zustand/store/settingsStore';

export function GovernmentEmblem() {
  const { appearanceSettings } = useSettingsStore();

  return (
    <div className="fixed bottom-4 left-4 z-50 pb-3">
      <Image
        src={appearanceSettings?.governmentEmblemDark ?? GovernmentEmblemImage}
        alt="Government Emblem"
        width={100}
        height={100}
        className="w-auto h-[60px]"
      />
    </div>
  );
}
