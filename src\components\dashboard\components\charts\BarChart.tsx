import { Card } from '@/components/ui/card';
import {
  <PERSON><PERSON>hart as Bar<PERSON>rap<PERSON>,
  XAxis,
  ResponsiveContainer,
  Bar,
  CartesianGrid,
  YAxis,
  Tooltip,
} from 'recharts';

const revenue = [
  { name: 'Accountant', total: 50 },
  { name: 'Financial Analyst', total: 44 },
  { name: 'CFO', total: 40 },
  { name: 'Bookkeeper', total: 34 },
  { name: 'Finance Clerk', total: 30 },
  { name: 'Accounting Manager', total: 25 },
  { name: 'Auditor', total: 23 },
  { name: 'Tax Accountant', total: 21 },
  { name: 'Accounting Officer', total: 19 },
  { name: 'Finance Manager', total: 16 },
];

export const EducationalBarCharts = ({
  brandColor,
}: {
  brandColor: string;
}) => {
  return (
    <Card className="flex flex-col">
      <div className="py-8 px-4">
        <ResponsiveContainer width="100%" height={250}>
          <BarGraph data={revenue} barCategoryGap="10%">
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={true}
              stroke="#000000"
              fontSize={12}
              interval={0}
              textAnchor="middle"
              height={30}
              tick={({
                x,
                y,
                payload,
              }: {
                x: number;
                y: number;
                payload: { value: string };
              }) => (
                <text
                  x={x}
                  y={y + 10}
                  textAnchor="middle"
                  fontSize={10}
                  style={{
                    whiteSpace: 'normal',
                    wordWrap: 'break-word',
                    gap: '40px',
                  }}
                >
                  {payload.value
                    .split(' ')
                    .map((word: string, index: number) => (
                      <tspan key={word} x={x} dy={index === 0 ? 0 : 14}>
                        {word}
                      </tspan>
                    ))}
                </text>
              )}
            />
            <YAxis
              domain={[0, 75]}
              ticks={[0, 25, 50, 75]}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip />
            <Bar dataKey="total" radius={[5, 5, 0, 0]} fill={brandColor} />
          </BarGraph>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};
