/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import SpaceshipIcon from '@/components/icons/JobIcon';
import { useRouter } from 'next/navigation';
import { getRecommendedCourses } from '@/zustand/services/course.services';
import type { Course } from '@/types/courseType';
import CourseCard from '../courses/CourseCard';
import useSettingsStore from '@/zustand/store/settingsStore';

function UpskillSection() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const brandColor = appearanceSettings?.brandColor;

  const fetchRecommendedCourses = async () => {
    setIsLoading(true);
    try {
      const response = await getRecommendedCourses();
      const mappedCourses = response.data.map((item: any) => ({
        ...item.courseDetails,
        id: item.courseId,
      }));
      setRecommendedCourses(mappedCourses);
    } catch (err) {
      console.error('Error fetching recommended courses:', err);
      setError('Failed to load recommended courses');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendedCourses();
  }, []);

  return (
    <div>
      <SectionHeader
        title="Access up-skilling recommendations to stay relevant in the job market"
        subtitle=""
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {isLoading ? (
          Array(3)
            .fill(0)
            .map((_, index) => (
              <div
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                className="bg-white rounded-lg shadow-sm p-4 animate-pulse"
              >
                <div className="h-40 bg-gray-200 rounded mb-4"></div>
                <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/5"></div>
                </div>
              </div>
            ))
        ) : error ? (
          <div className="col-span-3 text-center py-8 text-red-500">
            {error}
          </div>
        ) : (
          recommendedCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))
        )}

        <ActionCard
          onClick={() => router.push('/dashboard/career-guidance/study-path')}
          icon={<SpaceshipIcon stroke={brandColor} />}
          title={'Discover all up-skilling recommendations'}
          description={
            'Explore programs to help you level up your skills and stay competitive in the job market.'
          }
        />
      </div>
    </div>
  );
}

export default UpskillSection;
