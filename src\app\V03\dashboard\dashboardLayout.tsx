'use client';

import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { ActionsAlert } from '@/components/dashboard/components/ActionsAlert';
import DashboardFooter from '@/components/dashboard/components/DashboardFooter';
import TopBar from '@/components/dashboard/components/TopBar';
import useSettingsStore from '@/zustand/store/settingsStore';
import { LoadingScreen } from '@/components/common/LoadingVersionScreen';
import { CareerAssistant } from '../../../components/v0.3/ai/AssistantDrawer';
import V03AiAssistant from '../../../components/v0.3/ai';
import V03TopNavigation from '@/components/v0.3/components/dashboard/components/V03TopNavigation';
import V03SideNavigation from '@/components/v0.3/components/dashboard/components/V03SideNavigation';

const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const [showActionsAlert, setShowActionsAlert] = useState(false);
  const [isVersionFlag, setIsVersionFlag] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);

  const { appearanceSettings } = useSettingsStore();
  const mainRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  useEffect(() => {
    const isV3Path = pathname.startsWith('/V03');
    setIsVersionFlag(isV3Path);
  }, [pathname]);

  const toggleActionsAlert = () => {
    setShowActionsAlert((prev) => !prev);
  };

  const toggleVersionFlag = (checked: boolean) => {
    setIsVersionFlag(checked);
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1200);
  };

  const toggleMobileNav = () => setMobileNavOpen((prev) => !prev);
  const toggleMinimized = () => setIsMinimized((prev) => !prev);

  const {
    isBgStyleColor,
    bgStyleColor,
    bgStyleImg,
    overlayColor,
    overlayPercentage,
  } = appearanceSettings;

  const bgStyle = isBgStyleColor
    ? { backgroundColor: bgStyleColor }
    : {
        backgroundImage: `url(${bgStyleImg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      };

  useEffect(() => {
    mainRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, [pathname]);

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      setMobileNavOpen(false);
    }
  };

  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && mobileNavOpen) {
        setMobileNavOpen(false);
      }
    };

    if (mobileNavOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [mobileNavOpen]);

  return (
    <>
      <LoadingScreen
        isVisible={isLoading}
        message="Switching to Version0.2..."
      />

      <div className="flex flex-col h-screen overflow-hidden">
        <TopBar
          isVersionFlag={isVersionFlag}
          onToggleVersionFlag={toggleVersionFlag}
          toggleActionsAlert={toggleActionsAlert}
          toggleMobileNav={toggleMobileNav}
        />

        <div className="hidden lg:block">
          <V03TopNavigation toggleActionsAlert={toggleActionsAlert} />
        </div>

        <div className="flex flex-1 overflow-hidden relative" style={bgStyle}>
          {!isBgStyleColor && (
            <div
              className="fixed top-0 left-0 w-full h-full z-0"
              style={{
                backgroundColor: overlayColor,
                opacity: Number(overlayPercentage) / 100,
              }}
            />
          )}

          {mobileNavOpen && (
            <div
              className="lg:hidden fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out"
              onClick={handleBackdropClick}
            >
              <div
                className={`max-w-[280px] h-full transform ${
                  mobileNavOpen ? 'translate-x-0' : '-translate-x-full'
                } transition-transform duration-300 ease-in-out`}
                onClick={(e) => e.stopPropagation()}
              >
                <V03SideNavigation onClose={toggleMobileNav} />
              </div>
            </div>
          )}
          <div className="flex flex-1 relative z-10">
            <main
              ref={mainRef}
              className="flex-1 overflow-y-auto scrollbar-hide"
            >
              {showActionsAlert && <ActionsAlert />}
              <section className="p-8 min-h-full max-w-[1200px] mx-auto">
                {children}
              </section>
            </main>
          </div>

          <div className="hidden lg:block">
            <V03AiAssistant onToggle={toggleMinimized} />
            <CareerAssistant
              isMinimized={isMinimized}
              onToggle={toggleMinimized}
            />
          </div>
        </div>

        <div>
          <DashboardFooter />
        </div>
      </div>
    </>
  );
};

export default DashboardLayout;
