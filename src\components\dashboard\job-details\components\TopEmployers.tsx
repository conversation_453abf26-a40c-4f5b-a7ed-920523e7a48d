// app/components/TopEmployers.tsx
const employers = [
  { name: 'Energy Jobline', posts: 309 },
  { name: 'EPAM Anywhere', posts: 239 },
  { name: 'Airswift', posts: 211 },
  { name: 'Mondelez International', posts: 122 },
  { name: 'EPAM Systems', posts: 107 },
];

export default function TopEmployers() {
  return (
    <div className="">
      <h2 className="text-lg font-semibold mb-2">
        Top Employers in Kazakhstan
      </h2>
      <ul className="space-y-1">
        {employers.map((emp, idx) => (
          <li key={emp.name} className="flex justify-between text-[16px]">
            <span>
              {idx + 1}. {emp.name}
            </span>
            <span>{emp.posts} job posts</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
