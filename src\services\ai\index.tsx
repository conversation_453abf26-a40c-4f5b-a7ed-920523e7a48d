import axiosClient from '@/utils/axiosClient';

const API_URL = '/ai/';

class AiService {
  async generateSkillRecommendation(TargetRole: string) {
    const data = await axiosClient.post(
      `${API_URL}upskilling/generate-skills-recommendation`,
      {
        TargetRole,
      }
    );
    return data?.data;
  }

  async generateUpskillingSkillDetails(skillName: string) {
    const data = await axiosClient.post(
      `${API_URL}upskilling/generate-skill-detail`,
      {
        skillName,
      }
    );
    return data?.data;
  }

  async generateGrowthSteps(TargetRole: string) {
    const data = await axiosClient.post(
      `${API_URL}CareerRoadmap/generate-growth-opportunity`,
      {
        TargetRole,
      }
    );
    return data?.data;
  }

  async generateOpportunityChecklist(TargetRole: string) {
    const data = await axiosClient.post(
      `${API_URL}CareerRoadmap/generate-growth-opportunity-checklist`,
      {
        TargetRole,
      }
    );
    return data?.data;
  }

  async generateTopJobMatches(params: {
    skills: string;
    jobMode: string;
    salaryExpectation: string;
    Experience: string;
    Industry: string;
    Location: string;
  }) {
    const data = await axiosClient.post(
      `${API_URL}JobMatches/generate-job-matches`,
      params
    );
    return data?.data;
  }
}

const aiService = new AiService();
export default aiService;
