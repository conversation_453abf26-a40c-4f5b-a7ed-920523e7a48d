'use client';

import { Card } from '@/components/ui/card';
import React, { useCallback, useEffect, useState } from 'react';
import * as Switch from '@radix-ui/react-switch';
import { Button } from '@/components/ui/button';
import CaretDownRedIcon from '@/components/icons/CaretDownRed';
import CaretUpGreenIcon from '@/components/icons/CaretUpGreen';
import TopEmployers from './TopEmployers';
import OccupationCard from './OccupationCard';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { CustomValues } from '@/types/customValues';
import { Label } from '@/components/ui/label';
import ResetFilterIcon from '@/components/icons/ResetFilter';

function Trends() {
  const [industryOptions, setIndustryOptions] = useState<string[]>([]);
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [entryLevel, setEntryLevel] = useState(false);
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<
    'Quarter' | '1 Year' | '3 Years' | 'Custom'
  >('Quarter'); // Default to Quarter
  const [jobCount, setJobCount] = useState<number>(1280);
  const [growthPercentage, setGrowthPercentage] = useState<number>(8);

  const [isChecked, setIsChecked] = useState({
    'Quarter': false,
    '1 Year': false,
    '3 Years': false,
    'Custom': false,
  });

  const handleChoiceClick = useCallback(
    (choice: 'Quarter' | '1 Year' | '3 Years' | 'Custom') => {
      setIsChecked(() => ({
        'Quarter': false,
        '1 Year': false,
        '3 Years': false,
        'Custom': false,
        [choice]: true,
      }));
      setSelectedTimeFrame(choice);

      if (choice === 'Quarter') {
        setJobCount(1280);
        setGrowthPercentage(8);
      } else if (choice === '1 Year') {
        setJobCount(5400);
        setGrowthPercentage(15);
      } else if (choice === '3 Years') {
        setJobCount(15400);
        setGrowthPercentage(-5);
      } else {
        setJobCount(720);
        setGrowthPercentage(2);
      }
    },
    []
  );

  const fetchDropdownOptions = async () => {
    try {
      const response = await fetch('/api/jobs');
      const jobTypeData = await response.json();

      if (Array.isArray(jobTypeData)) {
        setIndustryOptions(jobTypeData);
      } else {
        console.error('Invalid API response format:', jobTypeData);
      }
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
    }
  };

  useEffect(() => {
    fetchDropdownOptions();
  }, []);

  const fetchDropdownOptions1 = async () => {
    try {
      const response = await fetch(
        'https://career-navigator-pro-api-dot-careernavigator-430608.el.r.appspot.com/api/jobs'
      );
      const jobTypeData = await response.json();

      if (Array.isArray(jobTypeData)) {
        setIndustryOptions(jobTypeData);
      } else {
        console.error('Invalid API response format:', jobTypeData);
      }
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
    }
  };

  useEffect(() => {
    fetchDropdownOptions1();
  }, []);

  const yearSections = ['Quarter', '1 Year', '3 Years', 'Custom'];

  const resetFilters = () => {
    setSelectedIndustry('');
    setSelectedLocation('');
    setEntryLevel(false);
    setIsChecked({
      'Quarter': false,
      '1 Year': false,
      '3 Years': false,
      'Custom': false,
    });
    setSelectedTimeFrame('Quarter');
  };

  const getSectorClass = useAdminValues({
    category: AdminValuesCategories?.sector?.category,
  });
  const sectorClass = getSectorClass.data?.data?.data?.customValues || [];

  const getOccupationClassification = useAdminValues({
    category: AdminValuesCategories?.occupationClassification?.category,
    subcategory:
      AdminValuesCategories?.occupationClassification?.subcategories?.ISCO_4,
  });
  const occupationClassification =
    getOccupationClassification.data?.data?.data?.customValues || [];

  return (
    <section className="mt-10">
      <Card className="px-6 py-5 w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-10 w-full">
          <div className=" gap-4">
            <h3 className="font-semibold text-20px text-blue-400">Job</h3>
            <div className="flex gap-4 grid grid-cols-2">
              <div className="space-y-1">
                <Label
                  htmlFor="industry"
                  className="text-neutral-500 font-medium"
                >
                  Industry
                </Label>
                <Select
                  value={selectedIndustry}
                  onValueChange={(value) => setSelectedIndustry(value)}
                >
                  <SelectTrigger className="">
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {sectorClass.map((sector: CustomValues) => (
                        <SelectItem key={sector.id} value={sector.value}>
                          {sector.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="">
                <Label
                  htmlFor="company size"
                  className="text-neutral-500 font-medium"
                >
                  Occupation
                </Label>
                <Select>
                  <SelectTrigger className="">
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {occupationClassification.map((sector: CustomValues) => (
                        <SelectItem key={sector.id} value={sector.value}>
                          {sector.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <div className="gap-4">
            <h3 className="font-semibold text-20px text-blue-400">Job</h3>
            <div className="flex gap-4 grid grid-cols-2 w-full">
              <div className="space-y-1">
                <Label
                  htmlFor="location"
                  className="text-neutral-500 font-medium"
                >
                  Location
                </Label>
                <Select
                  value={selectedLocation}
                  onValueChange={(value) => setSelectedLocation(value)}
                >
                  <SelectTrigger className="">
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {industryOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="">
                <Label
                  htmlFor="company size"
                  className="text-neutral-500 font-medium"
                >
                  Country
                </Label>
                <Select>
                  <SelectTrigger className="">
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {sectorClass.map((sector: CustomValues) => (
                        <SelectItem key={sector.id} value={sector.value}>
                          {sector.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <span className="text-16px font-light">Entry Level</span>
            <Switch.Root
              className="w-12 h-6 bg-primary-500 rounded-full relative"
              checked={entryLevel}
              onCheckedChange={setEntryLevel}
            >
              <Switch.Thumb className="block w-8 h-8 bg-primary-500 border rounded-full transform transition translate-y-[-4px]" />
            </Switch.Root>
          </div>

          <div className="flex gap-4">
            <Button
              onClick={resetFilters}
              variant="outline"
              className="px-5 py-2 transition"
            >
              <ResetFilterIcon fill="var(--buttonColor)" />
              Reset Filter
            </Button>
            <Button className="px-5 py-2 transition">Apply Filter</Button>
          </div>
        </div>
      </Card>

      <div className="mt-5">
        <div className="flex justify-start gap-4">
          {yearSections.map((choice) => (
            <Button
              key={choice}
              onClick={() =>
                handleChoiceClick(
                  choice as 'Quarter' | '1 Year' | '3 Years' | 'Custom'
                )
              }
              className={`px-4 py-2 rounded-full transition hover:bg-primary-500 hover:text-white ${isChecked[choice as 'Quarter' | '1 Year' | '3 Years' | 'Custom'] ? 'bg-primary-500 text-white' : 'bg-transparent text-gray-700'}`}
            >
              {choice}
            </Button>
          ))}
        </div>
      </div>

      {selectedTimeFrame && (
        <Card className="mt-6 w-full md:w-[60%] px-5 py-10 bg-white rounded-md">
          <div className="flex items-center gap-3">
            <h3 className="text-4xl font-semibold text-black">
              {jobCount.toLocaleString()}
            </h3>
            <div className="flex items-center gap-1">
              {growthPercentage >= 0 ? (
                <CaretUpGreenIcon />
              ) : (
                <CaretDownRedIcon />
              )}
              <span
                className={`text-lg font-medium ${growthPercentage >= 0 ? 'text-success-400' : 'text-destructive-400'}`}
              >
                {growthPercentage > 0
                  ? `+${growthPercentage}% growth`
                  : `${growthPercentage}% decline`}
              </span>
            </div>
          </div>
          <p className="mt-2 text-gray-600">
            Total new jobs posted for the selected filters in the past{' '}
            <span className="font-semibold">{selectedTimeFrame}</span>.
          </p>
        </Card>
      )}

      <div>
        <TopEmployers />
        <OccupationCard />
      </div>
    </section>
  );
}

export default Trends;
