'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import GoBackArrow from '../components/go-back-arrow';
import RedRocketIcon from '@/components/icons/RedRocketIcon';
import AiAssistantSuggestion from '../../../ui/AiAssistantSuggestion';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import {
  useGenerateGrowthStepsMutation,
  useGenerateOpportunityChecklistMutation,
} from '@/mutations/aiMutation';
import type {
  IGrowthOpportunityChecklist,
  IGrowthOpportunitySteps,
} from '@/types/aiTypes';
import { Loader } from '@/components/common/Loader';

interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
}

interface ChecklistSection {
  id: string;
  title: string;
  items: ChecklistItem[];
  isOpen: boolean;
}

export default function CareerRoadmap() {
  const selectedPersona = useSelectedPersona();

  const [careerRoadMap, setCareerRoadMap] =
    useState<IGrowthOpportunitySteps | null>(null);
  const [careerRoadMapChecklist, setCareerRoadMapChecklist] =
    useState<IGrowthOpportunityChecklist | null>(null);

  const targetRole = selectedPersona?.name || '';
  const currentRole = selectedPersona?.name || '';

  const {
    mutate: generateCareerRoadMap,
    isPending: isRoadMapPending,
    isError: isRoadMapError,
  } = useGenerateGrowthStepsMutation();

  const {
    mutate: generateOpportunityChecklist,
    isPending: isChecklistPending,
    isError: isChecklistError,
  } = useGenerateOpportunityChecklistMutation();

  const [checklistSections, setChecklistSections] = useState<
    ChecklistSection[]
  >([]);

  useEffect(() => {
    if (!careerRoadMap && !careerRoadMapChecklist && targetRole) {
      generateCareerRoadMap(targetRole, {
        onSuccess: (data: { data: IGrowthOpportunitySteps }) => {
          setCareerRoadMap(data?.data);
        },
        onError: (error) => {
          console.error('Failed to generate career roadmap:', error);
        },
      });
      generateOpportunityChecklist(targetRole, {
        onSuccess: (data: { data: IGrowthOpportunityChecklist }) => {
          setCareerRoadMapChecklist(data?.data);
        },
        onError: (error) => {
          console.error('Failed to generate opportunity checklist:', error);
        },
      });
    }
  }, [
    careerRoadMap,
    careerRoadMapChecklist,
    targetRole,
    generateCareerRoadMap,
    generateOpportunityChecklist,
  ]);

  useEffect(() => {
    if (careerRoadMapChecklist) {
      const transformedSections =
        careerRoadMapChecklist.data?.map((step, index) => ({
          id: `step-${index}`,
          title: `${index + 1}. ${step.title || `Step ${index + 1}`}`,
          isOpen: index === 0,
          items:
            step.items?.map((item, itemIndex) => ({
              id: `item-${index}-${itemIndex}`,
              text: item.text,
              completed: false,
            })) || [],
        })) || [];

      setChecklistSections(transformedSections);
    }
  }, [careerRoadMapChecklist]);

  const sectionsToDisplay =
    checklistSections.length > 0 ? checklistSections : [];

  const careerPathData = Array.isArray(careerRoadMap?.careerPath?.roles)
    ? careerRoadMap.careerPath?.roles
    : [];

  const toggleSection = (sectionId: string) => {
    setChecklistSections((sections) =>
      sections.map((section) =>
        section.id === sectionId
          ? { ...section, isOpen: !section.isOpen }
          : section
      )
    );
  };

  const toggleItem = (sectionId: string, itemId: string) => {
    setChecklistSections((sections) =>
      sections.map((section) =>
        section.id === sectionId
          ? {
            ...section,
            items: section.items.map((item) =>
              item.id === itemId
                ? { ...item, completed: !item.completed }
                : item
            ),
          }
          : section
      )
    );
  };

  const calculateProgress = () => {
    const totalItems = sectionsToDisplay.reduce(
      (acc, section) => acc + section.items.length,
      0
    );
    const completedItems = sectionsToDisplay.reduce(
      (acc, section) =>
        acc + section.items.filter((item) => item.completed).length,
      0
    );
    return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
  };

  const isPending = isRoadMapPending || isChecklistPending;
  const isError = isRoadMapError || isChecklistError;

  if (isPending || (!careerRoadMap && !careerRoadMapChecklist)) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
        <Loader />
        <p className="text-neutral-500 !text-white relative bottom-[20rem]">
          {isPending
            ? 'Generating your career roadmap...'
            : 'Loading your career roadmap...'}
        </p>
      </div>
    );
  }

  if (isError) {
    return (
      <Card className="mx-auto rounded-[20px]">
        <div className="border-b border-neutral-200 h-20 py-3 px-8 grid grid-cols-3 items-center">
          <GoBackArrow />
          <h1 className="text-[16px] text-center font-medium leading-[24px] text-primary-900">
            Career Roadmap
          </h1>
        </div>
        <CardContent className="p-8">
          <div className="text-center py-8">
            <p className="text-red-500">
              Failed to generate career roadmap. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mx-auto rounded-[20px]">
      <div className="border-b border-neutral-200 h-20 py-3 px-8 grid grid-cols-3 items-center">
        <GoBackArrow />
        <h1 className="w-full text-[16px] text-center font-medium leading-[24px] text-primary-900">
          Career Roadmap
        </h1>
      </div>

      <CardContent className="grid grid-cols-1 lg:grid-cols-3 gap-8 p-6">
        {/* Left Sidebar - Career Path */}
        <div className="lg:col-span-1">
          <Card className="">
            <h2 className="text-xl font-semibold text-[#111827] mb-2">
              Your career path as a {currentRole}
            </h2>
            <p className="text-sm text-[#646a77] mb-6">
              Estimated monthly salary progression:
            </p>

            <div className="space-y-4 mb-8">
              {careerPathData.map((roleItem, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div
                    className={`w-6 h-6 rounded-full border-2 flex items-center justify-center mt-0.5`}
                  >
                    {/* {roleItem.current && <div className="w-2 h-2 bg-white rounded-full" />} */}
                    {/* {!roleItem.current && index < careerPathData.findIndex((r) => r.current) && (
                      <div className="w-2 h-2 bg-[#d7dae0] rounded-full" />
                    )} */}
                  </div>
                  <div className="flex-1">
                    <p className={`font-medium`}>{roleItem.title}</p>
                    <p className="text-sm text-[#646a77]">{roleItem.salary}</p>
                  </div>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              className="w-full border-[#d7dae0] text-[#646a77]"
            >
              Change my career goal
            </Button>
          </Card>
        </div>

        <div className="lg:col-span-2 space-y-6">
          <Card className="p-4 border border-neutral-200 rounded-2xl">
            <CardContent className="p-0">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-6 h-6 rounded flex items-center">
                    <RedRocketIcon />
                  </div>
                </div>
                <div className=" items-center mx-auto">
                  <h3 className="font-medium !text-red-500 mb-1">
                    GROWTH OPPORTUNITY
                  </h3>
                  <p className="text-[16px] text-red-600">
                    {`You can be promoted to ${targetRole} within the next 12 months!`}
                  </p>
                </div>
              </div>
            </CardContent>
            <Button className="w-full md:hidden block mt-6">
              Find out how
            </Button>
          </Card>

          <div className="hidden md:block">
            <h2 className="text-xl font-semibold text-[#111827] mb-2">
              {`Steps to Become a ${targetRole} in 12 Months`}
            </h2>
            <p className="text-[#646a77] mb-6">
              {`Follow this roadmap to grow to ${targetRole}. Each step builds the skills and experience top employers look for.`}
            </p>

            <AiAssistantSuggestion />

            <div className="">
              <div className="flex flex-col justify-between mb-4">
                <h3 className="font-medium text-neutral-900 text-[18px]">
                  Your Growth Checklist
                </h3>
                <span className="text-[16px] text-neutral-500">
                  Progress: {calculateProgress()}%
                </span>
              </div>

              <Progress value={calculateProgress()} className="mb-6 h-2" />

              {sectionsToDisplay.length > 0 ? (
                <div className="space-y-4">
                  {sectionsToDisplay.map((section) => (
                    <Card key={section.id} className="border border-[#d7dae0]">
                      <Collapsible
                        open={section.isOpen}
                        onOpenChange={() => toggleSection(section.id)}
                      >
                        <CollapsibleTrigger asChild>
                          <div className="flex items-center justify-between p-4 cursor-pointer hover:bg-[#f3f5f7]">
                            <h4 className="font-medium text-[#111827]">
                              {section.title}
                            </h4>
                            {section.isOpen ? (
                              <ChevronUp className="h-5 w-5 text-[#646a77]" />
                            ) : (
                              <ChevronDown className="h-5 w-5 text-[#646a77]" />
                            )}
                          </div>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <div className="px-4 pb-4 space-y-3">
                            {section.items.map((item) => (
                              <div
                                key={item.id}
                                className="flex items-center space-x-3"
                              >
                                <Checkbox
                                  id={item.id}
                                  checked={item.completed}
                                  onCheckedChange={() =>
                                    toggleItem(section.id, item.id)
                                  }
                                  className="data-[state=checked]:!text-white  data-[state=checked]:bg-[#043c5b] data-[state=checked]:border-[#043c5b]"
                                />
                                <label
                                  htmlFor={item.id}
                                  className={`text-sm cursor-pointer ${item.completed
                                    ? 'text-[#646a77]'
                                    : 'text-[#111827]'
                                    }`}
                                >
                                  {item.text}
                                </label>
                              </div>
                            ))}
                          </div>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-neutral-500">
                    No checklist items available yet.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
