'use client';

import type React from 'react';
import { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { Job } from '@/types/jobTypes';
import { useJobStore } from '@/zustand/store/jobStore';

interface CareerSearchInputProps {
  setSelectedJob?: (_job: Job | null) => void
  onSearch?: () => void
}

const CareerSearchInput: React.FC<CareerSearchInputProps> = ({
  setSelectedJob,
  onSearch,
}) => {
  const { setSearchTerm, setLocation, fetchJobs, isLoading, status } =
    useJobStore();

  const [tempSearchTerm, setTempSearchTerm] = useState('');
  const [tempLocation, setTempLocation] = useState('');

  const handleApplyFilters = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (setSelectedJob) {
      setSelectedJob(null);
    }

    if (onSearch) {
      onSearch();
    }

    setSearchTerm(tempSearchTerm);
    setLocation(tempLocation);

    fetchJobs({
      page: 1,
      size: 10,
      search: tempSearchTerm,
      location: tempLocation,
      status,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleApplyFilters(e);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center my-10 w-full">
      <div className="flex flex-col sm:flex-row gap-3 w-full max-w-5xl px-4">
        <div className="flex-1 border border-neutral-200 rounded-full px-8 py-3 bg-white flex items-center gap-4">
          <Search className="w-5 h-5 text-neutral-400" />
          <input
            type="text"
            placeholder="Search by job title, company, or keywords..."
            className="flex-1 bg-transparent focus:outline-none"
            value={tempSearchTerm}
            onChange={(e) => setTempSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          <div className="h-5 border-l border-gray-300" />
          <MapPin className="w-5 h-5 text-neutral-400" />
          <input
            type="text"
            placeholder="Location"
            className="flex-1 bg-transparent focus:outline-none"
            value={tempLocation}
            onChange={(e) => setTempLocation(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleApplyFilters}
            className="rounded-full h-12"
            disabled={isLoading}
          >
            {isLoading ? 'Searching...' : 'Search Jobs'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CareerSearchInput;
