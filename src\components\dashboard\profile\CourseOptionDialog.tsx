'use client';
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { useUpdateEnrollmentComplete } from '@/queries';

interface Props {
  enrollment: any;
  onClose: () => void;
}

export default function CourseOptionsModal({ enrollment, onClose }: Props) {
  const [dialogType, setDialogType] = useState<'completed' | 'dropped' | null>(
    null
  );
  const [successOpen, setSuccessOpen] = useState(false);

  const isCompleted = dialogType === 'completed';

  const { mutate: updateEnrollmentStatus, status } =
    useUpdateEnrollmentComplete();
  const isLoading = status === 'pending';
  const handleConfirm = () => {
    if (isCompleted) {
      updateEnrollmentStatus(enrollment.courseDetails.id, {
        onSuccess: () => {
          setDialogType(null);
          setSuccessOpen(true);
        },
        onError: (error) => {
          console.error('Failed to mark as completed:', error);
          setDialogType(null);
        },
      });
    } else {
      setDialogType(null);
      setSuccessOpen(true);
    }
  };

  const closeSuccess = () => {
    setSuccessOpen(false);
    onClose();
  };

  return (
    <div className="relative flex justify-end">
      <div className="bg-white rounded-md mt-2 p-4 w-[200px] border border-neutral-200 shadow-md">
        <div className="flex flex-col gap-4">
          <Button
            className="text-left"
            variant="ghost"
            onClick={() => setDialogType('completed')}
          >
            Mark as Completed
          </Button>
          <Button
            className="text-left text-destructive-500"
            variant="ghost"
            onClick={() => setDialogType('dropped')}
          >
            Dropped Out
          </Button>
        </div>
      </div>

      {/* Confirm Dialog */}
      <Dialog
        open={dialogType !== null}
        onOpenChange={(open) => !open && setDialogType(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold text-neutral-900 leading-[24px] mb-4">
              {isCompleted ? 'Mark as Completed?' : 'Dropped Out of Course?'}
            </DialogTitle>
            <DialogDescription className="text-[18px] font-normal text-neutral-500 leading-[28px]">
              {isCompleted ? (
                "Marking this course as completed will update your Skills Profile with the skills you've learned."
              ) : (
                <div className="flex flex-col space-y-4">
                  <span> Are you sure you want to remove this course?</span>
                  <span>
                    {' '}
                    The skills that were extracted from this course will be
                    removed from your Skills Profile.
                  </span>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setDialogType(null)}>
              Cancel
            </Button>
            <Button
              variant={isCompleted ? 'default' : 'destructive'}
              onClick={handleConfirm}
              disabled={isLoading}
            >
              {isLoading
                ? isCompleted
                  ? 'Marking...'
                  : 'Dropping...'
                : isCompleted
                  ? 'Mark as Completed'
                  : 'Dropped Out'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog
        open={successOpen}
        onOpenChange={(open) => !open && closeSuccess()}
      >
        <DialogContent>
          <DialogHeader className="flex flex-row items-start gap-2">
            <span className="text-[28px]">🎉</span>
            <div>
              <DialogTitle className="text-[28px] font-semibold leading-[36px] text-neutral-900">
                Success!
              </DialogTitle>
              <DialogDescription>
                {isCompleted
                  ? 'You have successfully marked this course as completed.'
                  : 'You have successfully removed this course from your profile.'}
              </DialogDescription>
            </div>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button className="w-full" onClick={closeSuccess}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
