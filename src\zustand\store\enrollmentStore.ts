import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface StoredEnrollment {
  id: string;
  courseId: string;
  userId: string;
  userName: string;
  email: string;
  enrollmentDate: string;
  status: 'Enrolled' | 'Completed';
}

interface EnrollmentState {
  enrollments: Record<string, StoredEnrollment>;
  addEnrollment: (_enrollment: StoredEnrollment) => void;
  getEnrollment: (_courseId: string) => StoredEnrollment | undefined;
  updateEnrollmentStatus: (
    _enrollmentId: string,
    _status: 'Enrolled' | 'Completed'
  ) => void;
  clearEnrollments: () => void;
}

export const useEnrollmentStore = create<EnrollmentState>()(
  persist(
    (set, get) => ({
      enrollments: {},

      addEnrollment: (enrollment) =>
        set((state) => ({
          enrollments: {
            ...state.enrollments,
            [enrollment.courseId]: enrollment,
          },
        })),

      getEnrollment: (courseId) => {
        return get().enrollments[courseId];
      },

      updateEnrollmentStatus: (enrollmentId, status) =>
        set((state) => {
          const updatedEnrollments = { ...state.enrollments };
          Object.keys(updatedEnrollments).forEach((courseId) => {
            if (updatedEnrollments[courseId].id === enrollmentId) {
              updatedEnrollments[courseId] = {
                ...updatedEnrollments[courseId],
                status,
              };
            }
          });

          return { enrollments: updatedEnrollments };
        }),

      clearEnrollments: () => set({ enrollments: {} }),
    }),
    {
      name: 'course-enrollments-storage',
    }
  )
);
