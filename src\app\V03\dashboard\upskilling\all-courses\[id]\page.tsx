/* eslint-disable @typescript-eslint/no-non-null-assertion */
'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getCourseById } from '@/zustand/services/course.services';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import type { CourseRespones } from '@/types/courseType';
import { Skeleton } from '@/components/ui/skeleton';
import CourseDetailsContent from '@/components/dashboard/courses/course-details/CourseDetailsContent';
import SimilarCoursesTable from '@/components/dashboard/courses/course-details/SimilarCourse';
import { Loader } from '@/components/common/Loader';

export default function CourseDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [course, setCourse] = useState<CourseRespones | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCourse = async () => {
    try {
      setIsLoading(true);
      const data = await getCourseById(id);
      setCourse(data);
    } catch (err) {
      console.error('Error fetching course:', err);
      setError('Failed to load course details. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchCourse();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleBack = () => {
    router.back();
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
        <Button onClick={handleBack} variant="outline" className="mt-4">
          Back to Courses
        </Button>
      </div>
    );
  }

  if (!isLoading && !course) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>Course not found</p>
        <Button onClick={handleBack} variant="outline" className="mt-4">
          Back to Courses
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6 min-h-screen">
      <div className="mb-4">
        {isLoading ? (
          <Skeleton className="h-6 w-64 mt-10" />
        ) : (
          <DynamicBreadcrumb
            customBreadcrumbs={[
              {
                label: 'Upskilling',
                href: '/dashboard/upskilling/my-upskilling-journey',
              },
              {
                label: 'All Courses',
                href: '/dashboard/upskilling/all-courses',
              },
              {
                label: course && course.data ? course.data.name : '',
                href:
                  course && course.data
                    ? `/dashboard/upskilling/all-courses/${course.data.id}`
                    : '#',
              },
            ]}
          />
        )}
      </div>

      {isLoading ? (
        <Skeleton className="h-10 w-32 rounded-full" />
      ) : (
        <Button
          variant="outline"
          className="bg-transparent py-6 px-6 my-6"
          onClick={handleBack}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
      )}

      {isLoading ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <Loader />
        </div>
      ) : (
        <>
          <CourseDetailsContent course={course!.data} />
          <div className="w-[64%]">
            <SimilarCoursesTable currentCourse={course!.data} />
          </div>
        </>
      )}
    </div>
  );
}
