#!/bin/bash

# Docker build script for Career Navigator Pro UI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="career-navigator-ui"
TAG="latest"
BUILD_TYPE="production"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --tag TAG          Set image tag (default: latest)"
    echo "  -n, --name NAME        Set image name (default: career-navigator-ui)"
    echo "  -d, --dev              Build development image"
    echo "  -p, --production       Build production image (default)"
    echo "  -r, --run              Run container after building"
    echo "  -c, --compose          Use docker-compose instead of docker build"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                     # Build production image with default settings"
    echo "  $0 -d -r              # Build and run development image"
    echo "  $0 -t v1.0.0 -r       # Build production image with tag v1.0.0 and run"
    echo "  $0 -c                  # Use docker-compose to build and run"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -d|--dev)
            BUILD_TYPE="development"
            shift
            ;;
        -p|--production)
            BUILD_TYPE="production"
            shift
            ;;
        -r|--run)
            RUN_CONTAINER=true
            shift
            ;;
        -c|--compose)
            USE_COMPOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main build logic
main() {
    print_status "Starting Docker build process..."
    
    if [[ "$USE_COMPOSE" == "true" ]]; then
        print_status "Using Docker Compose..."
        
        if [[ "$BUILD_TYPE" == "development" ]]; then
            print_status "Building development environment..."
            docker-compose --profile dev up --build
        else
            print_status "Building production environment..."
            docker-compose up --build
        fi
    else
        # Determine Dockerfile
        if [[ "$BUILD_TYPE" == "development" ]]; then
            DOCKERFILE="Dockerfile.dev"
            FULL_IMAGE_NAME="${IMAGE_NAME}:dev-${TAG}"
        else
            DOCKERFILE="Dockerfile"
            FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
        fi
        
        print_status "Building $BUILD_TYPE image: $FULL_IMAGE_NAME"
        print_status "Using Dockerfile: $DOCKERFILE"
        
        # Build the image
        docker build -f "$DOCKERFILE" -t "$FULL_IMAGE_NAME" .
        
        if [[ $? -eq 0 ]]; then
            print_status "Build completed successfully!"
            print_status "Image: $FULL_IMAGE_NAME"
        else
            print_error "Build failed!"
            exit 1
        fi
        
        # Run container if requested
        if [[ "$RUN_CONTAINER" == "true" ]]; then
            print_status "Starting container..."
            
            # Stop and remove existing container if it exists
            docker stop "$IMAGE_NAME" 2>/dev/null || true
            docker rm "$IMAGE_NAME" 2>/dev/null || true
            
            # Run the container
            if [[ "$BUILD_TYPE" == "development" ]]; then
                docker run -d -p 3001:3000 --name "$IMAGE_NAME-dev" "$FULL_IMAGE_NAME"
                print_status "Development server running at http://localhost:3001"
            else
                docker run -d -p 3000:3000 --name "$IMAGE_NAME" "$FULL_IMAGE_NAME"
                print_status "Production server running at http://localhost:3000"
            fi
        fi
    fi
    
    print_status "Done!"
}

# Run main function
main
