/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  Course,
  GetProgramsListProps,
  ProgramDetailsResponse,
  ProgramsListResponse,
  SavedCourses,
  SaveUserProgramCoursesResponse,
  UserProgram,
} from '@/types';
import axiosClient from '@/utils/axiosClient';

const API_URL = '/Programbuilder';

class ProgramBuilderService {
  async getProgramByUniversityId(universityId: string): Promise<string[]> {
    const response = await axiosClient.get<string[]>(
      `${API_URL}/GetPrograms/${universityId}`
    );
    return response.data;
  }

  async getProgramInfoAndCoursesWithScore(
    programId: string,
    hubId: string
  ): Promise<any> {
    const response = await axiosClient.post<any>(
      `${API_URL}/GetProgramInfoAndCourseWithScore`,
      { programId, hubId }
    );
    return response.data;
  }

  async getProgramInfoAndCourses(
    programId: string
  ): Promise<ProgramDetailsResponse> {
    const response = await axiosClient.get<ProgramDetailsResponse>(
      `${API_URL}/getProgramInfoAndCourses/${programId}`
    );
    return response.data;
  }

  async getProgramId(programId: string): Promise<any> {
    const response = await axiosClient.get<any>(
      `${API_URL}/GetProgram/${programId}`
    );
    return response.data;
  }

  async getCoursePdf(data: any): Promise<Blob> {
    const response = await axiosClient.post<Blob>(
      `${API_URL}/GetCoursePdf`,
      data,
      {
        responseType: 'blob',
      }
    );
    return response.data;
  }

  async getUniversitiesLookup() {
    const response = await axiosClient.get(`${API_URL}/GetUniversitiesLookup`);
    return response.data;
  }

  async getUniversityPrograms() {
    const response = await axiosClient.get(`${API_URL}/GetUniversityPrograms`);
    return response.data;
  }

  async getUniversityList() {
    const response = await axiosClient.get(`${API_URL}/GetUniversityList`);
    return response.data;
  }

  async getProgramDegrees() {
    const response = await axiosClient.post(`${API_URL}/GetProgramDegrees`);
    return response.data;
  }

  async getProgramLevels() {
    const response = await axiosClient.post(`${API_URL}/GetProgramLevels`);
    return response.data;
  }

  async getProgramsByUniversity(
    universityId: string,
    faculties?: string[]
  ): Promise<any> {
    const response = await axiosClient.post<any>(
      `${API_URL}/GetProgramsByUniversity`,
      { universityId, faculties }
    );
    return response.data;
  }

  async getUserCourses(): Promise<{ course: Course }[]> {
    const response = await axiosClient.get<{ course: Course }[]>(
      `${API_URL}/GetUserCourses`
    );
    return response.data;
  }

  async getUserPrograms(data: any): Promise<UserProgram> {
    const response = await axiosClient.get<UserProgram>(
      `${API_URL}/GetUserPrograms`,
      data
    );
    return response.data;
  }

  async getProgramsWithPagination(
    params: GetProgramsListProps
  ): Promise<ProgramsListResponse> {
    const response = await axiosClient.post<ProgramsListResponse>(
      `${API_URL}/GetProgramsWithPagination`,
      params
    );
    return response.data;
  }

  async saveUserProgramCourses(
    data: SavedCourses
  ): Promise<SaveUserProgramCoursesResponse[]> {
    const response = await axiosClient.post<SaveUserProgramCoursesResponse[]>(
      `${API_URL}/SaveUserProgramCourses`,
      data
    );
    return response.data;
  }

  async getDepartmentsByUniversityIdV2(
    universityId: string
  ): Promise<Array<Record<'departmentName', string>>> {
    const response = await axiosClient.get<
      Array<Record<'departmentName', string>>
    >(`${API_URL}/GetDepartmentsByUniversity/${universityId}`);
    return response.data;
  }

  async deleteUserProgramCourses(data: {
    programId: string;
    courseIds: string[];
  }) {
    const response = await axiosClient.delete(
      `${API_URL}/DeleteUserProgramCourses`,
      {
        data,
      }
    );
    return response.data;
  }

  async requestProgramsForUniversity(data: {
    universityId: string;
    otherUniversityName?: string;
    programName: string;
    programUrl: string;
  }) {
    const response = await axiosClient.post(
      `${API_URL}/RequestProgramsForUniversity`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  }
}

const programBuilderService = new ProgramBuilderService();
export default programBuilderService;
