import Image from 'next/image';
import BriefCaseIcon from '@/assets/images/ws/3d-jobs.svg';
import RocketIcon from '@/assets/images/ws/3d-rocket.svg';
import RoadMapIcon from '@/assets/images/ws/3d-roadmap.svg';
import type { ReactNode } from 'react';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';

export interface CareerCardData {
  id: string;
  icon: ReactNode;
  iconLabel: string;
  title: string;
  description: string;
  progress?: {
    current: number;
    total: number;
    percentage: number;
    label?: string;
  };
  actionButton?: {
    text: string;
    variant: 'default' | 'outline' | 'secondary';
  };
  onClick?: string;
}

export const useCareerData = (): CareerCardData[] => {
  const selectedPersona = useSelectedPersona();
  const role = selectedPersona?.name || 'Data Analyst';

  return [
    {
      id: 'job-matches',
      icon: (
        <Image
          src={BriefCaseIcon || '/placeholder.svg'}
          alt="Briefcase"
          className="w-16 h-16"
        />
      ),
      iconLabel: 'briefcase',
      title: 'Apply for top job matches',
      description: '',
      progress: {
        current: 1,
        total: 3,
        percentage: 33,
        label: 'daily suggested jobs reviewed',
      },
      onClick: '/V03/dashboard/top-job-matches',
    },
    {
      id: 'skills',
      icon: (
        <Image
          src={RocketIcon || '/placeholder.svg'}
          alt="Skills"
          className="w-16 h-16"
        />
      ),
      iconLabel: 'skills',
      title: 'Top 3 skills to learn now',
      description: `Boost your career profile as a ${role} with these essential skills.`,
      onClick: '/V03/dashboard/upskilling/recommendations',
    },
    {
      id: 'career-roadmap',
      icon: (
        <Image
          src={RoadMapIcon || '/placeholder.svg'}
          alt="Career Roadmap"
          className="w-16 h-16"
        />
      ),
      iconLabel: 'map',
      title: 'Explore your career roadmap',
      description: `Understand your strengths and career path as a ${role}.`,
      onClick: '/V03/dashboard/upskilling/career-roadmap',
    },
  ];
};
