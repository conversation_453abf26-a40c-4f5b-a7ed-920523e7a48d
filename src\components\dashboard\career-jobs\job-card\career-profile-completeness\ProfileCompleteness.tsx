/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import CareerTestStep from './profile-steps/CareerTestStep';
import UploadCVStep from './profile-steps/UploadCvStep';
import EducationProgramStep from './profile-steps/EducationProgramStep';
import {
  useAssessmentScores,
  useCvs,
  useGetEnrollmentById,
  useGetUserPrograms,
} from '@/queries';
import ReviewStep from './profile-steps/reviewTestStep';

export interface ProfileStep {
  id: string;
  title: string;
  description: string;
  canSkip: boolean;
  completed: boolean;
}

interface ProfileCompletionProcessProps {
  onComplete: () => void;
  className?: string;
  showSearchInput?: boolean;
  showReviewStep?: boolean;
  showCareerTestStep?: boolean;
  showUploadCvStep?: boolean;
  showEducationProgramStep?: boolean;
}

const INITIAL_PERCENTAGE = 57;
const STEP_INCREMENT = 14;

const ProfileCompletionProcess: React.FC<ProfileCompletionProcessProps> = ({
  onComplete,
  className = '',
  showReviewStep = true,
  showCareerTestStep = true,
  showUploadCvStep = true,
  showEducationProgramStep = true,
}) => {
  const { user } = useAuthStore();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completionPercentage, setCompletionPercentage] =
    useState(INITIAL_PERCENTAGE);
  const [profileSteps, setProfileSteps] = useState<ProfileStep[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showCVModal, setShowCVModal] = useState(false);
  const [allStepsCompleted, setAllStepsCompleted] = useState(false);

  const { data: assessmentScores } = useAssessmentScores();
  const { data: programsResponse } = useGetUserPrograms();
  const { data: myCourses } = useGetEnrollmentById(user?.id as string);

  const isTestCompleted = assessmentScores && assessmentScores.length > 0;
  const isStudyPathComplete =
    programsResponse?.data && programsResponse.data.length > 0;

  const { data: myCvs } = useCvs(user?.id || '');
  const hasCV = myCvs?.data && myCvs.data.length > 0;

  const isReviewCompleted =
    myCourses?.data && myCourses.data.length > 0 && hasCV;

  useEffect(() => {
    if (!user) return;

    const allSteps = [
      {
        id: 'career-test',
        title: 'Take a career test',
        description:
          'Discover your unique strengths and preferences to connect with roles that truly suit you.',
        canSkip: true,
        completed: isTestCompleted,
        enabled: showCareerTestStep,
      },
      {
        id: 'upload-cv',
        title: 'Upload your CV',
        description:
          'Extract key skills to receive smarter and more accurate job recommendations. This step cannot be skipped.',
        canSkip: false,
        completed: hasCV,
        enabled: showUploadCvStep,
      },
      {
        id: 'education-program',
        title: 'Build your education program',
        description:
          'Customize your education to match your career goals and upskilling aspirations.',
        canSkip: true,
        completed: isStudyPathComplete,
        enabled: showEducationProgramStep,
      },
      {
        id: 'review',
        title: 'Review your career profile',
        description:
          'Tailor your education to match your career goals and upskilling aspirations. Add your degrees and electives to receive personalized job recommendations and career development opportunities. This step cannot be skipped.',
        canSkip: false,
        completed: isReviewCompleted,
        enabled: showReviewStep,
      },
    ];

    const enabledSteps = allSteps.filter((step) => step.enabled);

    const allEnabledStepsCompleted = enabledSteps.every(
      (step) => step.completed
    );

    if (allEnabledStepsCompleted) {
      setAllStepsCompleted(true);
      setCompletionPercentage(100);
      setProfileSteps([]);
      onComplete();
      return;
    }

    const incompleteEnabledSteps = enabledSteps.filter(
      (step) => !step.completed
    );
    const stepsWithoutEnabled = incompleteEnabledSteps.map(
      ({ enabled, ...step }) => step
    );

    setProfileSteps(stepsWithoutEnabled);
    setIsInitialized(true);

    const initialStep = stepsWithoutEnabled.length > 0 ? 0 : -1;
    setCurrentStepIndex(initialStep);
    updateCompletionPercentage(initialStep, stepsWithoutEnabled);
  }, [
    user,
    isTestCompleted,
    hasCV,
    isStudyPathComplete,
    isReviewCompleted,
    showReviewStep,
    showCareerTestStep,
    showUploadCvStep,
    showEducationProgramStep,
  ]);

  const updateCompletionPercentage = (
    currentStep: number,
    steps: ProfileStep[]
  ) => {
    const completedCount =
      (isTestCompleted ? 1 : 0) +
      (hasCV ? 1 : 0) +
      (isStudyPathComplete ? 1 : 0) +
      (isReviewCompleted ? 1 : 0);

    if (steps[currentStep]?.id === 'review') {
      setCompletionPercentage(85);
      return;
    }

    const percentage = INITIAL_PERCENTAGE + completedCount * STEP_INCREMENT;
    const finalPercentage = Math.min(percentage, 85);
    setCompletionPercentage(finalPercentage);
  };

  useEffect(() => {
    if (profileSteps.length === 0) return;

    updateCompletionPercentage(currentStepIndex, profileSteps);

    const allEnabledStepsCompleted =
      (!showCareerTestStep || isTestCompleted) &&
      (!showUploadCvStep || hasCV) &&
      (!showEducationProgramStep || isStudyPathComplete) &&
      (!showReviewStep || isReviewCompleted);

    if (allEnabledStepsCompleted || allStepsCompleted) {
      setCompletionPercentage(100);
      setAllStepsCompleted(true);
      onComplete();
    }
  }, [
    profileSteps,
    currentStepIndex,
    onComplete,
    isTestCompleted,
    hasCV,
    isStudyPathComplete,
    isReviewCompleted,
    showReviewStep,
    showCareerTestStep,
    showUploadCvStep,
    showEducationProgramStep,
    allStepsCompleted,
  ]);

  const handleCompleteStep = (index: number) => {
    const currentStep = profileSteps[index];

    if (currentStep.id === 'review') {
      setAllStepsCompleted(true);
      setCompletionPercentage(100);
      window.location.href = '/dashboard/career-profile';
      return;
    }

    setProfileSteps((prev) => {
      const updated = [...prev];
      updated[index].completed = true;
      return updated;
    });

    if (currentStep.id === 'education-program') {
      window.location.href = '/dashboard/career-guidance/program-builder';
      return;
    } else if (currentStep.id === 'career-test') {
      window.location.href = '/dashboard/career-guidance/career-test';
      return;
    }

    if (index < profileSteps.length - 1) {
      setCurrentStepIndex(index + 1);
    } else {
      onComplete();
    }
  };

  const handleSkipStep = (index: number) => {
    if (index < profileSteps.length - 1) {
      setCurrentStepIndex(index + 1);
    } else {
      onComplete();
    }
  };

  const currentStep = profileSteps[currentStepIndex];
  const remainingSteps = profileSteps.filter((step) => !step.completed).length;

  const renderCurrentStep = () => {
    if (!currentStep || currentStep.completed) return null;

    const stepIndex = profileSteps.findIndex(
      (step) => step.id === currentStep.id
    );

    switch (currentStep.id) {
      case 'career-test':
        return (
          <CareerTestStep
            step={currentStep}
            onComplete={() => handleCompleteStep(stepIndex)}
            onSkip={() => handleSkipStep(stepIndex)}
          />
        );
      case 'upload-cv':
        return (
          <UploadCVStep
            step={currentStep}
            onComplete={() => handleCompleteStep(stepIndex)}
            showModal={showCVModal}
            setShowModal={setShowCVModal}
          />
        );
      case 'education-program':
        return (
          <EducationProgramStep
            step={currentStep}
            onComplete={() => handleCompleteStep(stepIndex)}
            onSkip={() => handleSkipStep(stepIndex)}
          />
        );
      case 'review':
        return (
          <ReviewStep
            step={currentStep}
            onComplete={() => handleCompleteStep(stepIndex)}
          />
        );
      default:
        return null;
    }
  };

  const getTitle = () => {
    if (currentStep?.id === 'review') {
      return 'Review your profile';
    }
    if (isTestCompleted && hasCV && isStudyPathComplete) {
      return 'Complete your career profile';
    }
    if (isTestCompleted && hasCV) {
      return 'Complete your education profile';
    }
    if (isTestCompleted) {
      return 'Complete your profile';
    }
    return 'Your Career Profile is almost complete!';
  };

  if (allStepsCompleted || completionPercentage === 100) {
    return null;
  }

  return (
    <Card
      className={`mb-8 border border-primary-100 shadow-sm mt-4 rounded-lg ${className}`}
    >
      <CardContent className="p-6">
        <h2 className="text-[20px] font-semibold text-neutral-900 mb-2">
          {getTitle()}
        </h2>
        <p className="text-neutral-600 mb-4 text-[18px] font-normal leading-[28px]">
          {`You are just ${remainingSteps} ${remainingSteps === 1 ? 'step' : 'steps'} away from unlocking your personalized job recommendations, expert guidance, and a tailored upskilling roadmap.`}
        </p>

        <div className="mb-6">
          <Progress value={completionPercentage} className="h-2" />
          <div className="flex justify-end mt-1">
            <span className="text-[16px] text-neutral-500">
              {completionPercentage}%
            </span>
          </div>
        </div>

        {renderCurrentStep()}
      </CardContent>
    </Card>
  );
};

export default ProfileCompletionProcess;
