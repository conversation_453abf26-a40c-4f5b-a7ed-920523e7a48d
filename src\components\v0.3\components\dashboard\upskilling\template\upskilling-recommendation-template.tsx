'use client';

import { Card, CardContent } from '@/components/ui/card';
import SkillRecommendationCard from '../components/skill-recommendation-card';
import Image from 'next/image';
import GoBackArrow from '../components/go-back-arrow';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useQuery } from '@tanstack/react-query';
import aiService from '@/services/ai';
import type { ISkillsRecommendation } from '@/types/aiTypes';
import { Loader } from '@/components/common/Loader';

function UpskillingRecommendationTemplate() {
  const selectedPersona = useSelectedPersona();
  const role = selectedPersona?.name || 'Data Analyst';

  // Use React Query for automatic deduplication and caching
  const {
    data: skillsData,
    isLoading: isSkillsPending,
    isError: isSkillsError,
  } = useQuery({
    queryKey: ['skillRecommendations', role],
    queryFn: () => aiService.generateSkillRecommendation(role),
    enabled: !!role,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Extract data from query response
  const topSkills = skillsData?.data || null;
  const skillsArray = Array.isArray(topSkills?.skills) ? topSkills.skills : [];
  const topThreeSkills = skillsArray.slice(0, 3);

  if (isSkillsPending || !topSkills) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
        <Loader />
        <p className="text-neutral-500 !text-white relative bottom-[20rem]">
          {isSkillsPending
            ? 'Generating your top 3 skills...'
            : 'Loading your top 3 skills...'}
        </p>
      </div>
    );
  }

  if (isSkillsError) {
    return (
      <Card className="bg-white border border-neutral-200 w-full h-fit rounded-[20px]">
        <div className="border-b border-neutral-200 h-20 py-3 px-8 grid grid-cols-3 items-center">
          <GoBackArrow />
          <h1 className="text-[16px] font-medium leading-[24px] text-neutral-900 text-center">
            Skills Recommendations
          </h1>
        </div>
        <CardContent className="p-8">
          <div className="text-center py-8">
            <p className="text-red-500">
              Failed to generate skill recommendations. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white border border-neutral-200 w-full h-fit rounded-[20px]">
      <div className="border-b border-neutral-200 h-20 py-3 px-8 grid grid-cols-3 items-center">
        <GoBackArrow />
        <h1 className="text-[16px] font-medium leading-[24px] text-neutral-900 text-center">
          Skills Recommendations
        </h1>
      </div>
      <CardContent className="p-8 space-y-8">
        <div className="flex items-center justify-between">
          <h1
            className="text-[24px] font-semibold leading-[32px]"
            style={{ color: 'var(--headingTextColor)' }}
          >
            Top 3 skills to help you step into a {role} role
          </h1>
          <Image
            src={'/images/icons/image.svg'}
            height={48}
            width={48}
            alt="rocket emoji"
          />
        </div>

        {topThreeSkills.length > 0 ? (
          <div className="space-y-5">
            {topThreeSkills.map((skill: any, index: number) => (
              <SkillRecommendationCard
                key={`skill-${index}`}
                title={skill.title}
                badgeTitle={index === 0 ? 'Best pick' : undefined}
                description={skill.whyLearn}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-neutral-500">
              No skill recommendations available.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default UpskillingRecommendationTemplate;
