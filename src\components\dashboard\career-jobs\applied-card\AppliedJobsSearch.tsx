'use client';

import React, { useState, useEffect } from 'react';
import { Search, MapPin } from 'lucide-react';
import type { AppliedJob } from '@/types/jobTypes';
import { useAppliedJobStore } from '@/zustand/store/appliedStore';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { Button } from '@/components/ui/button';
import ResetIcon from '@/components/icons/ResetSvg';

interface AppliedSearchInputProps {
  onSearch: (_filteredJobs: AppliedJob[]) => void
}

const AppliedSearchInput: React.FC<AppliedSearchInputProps> = ({
  onSearch,
}) => {
  const {
    searchTerm,
    location,
    jobType,
    monthlyFrom,
    setSearchTerm,
    setLocation,
    resetSearch,
    fetchJobs,
    jobs: storeJobs,
  } = useAppliedJobStore();

  const { user } = useAuthStore();
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (storeJobs.length > 0) {
      ((jobsToFilter: AppliedJob[]) => {
        const filtered = jobsToFilter.filter((job) => {
          // Check title/company match
          const titleMatch = !searchTerm ||
            job.job.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            job.job.companyName?.toLowerCase().includes(searchTerm.toLowerCase());

          // Check location match
          const locationMatch = !location ||
            job.job.location?.toLowerCase().includes(location.toLowerCase());

          // Check job type match
          const jobTypeMatch = !jobType || job.job.jobType?.toLowerCase() === jobType.toLowerCase();

          return titleMatch && locationMatch && jobTypeMatch;
        });

        onSearch(filtered.length > 0 ? filtered : jobsToFilter);
      })(storeJobs);
    }
  }, [searchTerm, location, jobType, monthlyFrom, storeJobs, onSearch, (jobsToFilter: AppliedJob[]) => {
    const filtered = jobsToFilter.filter((job) => {
      // Check title/company match
      const titleMatch = !searchTerm ||
        job.job.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.job.companyName?.toLowerCase().includes(searchTerm.toLowerCase());

      // Check location match
      const locationMatch = !location ||
        job.job.location?.toLowerCase().includes(location.toLowerCase());

      // Check job type match
      const jobTypeMatch = !jobType || job.job.jobType?.toLowerCase() === jobType.toLowerCase();

      return titleMatch && locationMatch && jobTypeMatch;
    });

    onSearch(filtered.length > 0 ? filtered : jobsToFilter);
  }]);

  const handleSearchClick = async () => {
    if (!user?.id) return;

    setIsSearching(true);
    try {
      await fetchJobs({
        userId: user.id,
        page: 1,
        size: 10,
        search: searchTerm,
        location,
        jobType,
        monthlyFrom,
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleReset = async () => {
    if (!user?.id) return;

    resetSearch();
    setIsSearching(true);
    try {
      await fetchJobs({
        userId: user.id,
        page: 1,
        size: 10,
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearchClick();
    }
  };

  return (
    <div className="mx-auto justify-center items-center w-full mt-10">
      <div className="flex gap-2 items-center w-full">
        <div className="relative rounded-full border border-neutral-200 bg-white p-[2px] flex-1">
          <div className="flex items-center w-full rounded-full bg-white px-4 py-2">
            <div className="flex items-center w-3/5">
              <Search className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
                placeholder="Search by job title, company, or keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <span className="px-2 text-neutral-400">|</span>
            <div className="flex items-center w-2/5">
              <MapPin className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
                placeholder="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
          </div>
        </div>

        <Button
          onClick={handleReset}
          variant="outline"
          className="h-[56px] px-6"
          disabled={isSearching}
        >
          <ResetIcon className="text-[--buttonColor]" />
          <span className="hidden sm:inline ml-1 !text-[--buttonColor]">
            Reset
          </span>
        </Button>

        <Button
          onClick={handleSearchClick}
          disabled={isSearching}
          className="h-[56px] px-6"
        >
          {isSearching ? 'Searching...' : 'Search Applied Jobs'}
        </Button>
      </div>
    </div>
  );
};

export default AppliedSearchInput;
