/* eslint-disable no-unused-vars */
'use client';

import type React from 'react';
import { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ResetIcon from '@/components/icons/ResetSvg';

interface AppliedSearchInputProps {
  onSearch: (params: {
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: number;
  }) => void;
  isLoading?: boolean;
}

const AppliedSearchInput: React.FC<AppliedSearchInputProps> = ({
  onSearch,
  isLoading = false,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [location, setLocation] = useState('');

  const handleSearchClick = () => {
    onSearch({
      search: searchTerm.trim() || undefined,
      location: location.trim() || undefined,
    });
  };

  const handleReset = () => {
    setSearchTerm('');
    setLocation('');
    onSearch({});
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearchClick();
    }
  };

  return (
    <div className="mx-auto justify-center items-center w-full mt-10">
      <div className="flex gap-2 items-center w-full">
        <div className="relative rounded-full border border-neutral-200 bg-white p-[2px] flex-1">
          <div className="flex items-center w-full rounded-full bg-white px-4 py-2">
            <div className="flex items-center w-3/5">
              <Search className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none bg-transparent"
                placeholder="Search by job title, company, or keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isLoading}
              />
            </div>
            <span className="px-2 text-neutral-400">|</span>
            <div className="flex items-center w-2/5">
              <MapPin className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none bg-transparent"
                placeholder="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isLoading}
              />
            </div>
          </div>
        </div>

        <Button
          onClick={handleReset}
          variant="outline"
          className="h-[56px] px-6"
          disabled={isLoading}
        >
          <ResetIcon className="text-[--buttonColor]" />
          <span className="hidden sm:inline ml-1 !text-[--buttonColor]">
            Reset
          </span>
        </Button>

        <Button
          onClick={handleSearchClick}
          disabled={isLoading}
          className="h-[56px] px-6"
        >
          {isLoading ? 'Searching...' : 'Search Applied Jobs'}
        </Button>
      </div>
    </div>
  );
};

export default AppliedSearchInput;
