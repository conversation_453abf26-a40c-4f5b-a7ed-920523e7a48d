'use client';
import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useApplicationStore } from '@/zustand/store/userActionStore';
import { useCvs } from '@/queries';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useUploadCvCustomMutation } from '@/mutations';
import { formatDate } from '@/utils';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import DeleteCvModal from './DeleteCvModal';
import Image from 'next/image';
import { EyeIcon } from '@/assets/images/dashboard';
import { Upload } from 'lucide-react';
import BounceLoader from '@/components/common/BounceLoader';

interface UploadStepProps {
  showCvError?: boolean;
}

export default function UploadStep({ showCvError }: UploadStepProps) {
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { setSelectedDocumentId, selectedDocumentId } = useApplicationStore(
    (state) => state
  );

  const userId = useAuthStore((state) => state.user?.id);

  const { data: myCvs, isLoading, refetch } = useCvs(userId as string);
  const { uploadCv, isLoading: isCvUploading } =
    useUploadCvCustomMutation(refetch);
  const { downloadFile } = useDownloadFile();

  useEffect(() => {
    if (myCvs?.data?.length > 0) {
      const currentSelectionExists = myCvs.data.some(
        (cv: { id: string }) => cv.id === selectedDocumentId
      );
      if (!selectedDocumentId || !currentSelectionExists) {
        setSelectedDocumentId(myCvs.data[0].id);
      }
    }
  }, [myCvs, selectedDocumentId, setSelectedDocumentId]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    setError(null);
    setIsUploading(true);

    if (e.target.files && e.target.files.length > 0) {
      if (myCvs?.data?.length >= 4) {
        setError(
          'You can only upload up to 4 files. Please delete an existing file first.'
        );
        setIsUploading(false);
        return;
      }

      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ];
      const selectedFiles = Array.from(e.target.files);

      const validFiles = selectedFiles.filter((file) =>
        allowedTypes.includes(file.type)
      );
      if (validFiles.length !== selectedFiles.length) {
        setError('Only .pdf and .docx files are allowed.');
        setIsUploading(false);
        return;
      }
      try {
        const newFiles = await Promise.all(
          validFiles.map(async (file) => ({
            id: crypto.randomUUID(),
            name: file.name,
            size: file.size,
            date: new Date(),
            selected: false,
            content: await readFileAsBase64(file),
          }))
        );
        uploadCv({
          cv: {
            FileName: newFiles[0].name,
            Base64Content: newFiles[0].content,
          },
          userId: userId as string,
        });
        refetch();
      } catch (error) {
        console.error('Error uploading file:', error);
        setError(
          'An error occurred while uploading the file. Please try again.'
        );
      }
    }

    setIsUploading(false);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const selectFile = (id: string) => {
    setSelectedDocumentId(id);
  };

  return (
    <div className="bg-white pt-6 mb-6 text-left px-6">
      <div className="space-y-3 mb-3">
        {isLoading || isUploading || (isCvUploading && <BounceLoader />)}
        {myCvs?.data?.length === 0 && (
          <div className="border rounded-lg p-4 border-neutral-200 text-center">
            <p className="text-neutral-500">No CVs uploaded yet</p>
          </div>
        )}
        {myCvs?.data &&
          myCvs?.data?.map(
            (file: {
              fileName: string;
              base64Content: string;
              createdOn: string;
              id: string;
            }) => (
              <button
                key={file.id}
                className={`border w-full rounded-lg p-4 flex items-center ${
                  file.id === selectedDocumentId
                    ? 'border-2 border-primary-500'
                    : 'border-neutral-200'
                }`}
                onClick={() => selectFile(file.id)}
              >
                <div className="mr-3">
                  <div
                    className={`w-6 h-6 rounded-full border ${
                      file.id === selectedDocumentId
                        ? 'border-primary-500 bg-white'
                        : 'border-neutral-200'
                    } flex items-center justify-center`}
                  >
                    {file.id === selectedDocumentId && (
                      <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                    )}
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-[16px] text-left text-neutral-900 font-semibold leading-[24px] ">
                    {file.fileName}
                  </p>
                  <p className="text-[14px] text-left text-neutral-500">
                    {formatDate(file.createdOn)}
                  </p>
                </div>
                <div className="flex space-x-2 items-center">
                  <Button
                    variant={'ghost'}
                    className="px-0 w-[40px] h-[40px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadFile(file.base64Content, file.fileName);
                    }}
                  >
                    <Image
                      src={EyeIcon}
                      width={10}
                      height={10}
                      alt="trashIcon"
                      className="w-10 h-10"
                    />
                  </Button>
                  <div className=" bg-destructive-50 rounded-md">
                    <DeleteCvModal id={file.id} />
                  </div>
                </div>
              </button>
            )
          )}
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileUpload}
        className="hidden"
        multiple
        accept=".pdf,.docx"
      />

      {showCvError && (
        <div className="mb-4 p-3 bg-red-50 text-destructive-500 rounded-md text-[16px]">
          Please select a CV to continue
        </div>
      )}

      <Button
        className="flex items-center gap-2"
        onClick={() => fileInputRef.current?.click()}
        disabled={myCvs?.data?.length >= 4 || isUploading}
      >
        {isUploading ? (
          'Uploading...'
        ) : (
          <>
            <Upload size={18} />
            Upload a new CV
          </>
        )}
      </Button>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-destructive-500 rounded-md text-[16px]">
          {error}
        </div>
      )}

      {myCvs?.data?.length >= 4 && (
        <p className="mt-2 text-[16px] text-destructive-500">
          Maximum file limit reached (4/4). Delete a file to upload a new one.
        </p>
      )}
    </div>
  );
}
