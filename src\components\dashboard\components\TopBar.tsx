'use client';

import React from 'react';

import { useRouter } from 'next/navigation';
import Logo from '@/constants/CAREER_LOGO';
import Image from 'next/image';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';

interface TopBarProps {
  toggleActionsAlert?: () => void;
}

const TopBar: React.FC<TopBarProps> = () => {
  const { appearanceSettings } = useSettingsStore();

  const router = useRouter();

  return (
    <div className="bg-[--topBarBgColor] flex flex-col text-white px-6 py-6 h-[98px] shadow  z-10">
      <div className="flex justify-between items-center">
        <div className=" items-center space-x-2">
          <Logo logoColor="white" className="text-white" />
        </div>
        <div className="">
          <Image
            src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
            width={100}
            height={100}
            className="w-auto h-[70px] cursor-pointer"
            alt="bubbleIcon"
            onClick={() => router.push('/dashboard/contact-support')}
          />
        </div>
      </div>
    </div>
  );
};

export default TopBar;
