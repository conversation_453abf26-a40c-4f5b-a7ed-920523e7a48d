/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
'use client';

import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Logo from '@/constants/CAREER_LOGO';
import Image from 'next/image';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';
import { Switch } from '@/components/ui/switch';
import { Loader2, Menu } from 'lucide-react';
import HamburgerIcon from '@/components/icons/HamburgerIcon';
import WhiteShieldLogoIcon from '@/components/icons/WhiteShieldLogoIcon';
import ProfileIcon from '@/components/icons/ProfileSvg';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { InboxIcon } from '@/components/icons/InboxSvg';

interface TopBarProps {
  toggleActionsAlert?: () => void;
  isVersionFlag?: boolean;
  onToggleVersionFlag?: (checked: boolean) => void;
  toggleMobileNav?: () => void;
}

const TopBar: React.FC<TopBarProps> = ({
  toggleActionsAlert,
  isVersionFlag = false,
  onToggleVersionFlag,
  toggleMobileNav,
}) => {
  const { appearanceSettings } = useSettingsStore();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const logout = useAuthStore((state) => state.logout);
  const [isAccountDropdownOpen, setIsAccountDropdownOpen] = useState(false);
  const [openDropdownMenu, setOpenDropdownMenu] = useState<string | null>(null);

  const accountDropdownRef = useRef<HTMLDivElement>(null);
  const menuDropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const toggleAccountDropdown = () => {
    setIsAccountDropdownOpen((prev) => !prev);
  };

  const toggleMenuDropdown = (label: string) => {
    setOpenDropdownMenu((prev) => (prev === label ? null : label));
  };

  const handleProfile = () => {
    router.push('/dashboard/profile');
    setIsAccountDropdownOpen(false);
  };

  const handleCareerProfile = () => {
    router.push('/dashboard/career-profile');
    setIsAccountDropdownOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        accountDropdownRef.current &&
        !accountDropdownRef.current.contains(event.target as Node)
      ) {
        setIsAccountDropdownOpen(false);
      }
      let clickedOutsideAllMenus = true;
      Object.values(menuDropdownRefs.current).forEach((ref) => {
        if (ref && ref.contains(event.target as Node)) {
          clickedOutsideAllMenus = false;
        }
      });

      if (clickedOutsideAllMenus) {
        setOpenDropdownMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setIsAccountDropdownOpen(false);
    setOpenDropdownMenu(null);
  }, [pathname]);

  const isBgWhite = ['#ffffff', '#fff', '#FFF', '#FFFFFF'].includes(
    appearanceSettings.navMenuColor
  );
  const user = useAuthStore((state) => state.user);

  const handleSwitchChange = async (checked: boolean) => {
    setIsLoading(true);

    try {
      if (onToggleVersionFlag) {
        onToggleVersionFlag(checked);
      }
      await new Promise((resolve) => setTimeout(resolve, 800));

      if (checked) {
        router.push('/V03/dashboard');
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error during navigation:', error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  return (
    <div className="bg-[--topBarBgColor] flex flex-col text-white px-6 py-6 h-[98px] shadow z-10">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <button
            className="lg:hidden p-2 bg-white rounded-full h-10 w-10 items-center"
            onClick={toggleMobileNav}
          >
            <HamburgerIcon />
          </button>

          <div className="hidden lg:block items-center space-x-2">
            <Logo logoColor="white" className="text-white" />
          </div>
          <div className="block lg:hidden items-center space-x-2">
            <WhiteShieldLogoIcon />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {isLoading && (
                <Loader2 className="h-4 w-4 animate-spin text-white" />
              )}
              <Switch
                id="version-switch"
                checked={isVersionFlag}
                onCheckedChange={handleSwitchChange}
                disabled={isLoading}
                className="data-[state=checked]:bg-blue-500 disabled:opacity-50"
              />
              <div className="text-[14px] !text-white min-w-[60px]">
                {isLoading ? 'Loading...' : isVersionFlag ? 'V0.3' : 'V0.2'}
              </div>
            </div>
          </div>
          <Image
            src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
            width={100}
            height={100}
            className="hidden lg:block w-auto h-[70px] cursor-pointer"
            alt="bubbleIcon"
            onClick={() => router.push('/dashboard/contact-support')}
          />

          <div className="flex items-center gap-2 lg:hidden">
            <button
              onClick={toggleActionsAlert}
              className={`flex items-center space-x-4 w-[70px] h-[48px] py-3.5 text-white border border-white ${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'} rounded-full justify-center`}
            >
              <div className="block">
                <InboxIcon
                  fill={isBgWhite ? appearanceSettings.brandColor : 'white'}
                />
              </div>
              <button
                onClick={toggleActionsAlert}
                className={`text-[18px] items-stretch ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              >
                4
              </button>
            </button>

            <div className="relative" ref={accountDropdownRef}>
              <button
                onClick={toggleAccountDropdown}
                className={` space-x-2 w-auto h-[48px] py-3.5 px-[10px] ${isBgWhite ? 'bg-primary-500' : 'bg-white'} text-[--navMenuColor]  rounded-full`}
              >
                <ProfileIcon fill={appearanceSettings.navMenuColor} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
