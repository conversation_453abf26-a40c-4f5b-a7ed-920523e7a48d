import { Button } from '@/components/ui/button';
import type { Course } from '@/types/courseType';
import JourneyStep from './CourseJourney';
import InterviewWarmup from '@/components/dashboard/career-jobs/applied-card/InterviewWarmup';
import SkillsSection from './SkillSection';
import TargetJobSection from './TargetJobSection';
import CourseCard from '../CourseCard';
import { useRouter } from 'next/navigation';

interface MainContentProps {
  isLoading: boolean;
  essentialsCourses: Course[];
  strategicCourses: Course[];
  advancedCourses: Course[];
  completedCourses: string[];
}

const skills = [
  'Product Lifecycle Management',
  'User Story Creation',
  'Basic Prototyping',
  'Market Research Techniques',
  'Customer Journey Mapping',
  'Competitive Analysis',
  'Basic Project Management',
];

const skillsGap = ['Competitive Analysis', 'Basic Project Management'];

export default function MainContent({
  isLoading,
  essentialsCourses,
  strategicCourses,
  advancedCourses,
}: MainContentProps) {
  const router = useRouter();

  const handleRedirect = () => {
    router.push('/dashboard/career-opportunities/jobs');
  };
  return (
    <div className="flex-1 p-6 bg-white rounded-lg">
      <TargetJobSection />

      <JourneyStep
        number={1}
        title="Product Manager Essentials (2 Courses)"
        description="Master the fundamentals of product management."
        isLoading={isLoading}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {essentialsCourses.map((course) => (
            <CourseCard key={course.id} course={course} completed />
          ))}
        </div>
      </JourneyStep>

      <JourneyStep
        number={2}
        title="Strategic Product Development (2 Courses)"
        description="Learn to craft and communicate product strategy effectively."
        isLoading={isLoading}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {strategicCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>
        <div className="">
          <SkillsSection
            title="Skill Gap Analysis"
            count={skillsGap.length}
            skills={skillsGap}
            showBackground={true}
            collapsed={true}
          />
        </div>
      </JourneyStep>

      <div className="">
        <JourneyStep
          number={3}
          title="Strategic Product Development (1 Course)"
          description="Learn to craft and communicate product strategy effectively."
          isLoading={isLoading}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {advancedCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
          <SkillsSection
            title="Skill Gap Analysis"
            count={skills.length}
            skills={skills}
            showBackground={true}
          />
        </JourneyStep>
      </div>

      <JourneyStep number={4} isLoading={isLoading} isLast>
        <div className=" relative bottom-10">
          <InterviewWarmup />
        </div>
      </JourneyStep>

      <Button
        onClick={handleRedirect}
        variant="default"
        className="w-full py-6 mt-4"
      >
        View 34 more Product Manager job listings
      </Button>
    </div>
  );
}
