/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useEffect, useRef, useState } from 'react';
import { LogOut, ChevronDown } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { dashboardSideBarRoutes } from '@/constants/V03NAV_CONFIG';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { GlobeIcon } from '@/components/icons/GlobeSvg';
import useSettingsStore from '@/zustand/store/settingsStore';
import ProfileIcon from '@/components/icons/ProfileSvg';
import { InboxIcon } from '@/components/icons/InboxSvg';

interface TopBarProps {
  toggleActionsAlert?: () => void;
}
const V03TopNavigation: React.FC<TopBarProps> = ({ toggleActionsAlert }) => {
  const router = useRouter();
  const pathname = usePathname();
  const logout = useAuthStore((state) => state.logout);
  const { appearanceSettings } = useSettingsStore();
  const [isAccountDropdownOpen, setIsAccountDropdownOpen] = useState(false);
  const [openDropdownMenu, setOpenDropdownMenu] = useState<string | null>(null);

  const accountDropdownRef = useRef<HTMLDivElement>(null);
  const menuDropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const toggleAccountDropdown = () => {
    setIsAccountDropdownOpen((prev) => !prev);
  };

  const toggleMenuDropdown = (label: string) => {
    setOpenDropdownMenu((prev) => (prev === label ? null : label));
  };

  const handleProfile = () => {
    router.push('/dashboard/profile');
    setIsAccountDropdownOpen(false);
  };

  const handleCareerProfile = () => {
    router.push('/dashboard/career-profile');
    setIsAccountDropdownOpen(false);
  };

  const isActive = (route: string) => pathname === route;

  const hasActiveChild = (item: any) => {
    if (!item.children) return false;
    return item.children.some((child: any) => isActive(child.route));
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close account dropdown if clicked outside
      if (
        accountDropdownRef.current &&
        !accountDropdownRef.current.contains(event.target as Node)
      ) {
        setIsAccountDropdownOpen(false);
      }
      let clickedOutsideAllMenus = true;
      Object.values(menuDropdownRefs.current).forEach((ref) => {
        if (ref && ref.contains(event.target as Node)) {
          clickedOutsideAllMenus = false;
        }
      });

      if (clickedOutsideAllMenus) {
        setOpenDropdownMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close all dropdowns when route changes
  useEffect(() => {
    setIsAccountDropdownOpen(false);
    setOpenDropdownMenu(null);
  }, [pathname]);

  const isBgWhite = ['#ffffff', '#fff', '#FFF', '#FFFFFF'].includes(
    appearanceSettings.navMenuColor
  );
  const user = useAuthStore((state) => state.user);

  return (
    <div className="relative top-0 left-0 right-0 bg-[--navMenuColor] text-white z-50 shadow-md py-2 items-center">
      <div className="mx-auto px-4 items-center">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {dashboardSideBarRoutes.map((item) => (
              <div
                key={item.label}
                ref={(el) => {
                  menuDropdownRefs.current[item.label] = el;
                }}
                className="relative"
              >
                <button
                  className={`flex items-center space-x-2 px-6 py-2 ${
                    isActive(item.route || '') || hasActiveChild(item)
                      ? `border-b-4 ${isBgWhite ? 'border-primary-500' : 'border-white'}`
                      : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                  }`}
                  onClick={() => {
                    if (item.children) {
                      toggleMenuDropdown(item.label);
                    } else {
                      router.push(item.route);
                      setOpenDropdownMenu(null);
                    }
                  }}
                >
                  <span
                    className={`text-[16px] ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
                  >
                    {item.label}
                  </span>
                  {item.children && (
                    <ChevronDown
                      className={`w-4 h-4 transition-transform ${isBgWhite ? '!text-primary-500' : '!text-white'} ${
                        openDropdownMenu === item.label ? 'rotate-180' : ''
                      }`}
                    />
                  )}
                </button>

                {item.children && openDropdownMenu === item.label && (
                  <div className="absolute left-0 mt-1 w-[322px] bg-[--navMenuColor] rounded-md shadow-lg py-1 text-black border border-[--navMenuColor] z-10 top-14">
                    {item.children.map((child) => (
                      <button
                        key={child.route}
                        onClick={() => {
                          router.push(child.route);
                          setOpenDropdownMenu(null);
                        }}
                        className={`block w-full text-left px-6 py-4 text-[16px] text-primary-100 space-y-4 ${isBgWhite ? 'text-primary-500' : 'text-white'} ${
                          isActive(child.route)
                            ? 'bg-transparent'
                            : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                        }`}
                      >
                        {child.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center space-x-4">
            <button
              className={`flex items-center space-x-1 px-[28px] py-[14px] h-[48px] cursor-not-allowed text-white opacity-60 border ${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'} rounded-full`}
            >
              <GlobeIcon
                fill={isBgWhite ? appearanceSettings.brandColor : 'white'}
              />
              <span
                className={`${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              >
                EN
              </span>
              <ChevronDown
                className={`w-4 h-4 ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              />
            </button>

            <button
              onClick={toggleActionsAlert}
              className={`flex items-center space-x-4 w-[150px] h-[48px] py-3.5 text-white border border-white ${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'} rounded-full justify-center`}
            >
              <div className="block">
                <InboxIcon
                  fill={isBgWhite ? appearanceSettings.brandColor : 'white'}
                />
              </div>
              <button
                onClick={toggleActionsAlert}
                className={`text-[18px] items-stretch ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              >
                Inbox: 4
              </button>
            </button>

            <div className="relative" ref={accountDropdownRef}>
              <button
                onClick={toggleAccountDropdown}
                className={`flex items-center space-x-2 w-auto h-[48px] py-3.5 px-[28px] ${isBgWhite ? 'bg-primary-500' : 'bg-white'} text-[--navMenuColor]  rounded-full`}
              >
                <ProfileIcon fill={appearanceSettings.navMenuColor} />
                <span className="!text-[--navMenuColor] text-[18px] flex truncate overflow-hidden whitespace-nowrap">
                  {user?.fullName || 'My Account'}
                </span>
                <ChevronDown
                  className={`w-5 h-5x transition-transform ${
                    isAccountDropdownOpen ? 'rotate-180' : ''
                  }`}
                />
              </button>

              {isAccountDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 text-black border border-white z-50">
                  <button
                    onClick={handleProfile}
                    className="flex items-center text-neutral-900 w-full px-4 py-2 text-[16px] hover:bg-primary-500 hover:text-white"
                  >
                    My Details
                  </button>

                  <button
                    onClick={handleCareerProfile}
                    className="flex items-center text-neutral-900 w-full px-4 py-2 text-[16px] hover:bg-primary-500 hover:text-white"
                  >
                    Career Profile
                  </button>

                  <button
                    onClick={logout}
                    className="flex items-center w-full px-4 py-2 text-[16px] text-destructive-500 hover:bg-primary-500 hover:text-white"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default V03TopNavigation;
