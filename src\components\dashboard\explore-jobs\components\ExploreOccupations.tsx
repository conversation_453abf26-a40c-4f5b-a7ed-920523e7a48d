import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import ResetFilterIcon from '@/components/icons/ResetFilter';
import ExploreOccupationsJobCategory from './ExploreOccupationsJobCategory';

function ExploreOccupations() {
  return (
    <div className="w-full space-y-4">
      <div className="flex w-full items-center">
        <div className="h-[1px] w-full bg-lightGray-100"></div>
        <h4 className="text-back-100 text-xl font-semibold whitespace-nowrap">
          Explore Occupations
        </h4>
        <div className="h-[1px] w-full bg-lightGray-100"></div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        <div className="col-span-3 bg-white rounded-lg p-5 space-y-4 h-[70%]">
          <h4 className="text-blue-400 text-xl font-semibold">Occupation</h4>
          <Input placeholder="Search keyword..." />
          <div className="">
            <h4 className="text-blue-400 text-base font-semibold">Country</h4>
            <Select>
              <SelectTrigger className="border-t-0 border-x-0">
                <SelectValue placeholder="Theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="">
            <h4 className="text-blue-400 text-base font-semibold">Refine</h4>
            <p className="text-[16px] font-normal text-gray-20">
              Use the dropdowns below to filter ISCO-08 groups.
            </p>
          </div>
          <div className="bg-black-50 rounded-lg p-2 space-y-3">
            <div className="space-y-2">
              <h4 className="text-back-100 text-base font-semibold">
                Major Group
              </h4>
              <Select>
                <SelectTrigger className="">
                  <SelectValue placeholder="Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <h4 className="text-back-100 text-base font-semibold whitespace-nowrap">
                Sub-major Group
              </h4>
              <Select>
                <SelectTrigger className="">
                  <SelectValue placeholder="Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <h4 className="text-back-100 text-base font-semibold whitespace-nowrap">
                Minor Group
              </h4>
              <Select>
                <SelectTrigger className="">
                  <SelectValue placeholder="Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button className="px-4 py-3 mr-2 w-full font-medium text-[16px]">
            Apply Filters
          </Button>
          <Button
            variant="outline"
            className="px-4 py-3 w-full font-medium text-[16px] flex justify-center items-center gap-2"
          >
            <ResetFilterIcon fill="var(--buttonColor)" /> Reset Filters
          </Button>
        </div>
        <div className="col-span-7 bg-white rounded-lg p-5 space-y-4 h-auto">
          <Tabs defaultValue="job_category" className="w-full">
            <TabsList className="bg-transparent mb-3 gap-3">
              <TabsTrigger
                value={'job_category'}
                className={`flex-1 text-center font-semibold text-lg leading-[24.38px] text-gray-20 transition-all`}
              >
                Job Categories
              </TabsTrigger>
              <TabsTrigger
                value={'job_titles'}
                className={`flex-1 text-center font-semibold text-lg leading-[24.38px] text-gray-20 transition-all`}
              >
                Job Titles
              </TabsTrigger>
            </TabsList>

            <TabsContent value="job_category" className="">
              <ExploreOccupationsJobCategory />
            </TabsContent>
            <TabsContent value="job_titles" className="">
              <ExploreOccupationsJobCategory />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

export default ExploreOccupations;
