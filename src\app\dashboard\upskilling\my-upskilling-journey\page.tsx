'use client';

import { useState, useEffect } from 'react';
import { getCourses } from '@/zustand/services/course.services';
import type { Course } from '@/types/courseType';
import MainContent from '@/components/dashboard/courses/up-skilling/MainContent';
import Sidebar from '@/components/dashboard/courses/up-skilling/Sidebar';

export default function UpskillingJourneyPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [completedCourses, setCompletedCourses] = useState<string[]>([]);

  const fetchCourses = async () => {
    setIsLoading(true);
    try {
      const response = await getCourses({
        status: 'Active',
        sortField: 'createdOn',
        pageSize: 10,
      });
      setCourses(response.data);

      setCompletedCourses([
        response.data[0]?.id || '',
        response.data[1]?.id || '',
      ]);
    } catch (error) {
      console.error('Error fetching courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  const essentialsCourses = courses.slice(0, 2);
  const strategicCourses = courses.slice(2, 4);
  const advancedCourses = courses.slice(4, 5);

  return (
    <div className="flex min-h-screen bg-transparent gap-4">
      <div className="max-w-[70%]">
        <MainContent
          isLoading={isLoading}
          essentialsCourses={essentialsCourses}
          strategicCourses={strategicCourses}
          advancedCourses={advancedCourses}
          completedCourses={completedCourses}
        />
      </div>

      <div className="max-w-[30%]">
        <Sidebar />
      </div>
    </div>
  );
}
