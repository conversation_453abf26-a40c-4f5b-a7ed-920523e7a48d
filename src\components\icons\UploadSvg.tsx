import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const UploadIcon: React.FC<IconProps> = ({
  size = 24,
  color = 'currentColor',
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M3.60156 16.1999V17.2499C3.60156 18.9896 5.01187 20.3999 6.75156 20.3999H17.2516C18.9913 20.3999 20.4016 18.9896 20.4016 17.2499V16.1999M16.2016 7.7999L12.0016 3.5999M12.0016 3.5999L7.80156 7.7999M12.0016 3.5999V16.1999"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default UploadIcon;
