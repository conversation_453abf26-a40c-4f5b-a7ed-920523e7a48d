/* eslint-disable @typescript-eslint/no-explicit-any */
import type { FC } from 'react';
import { useEffect } from 'react';
import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

type SmallDonutChartColorsProps = {
  categoryName: string;
  color: am5.Color;
};

type SmallDonutChartProps = {
  name: string;
  categoryFieldName: string;
  valueFieldName: string;
  data: any[];
  colors?: SmallDonutChartColorsProps[];
};

const SmallDonutChart: FC<SmallDonutChartProps> = ({
  name,
  data,
  categoryFieldName,
  valueFieldName,
  colors,
}) => {
  useEffect(() => {
    am5.addLicense('AM5M326788873');

    const root = am5.Root.new(`${name}-smalldonutchart`);

    if (colors) {
      root.setThemes([am5themes_Animated.new(root)]);
    } else {
      const defaultTheme = am5.Theme.new(root);
      root.setThemes([am5themes_Animated.new(root), defaultTheme]);

      defaultTheme
        .rule('ColorSet')
        .set('colors', [
          am5.color(0x022437),
          am5.color(0x1d506b),
          am5.color(0x829ead),
          am5.color(0xb4c5ce),
        ]);
    }

    const chart = root.container.children.push(
      am5percent.PieChart.new(root, {
        layout: root.horizontalLayout,
        innerRadius: am5.percent(50),
      })
    );

    const series = chart.series.push(
      am5percent.PieSeries.new(root, {
        valueField: valueFieldName,
        categoryField: categoryFieldName,
        tooltip: am5.Tooltip.new(root, {
          disabled: true,
        }),
      })
    );

    series.slices.template.setAll({
      templateField: 'sliceSettings',
    });

    if (data && data.length > 0 && colors && colors.length > 0) {
      let tempData = data.map((a: any) => Object.assign({}, a));
      tempData = tempData.map((value: any) => {
        const colorSettings = colors.find(
          (c: any) => c.categoryName === value[categoryFieldName]
        );
        if (colorSettings) {
          value.sliceSettings = {
            stroke: colorSettings.color,
            fill: colorSettings.color,
          };
        }
        return value;
      });

      series.data.setAll(tempData);
    }

    series.labels.template.set('visible', false);
    series.labels.template.set('forceHidden', true);
    series.slices.template.set('tooltipText', '');

    series.slices.template.set('toggleKey', 'none');

    series.ticks.template.set('visible', false);
    series.labels.template.adapters.add('y', (y: any, target: any) => {
      const { dataItem } = target;
      if (dataItem) {
        const tick = dataItem.get('tick');
        if (tick) {
          target.set('forceHidden', true);
          tick.set('forceHidden', true);
        }
        return y;
      }
    });
    series.appear(1000, 100);

    return () => root.dispose();
  }, []); // eslint-disable-line
  return (
    <div
      id={`${name}-smalldonutchart`}
      className="chart-container !w-[32px] !h-[32px] float-left mr-[9px]"
    />
  );
};

export default SmallDonutChart;
