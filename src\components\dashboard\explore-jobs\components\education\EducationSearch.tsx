import { Card } from '@/components/ui/card';
import React, { useState } from 'react';
// import * as Switch from '@radix-ui/react-switch'
import { Button } from '@/components/ui/button';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { CustomValues } from '@/types/customValues';
import { Label } from '@/components/ui/label';
import type { AdminValue } from '@/types';
import ResetFilterIcon from '@/components/icons/ResetFilter';

function EducationSearch() {
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [, setSelectedLocation] = useState('');

  const resetFilters = () => {
    setSelectedIndustry('');
    setSelectedLocation('');
    // setEntryLevel(false)
  };

  const getEducationSubSpecialisation = useAdminValues({
    category: AdminValuesCategories?.educationSpecialisation?.category,
    subcategory:
      AdminValuesCategories?.educationSpecialisation?.subcategories?.ISCED_3,
  });
  const educationSubSpecialisation =
    getEducationSubSpecialisation.data?.data?.data?.customValues || [];

  return (
    <div className="mt-10">
      <Card className="px-6 py-5 w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-10 w-full">
          <div className="">
            <h3 className="font-semibold text-20px text-blue-400">
              Education Specialisation
            </h3>
            <div className="space-y-1">
              <Label
                htmlFor="industry"
                className="text-neutral-500 font-medium"
              ></Label>
              <Select>
                <SelectTrigger className="">
                  <SelectValue
                    placeholder={
                      getEducationSubSpecialisation?.isLoading
                        ? 'Loading...'
                        : 'Please Select'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {educationSubSpecialisation.map(
                      (item: AdminValue, index: number) => (
                        <SelectItem
                          // eslint-disable-next-line react/no-array-index-key
                          key={`${item.id}-${index}-${index}`}
                          value={item.value}
                        >
                          {item.label}
                        </SelectItem>
                      )
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="">
            <h3 className="font-semibold text-20px text-blue-400">Location</h3>
            <div className="space-y-1">
              <Label
                htmlFor="industry"
                className="text-neutral-500 font-medium"
              ></Label>
              <Select
                value={selectedIndustry}
                onValueChange={(value) => setSelectedIndustry(value)}
              >
                <SelectTrigger className="">
                  <SelectValue placeholder="Please Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {educationSubSpecialisation.map((sector: CustomValues) => (
                      <SelectItem key={sector.id} value={sector.value}>
                        {sector.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            {/* <span className="text-16px font-light">Entry Level</span> */}
            {/* <Switch.Root
              className="w-12 h-6 bg-gray-400 rounded-full relative"
              checked={entryLevel}
              onCheckedChange={setEntryLevel}
            >
              <Switch.Thumb className="block w-8 h-8 bg-primary-400 border rounded-full transform transition translate-y-[-4px]" />
            </Switch.Root> */}
          </div>

          <div className="flex gap-4">
            <Button
              onClick={resetFilters}
              variant="outline"
              className="px-5 py-2 transition"
            >
              <ResetFilterIcon fill="var(--buttonColor)" />
              Reset Filter
            </Button>
            <Button className="px-5 py-2 transition">Apply Filter</Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default EducationSearch;
