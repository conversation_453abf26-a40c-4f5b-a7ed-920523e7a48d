'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, ChevronDown, Search, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Text from '@/components/ui/text';
import CareerProgramSvg from '@/components/icons/svg/CareerProgramIconSvg';
import { Card } from '@/components/ui/card';
import CareerProgramSvg1 from '@/components/icons/svg/CareerProgramIconSvg1';
import { useGetUniversityList, useGetProgramByUniversityId } from '@/queries';
import RequestProgram from './RequestProgram';
import { ProgramsList } from './ProgramLists';

interface ProgramItem {
  id: string;
  name: string;
  code: string;
  description?: string;
  universityId?: string;
}

interface UniversityItem {
  id: string;
  name: string;
  location?: string;
}

interface DepartmentWithPrograms {
  department: string;
  programs: ProgramItem[];
}

interface ApiResponse {
  totalCount: number;
  result: DepartmentWithPrograms[];
}

function StudentProgramPage() {
  const router = useRouter();
  const { data: universitiesResponse, isLoading: isUniversitiesLoading } =
    useGetUniversityList();
  const universities = universitiesResponse?.result || [];
  const [selectedUniversity, setSelectedUniversity] =
    useState<UniversityItem | null>(null);
  const [selectedProgram, setSelectedProgram] = useState<ProgramItem | null>(
    null
  );

  const {
    data: programsResponse,
    isLoading: isProgramsLoading,
    error: programsError,
  } = useGetProgramByUniversityId(selectedUniversity?.id || '');

  const safeResponse: ApiResponse = (() => {
    if (isProgramsLoading || !selectedUniversity?.id) {
      return { totalCount: 0, result: [] };
    }
    if (programsError) {
      console.error('Error fetching programs:', programsError);
      return { totalCount: 0, result: [] };
    }
    if (!programsResponse) {
      return { totalCount: 0, result: [] };
    }
    if (
      typeof programsResponse === 'object' &&
      programsResponse !== null &&
      'result' in programsResponse &&
      Array.isArray(programsResponse.result)
    ) {
      if (
        'totalCount' in programsResponse &&
        Array.isArray((programsResponse as ApiResponse).result)
      ) {
        return programsResponse as ApiResponse;
      }
      return { totalCount: 0, result: [] };
    }
    return { totalCount: 0, result: [] };
  })();

  const [programOpen, setProgramOpen] = useState(false);
  const [universityOpen, setUniversityOpen] = useState(false);

  const handleProgramSelection = () => {
    if (selectedProgram && selectedUniversity) {
      router.push(
        `/dashboard/career-guidance/program-builder/${selectedProgram.id}`
      );
    }
  };

  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);

  const handleRequestProgram = () => {
    setIsRequestModalOpen(true);
  };

  const isLoading = isUniversitiesLoading || isProgramsLoading;

  return (
    <>
      <Card className="flex md:items-center rounded-lg w-full">
        <div className="w-full p-6 relative z-10 md:p-8 lg:p-8">
          <div className="!p-0">
            <div className="flex flex-col">
              <div className="md:order-1">
                <h1 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
                  Program Builder
                </h1>
                <div className="flex gap-10 items-center py-8">
                  <CareerProgramSvg />
                  <CareerProgramSvg1 />
                </div>
                <Text className="text-[20px] text-neutral-900 font-semibold leading-[29px] my-2">
                  Tailor Your Education for Career Success
                </Text>
                <Text className="mb-5 text-neutral-500 text-[16px] font-normal">
                  The Study Path tool allows you to add programs, choose
                  different electives available within your selected programs,
                  and compare how they align with top occupation matches upon
                  completing the path.
                </Text>

                <div className="flex flex-col md:flex-row gap-4 my-4">
                  {/* University Dropdown - Fixed width container */}
                  <div className="w-full md:w-1/2">
                    <p className="text-[14px] text-neutral-500 leading-[16px] font-medium mb-2">
                      University:
                    </p>
                    <Popover
                      open={universityOpen}
                      onOpenChange={setUniversityOpen}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={universityOpen}
                          className="w-full max-w-full justify-between bg-white hover:bg-white rounded-md overflow-hidden"
                          disabled={isUniversitiesLoading}
                        >
                          <div className="flex items-center truncate">
                            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                            <span className="truncate">
                              {selectedUniversity
                                ? `${selectedUniversity.name}${selectedUniversity.location ? ` (${selectedUniversity.location})` : ''}`
                                : 'All'}
                            </span>
                          </div>
                          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                        <Command>
                          <div className="flex items-center border-b px-3">
                            <CommandInput placeholder="Search universities..." />
                          </div>
                          <CommandList>
                            <CommandEmpty>
                              {isUniversitiesLoading
                                ? 'Loading...'
                                : 'No university found.'}
                            </CommandEmpty>
                            <CommandGroup className="max-h-[300px] overflow-y-auto">
                              {universities.map(
                                (university: UniversityItem) => (
                                  <CommandItem
                                    key={university.id}
                                    value={`${university.name} ${university.location || ''}`}
                                    onSelect={() => {
                                      setSelectedUniversity(
                                        university.id === selectedUniversity?.id
                                          ? null
                                          : university
                                      );
                                      setUniversityOpen(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        'mr-2 h-4 w-4',
                                        selectedUniversity?.id === university.id
                                          ? 'opacity-100'
                                          : 'opacity-0'
                                      )}
                                    />
                                    <span className="truncate">
                                      {university.name}
                                      {university.location && (
                                        <span className="text-neutral-500 ml-2">
                                          ({university.location})
                                        </span>
                                      )}
                                    </span>
                                  </CommandItem>
                                )
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Program Dropdown - Fixed width container */}
                  <div className="w-full md:w-1/2">
                    <p className="text-[14px] text-neutral-500 leading-[16px] font-medium mb-2">
                      Program:
                    </p>
                    <Popover open={programOpen} onOpenChange={setProgramOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={programOpen}
                          className="w-full max-w-full justify-between bg-white hover:bg-white rounded-md overflow-hidden"
                          disabled={!selectedUniversity || isProgramsLoading}
                        >
                          <div className="flex items-center truncate">
                            {isProgramsLoading && selectedUniversity ? (
                              <Loader2 className="mr-2 h-4 w-4 shrink-0 animate-spin" />
                            ) : (
                              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                            )}
                            <span className="truncate">
                              {selectedProgram
                                ? `${selectedProgram.name}`
                                : 'All'}
                            </span>
                          </div>
                          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                        <Command>
                          <div className="flex items-center border-b px-3">
                            <CommandInput placeholder="Search programs..." />
                          </div>
                          <CommandList>
                            {isProgramsLoading ? (
                              <div className="py-6 text-center">
                                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                                <p className="text-sm text-neutral-500">
                                  Loading programs...
                                </p>
                              </div>
                            ) : programsError ? (
                              <div className="py-6 text-center">
                                <p className="text-sm text-red-500">
                                  Error loading programs. Please try again.
                                </p>
                              </div>
                            ) : safeResponse.result.length === 0 ? (
                              <CommandEmpty>
                                No programs found for this university.
                              </CommandEmpty>
                            ) : (
                              safeResponse.result.map(
                                (department: DepartmentWithPrograms) => (
                                  <CommandGroup
                                    key={department.department}
                                    heading={department.department || 'General'}
                                    className="max-h-[300px] overflow-y-auto"
                                  >
                                    {department.programs.map(
                                      (program: ProgramItem) => (
                                        <CommandItem
                                          key={program.id}
                                          value={program.name}
                                          onSelect={() => {
                                            setSelectedProgram(
                                              program.id === selectedProgram?.id
                                                ? null
                                                : program
                                            );
                                            setProgramOpen(false);
                                          }}
                                        >
                                          <Check
                                            className={cn(
                                              'mr-2 h-4 w-4',
                                              selectedProgram?.id === program.id
                                                ? 'opacity-100'
                                                : 'opacity-0'
                                            )}
                                          />
                                          <span className="truncate">
                                            {program.name}
                                          </span>
                                        </CommandItem>
                                      )
                                    )}
                                  </CommandGroup>
                                )
                              )
                            )}
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <Button
                  disabled={
                    isLoading || !selectedProgram || !selectedUniversity
                  }
                  onClick={handleProgramSelection}
                  className="min-w-[164px] mt-4"
                  variant="default"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Add Programs'
                  )}
                </Button>

                <p className="text-[16px] font-normal text-neutral-500 mt-4">
                  Cannot find your program?{' '}
                  <Button
                    variant="link"
                    onClick={handleRequestProgram}
                    className="!text-blue-400 font-semibold cursor-pointer -ml-4 no-underline hover:no-underline"
                  >
                    Request program to be added
                  </Button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <RequestProgram
        isRequestModalOpen={isRequestModalOpen}
        setIsRequestModalOpen={setIsRequestModalOpen}
      />

      <div className="mt-4">
        <ProgramsList />
      </div>
    </>
  );
}

export default StudentProgramPage;
