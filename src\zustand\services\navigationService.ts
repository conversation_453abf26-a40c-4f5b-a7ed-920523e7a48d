import axiosClient from '@/utils/axiosClient'

const API_URL = '/navigation'

export interface Component {
  id: string;
  type: string;
  name: string;
  parent: string; // Required by backend
  description: string;
  next: string; // Required by backend
  path: string;
}

interface NavigationResponse {
  success: boolean;
  message: string;
  data: {
    mappedComponents: number;
  };
}

interface UpdateCurrentPageResponse {
  success: boolean;
  message: string;
  data: {
    pageId: string;
    wasUpdated: boolean;
  };
}

const navigationService = {
  // Map UI components to Neo4j
  mapComponents: async (components: Component[], path: string): Promise<{
    mappedComponents: number;
  }> => {
    try {
      const response = await axiosClient.post<NavigationResponse>(
        `${API_URL}/map-components`,
        { components, path }
      )

      // Return the data property from the response
      return response.data.data
    } catch (error) {
      console.error('Error mapping components:', error)
      throw new Error('Failed to map components to Neo4j')
    }
  },

  // Update the current page in the navigation system
  updateCurrentPage: async (pageId: string, priority: string = 'normal'): Promise<{
    pageId: string;
    wasUpdated: boolean;
  }> => {
    try {
      const response = await axiosClient.post<UpdateCurrentPageResponse>(
        `${API_URL}/update-current-page`,
        { pageId, priority }
      )

      // Return the data property from the response
      return response.data.data
    } catch (error) {
      console.error('Error updating current page:', error)
      throw new Error('Failed to update current page')
    }
  },
}

export default navigationService
