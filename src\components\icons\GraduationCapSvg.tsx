import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const ShieldIcon: React.FC<IconProps> = ({
  size = 24,
  color = '#9CA2B0',
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M11.7399 3.65412C11.9047 3.58209 12.0921 3.58209 12.257 3.65412C15.4591 5.05319 18.494 6.76349 21.3233 8.74668C21.528 8.8902 21.6315 9.13902 21.589 9.38541C21.5464 9.63181 21.3655 9.83149 21.1244 9.89802C20.5347 10.0608 19.95 10.2361 19.3706 10.423C17.019 11.1814 14.7553 12.1357 12.5987 13.2666L12.5958 13.2681C12.4981 13.3194 12.4005 13.371 12.3032 13.423C12.113 13.5246 11.8847 13.5246 11.6945 13.423C11.5962 13.3705 11.4977 13.3184 11.399 13.2666C10.3326 12.7074 9.24003 12.1914 8.12359 11.7209V11.5283C8.12359 11.4152 8.18164 11.3148 8.27146 11.2604C9.56746 10.4752 10.9043 9.75076 12.278 9.09093C12.5995 8.93651 12.7349 8.5507 12.5805 8.2292C12.4261 7.9077 12.0403 7.77225 11.7188 7.92668C10.3073 8.60466 8.93373 9.34902 7.60221 10.1557C7.21182 10.3922 6.94938 10.7793 6.86305 11.213C6.12691 10.9298 5.38109 10.666 4.6262 10.4226C4.04685 10.2357 3.46216 10.0608 2.87243 9.89801C2.6314 9.83148 2.45043 9.6318 2.40788 9.3854C2.36533 9.13901 2.46883 8.89019 2.67358 8.74667C5.5029 6.76348 8.53774 5.05319 11.7399 3.65412Z"
      fill={color}
    />
    <path
      d="M12.9118 14.5622C15.0119 13.4401 17.2182 12.4915 19.5119 11.7355C19.6274 12.9537 19.7012 14.184 19.7318 15.425C19.7384 15.6913 19.5808 15.9343 19.335 16.0369C16.88 17.0619 14.5449 18.317 12.3565 19.7753C12.1396 19.9198 11.8571 19.9198 11.6403 19.7753C9.45193 18.317 7.11677 17.0619 4.66176 16.0369C4.41598 15.9343 4.25841 15.6913 4.26497 15.425C4.29555 14.184 4.36936 12.9535 4.48497 11.7352C5.27801 11.9966 6.06061 12.281 6.83199 12.5876V13.6815C6.44593 13.9049 6.18619 14.3223 6.18619 14.8003C6.18619 15.2268 6.39292 15.6051 6.71163 15.8403C6.63455 16.168 6.52048 16.4887 6.3694 16.7958C6.7592 16.9798 7.14566 17.1697 7.52868 17.3654C7.74682 16.9217 7.90498 16.4561 8.00316 15.9806C8.45466 15.7793 8.76939 15.3266 8.76939 14.8003C8.76939 14.3223 8.50965 13.9049 8.12359 13.6815V13.1263C9.13224 13.5671 10.1203 14.0463 11.0858 14.5622C11.6563 14.867 12.3413 14.867 12.9118 14.5622Z"
      fill={color}
    />
    <path
      d="M5.50787 17.9969C5.86877 17.636 6.15607 17.2295 6.3694 16.7958C6.7592 16.9798 7.14566 17.1697 7.52868 17.3654C7.25438 17.9232 6.88507 18.4463 6.42116 18.9102C6.16896 19.1624 5.76007 19.1624 5.50787 18.9102C5.25567 18.658 5.25567 18.2491 5.50787 17.9969Z"
      fill={color}
    />
  </svg>
);

export default ShieldIcon;
