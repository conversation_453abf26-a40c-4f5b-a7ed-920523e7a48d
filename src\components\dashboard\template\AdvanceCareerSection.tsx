import React from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import JobIcon from '@/components/icons/JobIcon';
import JobSearchIcon from '@/components/icons/JobSearchIcon';
import { useRouter } from 'next/navigation';
import useSettingsStore from '@/zustand/store/settingsStore';

function AdvanceCareerSection() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;

  return (
    <div>
      <SectionHeader title="Advance your career" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActionCard
          icon={<JobIcon stroke={brandColor} />}
          onClick={() => router.push('/dashboard/career-opportunities/jobs')}
          title={'View recommended jobs'}
          description={
            'Discover tailored job listings that match your skills and goals.'
          }
        />
        <ActionCard
          onClick={() => router.push('/dashboard/career-guidance/job-trends')}
          icon={<JobSearchIcon stroke={brandColor} />}
          title={'Explore job trends'}
          description={
            'Stay ahead with the latest industry trends and in-demand roles.'
          }
        />
      </div>
    </div>
  );
}

export default AdvanceCareerSection;
