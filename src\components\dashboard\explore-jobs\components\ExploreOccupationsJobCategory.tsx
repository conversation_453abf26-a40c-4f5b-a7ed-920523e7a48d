import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import ExploreOccupationsJobCategoryCard from './ExploreOccupationsJobCategoryCard';

function ExploreOccupationsJobCategory() {
  return (
    <div>
      <div className="flex items-center justify-between">
        <p className="text-[16px] font-normal text-gray-20">
          Showing 1 - 5 of 34 occupations
        </p>
        <div className="flex items-center gap-2">
          <p className="text-[16px] font-normal text-gray-20 whitespace-nowrap">
            Sort By:
          </p>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Highest Demand" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="light">Light</SelectItem>
              <SelectItem value="dark">Dark</SelectItem>
              <SelectItem value="system">System</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-col items-center min-h-screen  py-10 space-y-6">
        {jobListingsData.map((job) => (
          <ExploreOccupationsJobCategoryCard
            key={job.id}
            title={job.title}
            description={job.description}
            salary={job.salary}
            jobListings={job.jobListings}
            growthPercentage={job.growthPercentage}
          />
        ))}
      </div>
    </div>
  );
}

export default ExploreOccupationsJobCategory;

export const jobListingsData = [
  {
    id: 1,
    title: 'Registered Nurse (Mental Health)',
    description:
      'Registered Nurses (Mental Health) provide nursing care to patients with mental health illness, disorder and dysfunction, and those experiencing emotional difficulties, distress, and crisis in health, welfare and aged care facilities, correctional services, and the community.',
    salary: 1500,
    jobListings: 806,
    growthPercentage: 8,
  },
  {
    id: 2,
    title: 'Software Engineer',
    description:
      'Software Engineers design, develop, and maintain software systems and applications. They collaborate with teams to build scalable and efficient solutions.',
    salary: 5000,
    jobListings: 1200,
    growthPercentage: 15,
  },
  {
    id: 3,
    title: 'Data Scientist',
    description:
      'Data Scientists analyze complex datasets to derive insights and support business decision-making using statistical and machine learning techniques.',
    salary: 6000,
    jobListings: 950,
    growthPercentage: 10,
  },
  {
    id: 4,
    title: 'Cybersecurity Analyst',
    description:
      'Cybersecurity Analysts protect organizations from cyber threats by implementing security measures and monitoring for vulnerabilities.',
    salary: 4500,
    jobListings: 700,
    growthPercentage: -5,
  },
];
