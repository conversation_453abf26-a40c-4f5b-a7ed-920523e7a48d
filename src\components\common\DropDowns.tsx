/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { X } from 'lucide-react';
import type { FC, ReactNode } from 'react';
import { useCallback, useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DROPDOWN_NOT_APPLICABLE, DROPDOWN_OTHER } from '@/utils';

type Option = { label: string; value: string };

export interface DropDownData {
  id: string | number;
  title: string;
}

export interface DropDownProps {
  isAutoComplete?: boolean;
  isMultiple?: boolean;
  isRequired?: boolean;
  title?: string;
  data: any[];
  dataIdPropName: string;
  dataTitlePropName: string;
  dataPrefixPropName?: string;
  dataPostfixPropName?: string;
  handleOpen?: () => void;
  handleChange: (_e: unknown, _selectedValue: Option | Option[] | null) => void;
  handleClose?: () => void;
  style?: any;
  className?: string;
  disabled?: boolean;
  selectedValue?: any | any[] | null;
  disableCloseOnSelect?: boolean;
  placeholder?: string;
  adornment?: ReactNode;
  addDataNotApplicable?: boolean;
  byDefaultSelectDataNotApplicable?: boolean;
  addOtherOption?: boolean;
  otherOptionPlaceholder?: string;
  otherOptionValue?: string;
  setOtherOptionValue?: (_e: any, _selectedValue: string | undefined) => void;
  variant?: 'outlined' | 'standard' | null;
}

const DropDown: FC<DropDownProps> = ({
  isAutoComplete = false,
  isMultiple = false,
  isRequired = false,
  title,
  data,
  handleOpen: _handleOpen,
  handleChange,
  handleClose: _handleClose,
  style: _style,
  className,
  disabled,
  selectedValue,
  disableCloseOnSelect: _disableCloseOnSelect = false,
  placeholder,
  dataIdPropName,
  dataTitlePropName,
  dataPrefixPropName,
  dataPostfixPropName,
  adornment: _adornment,
  addDataNotApplicable = false,
  byDefaultSelectDataNotApplicable = false,
  addOtherOption = false,
  otherOptionPlaceholder,
  otherOptionValue,
  setOtherOptionValue,
  variant: _variant = 'standard',
}) => {
  const [selectDropdownValue, setselectDropdownValue] = useState<
    any | null | any[]
  >(selectedValue);
  const [dropDownData, setdropDownData] = useState<DropDownData[]>([]);
  const [isSelectedSelectValue, setisSelectedSelectValue] =
    useState<boolean>(false);
  const [reason, setReason] = useState('');

  const mapDropdownValue = useCallback(
    (value: any) => {
      let val = '';
      val = `${
        dataPrefixPropName && value[dataPrefixPropName] !== null
          ? `(${value[dataPrefixPropName]})` + ' '
          : ''
      }${value[dataTitlePropName]}${
        dataPostfixPropName && value[dataPostfixPropName] !== null
          ? ' ' + `(${value[dataPostfixPropName]})`
          : ''
      }`;
      return {
        id: value[dataIdPropName],
        title: val,
      };
    },
    [dataIdPropName, dataPostfixPropName, dataPrefixPropName, dataTitlePropName]
  );

  const updateSelectedValue = useCallback(
    (v: any, r?: any) => {
      if (Array.isArray(v)) {
        let info = [];
        if (isAutoComplete)
          info = v.map((value: any): DropDownData => {
            return mapDropdownValue(value);
          });
        else
          info = v.map((value: any): DropDownData => {
            return value[dataIdPropName];
          });
        setselectDropdownValue(info);
      } else if (v && typeof v === 'object') {
        if (isAutoComplete) {
          if (addOtherOption && otherOptionValue && r === 'selectOption') {
            setOtherOptionValue && setOtherOptionValue({}, undefined);
          }
          setselectDropdownValue(mapDropdownValue(v));
        } else {
          setselectDropdownValue(v ? v[dataIdPropName] : null);
        }
      } else {
        if (
          r !== 'clear' &&
          byDefaultSelectDataNotApplicable &&
          addDataNotApplicable
        ) {
          const data = {
            [dataIdPropName]: DROPDOWN_NOT_APPLICABLE.id,
            [dataTitlePropName]: DROPDOWN_NOT_APPLICABLE.title,
          };
          setselectDropdownValue(mapDropdownValue(data));
        } else {
          setselectDropdownValue(null);
        }
      }
    },
    [
      setselectDropdownValue,
      addDataNotApplicable,
      byDefaultSelectDataNotApplicable,
      dataIdPropName,
      dataTitlePropName,
      isAutoComplete,
      mapDropdownValue,
      setOtherOptionValue,
      addOtherOption,
      otherOptionValue,
    ]
  );

  const mapSelectedValue = useCallback(
    (data: any[], selectedValue: any, r?: any) => {
      let info = null;
      if (typeof selectedValue === 'string')
        info = data.find((x: any) => x[dataIdPropName] === selectedValue);
      else if (Array.isArray(selectedValue)) {
        info = [];
        selectedValue.forEach((value: any) => {
          if (typeof value === 'string') {
            info.push(data.find((x: any) => x[dataIdPropName] === value));
          } else {
            info.push(value);
          }
        });
      } else if (selectedValue !== null && typeof selectedValue === 'object') {
        info = data.find((x: any) => x[dataIdPropName] === selectedValue.id);
      } else {
        info = isMultiple ? [] : null;
      }
      updateSelectedValue(info, r);
      return info;
    },
    [dataIdPropName, isMultiple, updateSelectedValue]
  );

  const handleDropdownChange = useCallback(
    (e: any, v: any, r?: any) => {
      if (r === 'clear') {
        setReason(r);
        handleChange(e, null);
      }
      handleChange(e, mapSelectedValue(data, v, r));
    },
    [data, handleChange, mapSelectedValue]
  );

  useEffect(() => {
    if (Array.isArray(selectDropdownValue)) {
      setisSelectedSelectValue(selectDropdownValue.length > 0);
    } else {
      if (selectDropdownValue === null) updateSelectedValue(null);
      setisSelectedSelectValue(
        selectDropdownValue !== null && selectDropdownValue !== undefined
      );
    }
  }, [
    selectedValue,
    selectDropdownValue,
    setisSelectedSelectValue,
    updateSelectedValue,
  ]);

  useEffect(() => {
    if (reason === 'clear') {
      return;
    }
    updateSelectedValue(selectedValue);
  }, [updateSelectedValue, selectedValue, reason]);

  useEffect(() => {
    if (
      data.length > 0 &&
      addDataNotApplicable &&
      data.indexOf(
        (x: any) => x[dataIdPropName] === DROPDOWN_NOT_APPLICABLE.id
      ) < 0
    ) {
      data.unshift({
        [dataIdPropName]: DROPDOWN_NOT_APPLICABLE.id,
        [dataTitlePropName]: DROPDOWN_NOT_APPLICABLE.title,
      });
    }
    if (
      data.length > 0 &&
      addOtherOption &&
      data.indexOf((x: any) => x[dataIdPropName] === DROPDOWN_OTHER.id) < 0
    ) {
      data.unshift({
        [dataIdPropName]: DROPDOWN_OTHER.id,
        [dataTitlePropName]: DROPDOWN_OTHER.title,
      });
    }
    setdropDownData(
      data.map((value: any): DropDownData => {
        return mapDropdownValue(value);
      })
    );
  }, [
    data,
    addDataNotApplicable,
    dataIdPropName,
    dataTitlePropName,
    mapDropdownValue,
    addOtherOption,
  ]);

  if (dropDownData.length === 0) return null;

  if (isAutoComplete) {
    return (
      <div className="space-y-2">
        <Select
          onValueChange={(value) => {
            const selectedItem = dropDownData.find((item) => item.id === value);
            handleDropdownChange({}, selectedItem, 'selectOption');
          }}
          value={selectDropdownValue?.id || ''}
          disabled={disabled}
        >
          <SelectTrigger className={className}>
            <SelectValue placeholder={placeholder || `Select ${title}`} />
          </SelectTrigger>
          <SelectContent>
            {dropDownData.map((item) => (
              <SelectItem key={item.id} value={String(item.id)}>
                {isMultiple && (
                  <Checkbox
                    checked={
                      Array.isArray(selectDropdownValue) &&
                      selectDropdownValue.some((x: any) => x.id === item.id)
                    }
                    className="mr-2"
                  />
                )}
                {item.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {addOtherOption && selectDropdownValue?.id === DROPDOWN_OTHER.id && (
          <Input
            className="mt-2"
            defaultValue={otherOptionValue}
            placeholder={otherOptionPlaceholder}
            onBlur={(e) =>
              setOtherOptionValue &&
              setOtherOptionValue(e, e.target.value ?? '')
            }
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      {isSelectedSelectValue && !isRequired && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-0 h-8 w-8"
          onClick={() => handleDropdownChange({ target: 'reset' }, null)}
        >
          <X className="h-4 w-4 text-neutral-600" />
        </Button>
      )}

      <Select
        onValueChange={(value) => {
          handleDropdownChange({}, value);
        }}
        value={selectDropdownValue || ''}
        disabled={disabled}
      >
        <SelectTrigger className={className}>
          <SelectValue placeholder={`Select ${title}`} />
        </SelectTrigger>
        <SelectContent>
          {dropDownData.map((item) => (
            <SelectItem key={item.id} value={String(item.id)}>
              {isMultiple && (
                <Checkbox
                  checked={
                    Array.isArray(selectDropdownValue) &&
                    selectDropdownValue.includes(item.id)
                  }
                  className="mr-2"
                />
              )}
              {item.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {isMultiple &&
        Array.isArray(selectDropdownValue) &&
        selectDropdownValue.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {selectDropdownValue.map((value) => {
              const item = data.find((x) => x[dataIdPropName] === value);
              return item ? (
                <Badge key={value} variant="secondary">
                  {item[dataTitlePropName]}
                </Badge>
              ) : null;
            })}
          </div>
        )}
    </div>
  );
};

export default DropDown;
