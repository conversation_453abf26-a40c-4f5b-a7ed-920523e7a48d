'use client';
import ArrowBackIcon from '@/components/icons/ArrowBackIcon';
import HistoryIcon from '@/components/icons/HistoryIcon';
// import googleIcon from '@/assets/images/Google.svg';
// import Image from 'next/image';
import JobDetail from './JobDetail';
import { Button } from '@/components/ui/button';
import SkipJobModal from '@/components/modals/SkipJob';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useNotificationStore } from '@/zustand/store/notificationStore';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';
import { useQuery } from '@tanstack/react-query';
import jobMatchesService from '@/zustand/services/jobMatches.services';
import ApplyForJob from './ApplyJob';
import ApplidAllJob from './ApplidAllJob';
import JobApplied from './JobApplied';
import type { IJobMatch } from '@/types';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { Loader } from '@/components/common/Loader';

// const jobInfo = {
//     logo: <Image src={googleIcon} alt="Google Icon" width={48} height={48} />,
//     title: 'Junior Data Analyst',
//     company: 'Google',
//     location: 'Kuwait City, Kuwait',
//     salaryRange: '$4.0k–$4.5k per month',
//     jobType: 'Full-time',
//     jobNature: 'OnSite',
//     benefits: 'Benefits Available',
//     whySuits:
//         'With your CS degree and strong analytical mindset, you’re exactly who Google is looking for. This role offers real-world impact, access to world-class mentors, and a springboard into tech innovation—where your skills will shine.',
//     jobDescription: `
//     As a Junior Data Analyst at Google, you’ll be part of a high-impact team that turns data into actionable insights across a variety of global products. Your responsibilities will include gathering, cleaning, and analyzing large datasets to support business decisions and improve product performance.`,
//     jobDescription1: `You’ll work closely with engineers, designers, and product managers to uncover patterns, measure impact, and identify opportunities for innovation. From enhancing internal tools to improving user experiences, your work will help shape smarter, more intuitive Google services used by millions.`,
//     jobDescription2: `This is an ideal role for early-career professionals who are passionate about data, eager to learn, and ready to contribute to projects that make a real-world difference.
//     `,
//     responsibilities: [
//         'Collect, process, and interpret structured and unstructured data',
//         'Design and maintain dashboards, reports, and metrics',
//         'Collaborate with cross-functional teams to support product decisions',
//         'Apply statistical techniques to identify trends and generate insights',
//         'Present findings in a clear, visual, and impactful way',
//     ],
// };

const TopJobMatches = () => {
  const { showNotification } = useNotificationStore();
  const user = useAuthStore((state) => state.user);
  const selectedPersona = useSelectedPersona();
  const router = useRouter();

  const [skipJob, setSkipJob] = useState(false);
  const [applyJob, setApplyJob] = useState(0);
  const [jobInfo, setJobInfo] = useState<IJobMatch | null>(null);
  const [localJobMatches, setLocalJobMatches] = useState<IJobMatch[]>([]);
  const [appliedJobs, setAppliedJobs] = useState(0);
  const [reviewedJobs, setReviewedJobs] = useState(0);

  // Prepare job match request parameters
  const jobMatchParams = {
    role: selectedPersona?.name || '',
    skills: Array.isArray(selectedPersona?.profile?.skills)
      ? selectedPersona.profile.skills.join(', ')
      : selectedPersona?.profile?.skills || 'React, Node.js',
    jobMode: selectedPersona?.profile?.jobMode || 'Remote',
    experience: selectedPersona?.profile?.experience || 'Expert',
    industry: selectedPersona?.profile?.industry || 'IT',
    location: selectedPersona?.profile?.location || 'Dubai',
  };

  // Use React Query for automatic caching and deduplication
  const {
    data: jobMatchesData,
    isLoading,
    isError,
    refetch: fetchJobMatches,
  } = useQuery({
    queryKey: ['jobMatches', selectedPersona?.name, jobMatchParams],
    queryFn: () => jobMatchesService.getJobMatches(jobMatchParams),
    enabled: !!(selectedPersona?.name && reviewedJobs < 3), // Only fetch if persona exists and not all reviewed
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Store for cover letter generation and progress tracking
  const { generateCoverLetter, isLoading: isCoverLetterLoading, applyForJob: updateStoreProgress } = useJobMatchesStore();

  // Extract job matches from React Query data and track completion
  const allJobsReviewed = reviewedJobs >= 3;

  // Initialize local job matches when data is fetched
  useEffect(() => {
    if (jobMatchesData?.data?.jobs) {
      setLocalJobMatches(jobMatchesData.data.jobs);
    }
  }, [jobMatchesData]);

  // Local function to handle job applications/skips
  const handleJobAction = (jobId: string, actionType: 'applied' | 'skipped') => {
    setLocalJobMatches(prev =>
      prev.map(job =>
        job.id === jobId ? { ...job, applyType: actionType } : job
      )
    );

    if (actionType === 'applied') {
      setAppliedJobs(prev => prev + 1);
    }
    setReviewedJobs(prev => prev + 1);

    // Update Zustand store for dashboard progress tracking
    updateStoreProgress(jobId, actionType);

    // Reset to job view to show next job
    setApplyJob(0);
  };

  useEffect(() => {
    // If all jobs are reviewed, show completion screen
    if (allJobsReviewed) {
      setApplyJob(4);
      return;
    }

    // Find next unreviewed job from local state
    const job = localJobMatches?.find((el: any) => !el?.applyType);
    if (job) {
      setJobInfo(job);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localJobMatches, allJobsReviewed]);

  const handleOpen = () => {
    setSkipJob(!skipJob);
    if (skipJob && jobInfo) {
      handleJobAction(jobInfo?.id, 'skipped');
      showNotification('Thank you for providing feedback', 'success');
    }
  };

  const handleBack = () => {
    if (applyJob > 0) {
      return setApplyJob(applyJob - 1);
    }
    router.back();
  };

  const handleApply = () => {
    setApplyJob(applyJob + 1);
  };

  const handleNextJob = () => {
    setApplyJob(0);
  };

  const handleBasicDetails = async ({
    email,
    mobile,
  }: {
    email?: string;
    mobile?: string;
  }) => {
    const res = await generateCoverLetter({
      email,
      mobile: `${mobile}`,
      name: user?.fullName,
      companyName: jobInfo?.companyName,
      role: selectedPersona?.name,
    });

    console.log(res);

    setApplyJob(applyJob + 1);
  };

  const handleSendApplication = () => {
    if (jobInfo) {
      handleJobAction(jobInfo?.id, 'applied');
    }

    if (appliedJobs === 2) {
      return setApplyJob(applyJob + 2);
    }
    setApplyJob(applyJob + 1);
  };

  const handleHistory = () => {
    router.push('/V03/dashboard/career-opportunities/applied-jobs');
  };

  // Show loading screen when fetching job matches
  if (isLoading || (isLoading && localJobMatches.length === 0)) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
        <Loader />
        <p className="text-neutral-500 !text-white relative bottom-[20rem]">
          Finding your top job matches...
        </p>
      </div>
    );
  }

  if (localJobMatches.length === 0 && !isLoading) {
    return (
      <div className="w-full h-100vh flex justify-center items-center">
        <h1 className="text-[18px] font-semibold text-neutral-900 pt-10">
          No job matches found. Please check back later.
        </h1>
      </div>
    );
  }

  return (
    <div className="w-full h-100vh flex justify-center items-center flex-col relative">
      {/* Loading overlay for cover letter generation */}
      {isCoverLetterLoading && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
          <Loader />
          <p className="text-neutral-500 !text-white relative bottom-[20rem]">
            Generating your cover letter...
          </p>
        </div>
      )}

      <div className="w-full  inline-flex flex-col justify-center items-start overflow-hidden mb-20 items-center ">
        {applyJob < 3 && (
          <div className="w-full flex justify-between items-center py-[12px] px-8 border-b-[1px] border-neutral-200 rounded-tl-[20px] rounded-tr-[20px] bg-white border-[1px] border-neutral-200">
            <div onClick={handleBack} className="cursor-pointer">
              <ArrowBackIcon />
            </div>
            <div>
              <h1 className="font-medium text-[16px] text-[--headingTextColor]">
                {applyJob > 0 ? `Apply Now` : `Top Job Matches`}
              </h1>
            </div>
            <div onClick={handleHistory} className="cursor-pointer">
              {applyJob === 0 && <HistoryIcon />}
            </div>
          </div>
        )}

        {applyJob === 0 && (
          <JobDetail job={jobInfo} handleApply={handleApply} />
        )}
        {(applyJob === 1 || applyJob === 2) && (
          <ApplyForJob
            job={jobInfo}
            step={applyJob}
            handleBasicDetails={handleBasicDetails}
            handleSendApplication={handleSendApplication}
          />
        )}
        {applyJob === 3 && (
          <JobApplied handleNextJob={handleNextJob} appliedJobs={appliedJobs} reviewedJobs={reviewedJobs} />
        )}
        {applyJob === 4 && <ApplidAllJob />}
      </div>
      {applyJob === 0 && (
        <div className="w-full sm:w-[568px] md:w-[568px] lg:w-[600px] rounded-[20px] border-[1px] border-neutral-200 inline-flex justify-center items-start overflow-hidden  bg-white p-4 gap-4 fixed top-[89%]">
          <Button
            variant={'outline'}
            className="w-[35%] h-[48px] border-neutral-100"
            onClick={handleOpen}
          >
            Skip
          </Button>
          <Button className="w-[65%] h-[48px]" onClick={handleApply}>
            Apply now
          </Button>
        </div>
      )}

      <SkipJobModal open={skipJob} handleOpen={handleOpen} />
    </div>
  );
};

export default TopJobMatches;
