'use client';
import ArrowBackIcon from '@/components/icons/ArrowBackIcon';
import HistoryIcon from '@/components/icons/HistoryIcon';
// import googleIcon from '@/assets/images/Google.svg';
// import Image from 'next/image';
import JobDetail from './JobDetail';
import { Button } from '@/components/ui/button';
import SkipJobModal from '@/components/modals/SkipJob';
import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useNotificationStore } from '@/zustand/store/notificationStore';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';
import ApplyForJob from './ApplyJob';
import ApplidAllJob from './ApplidAllJob';
import JobApplied from './JobApplied';
import type { IJobMatch } from '@/types';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { Loader } from '@/components/common/Loader';

// const jobInfo = {
//     logo: <Image src={googleIcon} alt="Google Icon" width={48} height={48} />,
//     title: 'Junior Data Analyst',
//     company: 'Google',
//     location: 'Kuwait City, Kuwait',
//     salaryRange: '$4.0k–$4.5k per month',
//     jobType: 'Full-time',
//     jobNature: 'OnSite',
//     benefits: 'Benefits Available',
//     whySuits:
//         'With your CS degree and strong analytical mindset, you’re exactly who Google is looking for. This role offers real-world impact, access to world-class mentors, and a springboard into tech innovation—where your skills will shine.',
//     jobDescription: `
//     As a Junior Data Analyst at Google, you’ll be part of a high-impact team that turns data into actionable insights across a variety of global products. Your responsibilities will include gathering, cleaning, and analyzing large datasets to support business decisions and improve product performance.`,
//     jobDescription1: `You’ll work closely with engineers, designers, and product managers to uncover patterns, measure impact, and identify opportunities for innovation. From enhancing internal tools to improving user experiences, your work will help shape smarter, more intuitive Google services used by millions.`,
//     jobDescription2: `This is an ideal role for early-career professionals who are passionate about data, eager to learn, and ready to contribute to projects that make a real-world difference.
//     `,
//     responsibilities: [
//         'Collect, process, and interpret structured and unstructured data',
//         'Design and maintain dashboards, reports, and metrics',
//         'Collaborate with cross-functional teams to support product decisions',
//         'Apply statistical techniques to identify trends and generate insights',
//         'Present findings in a clear, visual, and impactful way',
//     ],
// };

const TopJobMatches = () => {
  const { jobMatches, appliedJobs, applyForJob, generateCoverLetter, isLoading, getJobMatches } =
    useJobMatchesStore();
  const { showNotification } = useNotificationStore();
  const user = useAuthStore((state) => state.user);
  const selectedPersona = useSelectedPersona();
  const router = useRouter();

  const [skipJob, setSkipJob] = useState(false);
  const [applyJob, setApplyJob] = useState(0);
  const [jobInfo, setJobInfo] = useState<IJobMatch | null>(null);

  // Use ref to track if we've already initiated API calls for this persona
  const apiCallsInitiated = useRef<string | null>(null);

  // Fetch job matches if not already loaded or when persona changes
  useEffect(() => {
    const personaKey = selectedPersona?.name || '';

    // Only make API calls if we haven't already initiated them for this persona
    if (selectedPersona && personaKey && apiCallsInitiated.current !== personaKey) {
      apiCallsInitiated.current = personaKey;

      // Prepare the job match request with proper data formatting
      const jobMatchRequest = {
        role: selectedPersona?.name || '',
        skills: Array.isArray(selectedPersona?.profile?.skills)
          ? selectedPersona.profile.skills.join(', ')
          : selectedPersona?.profile?.skills || 'React, Node.js',
        jobMode: selectedPersona?.profile?.jobMode || 'Remote',
        experience: selectedPersona?.profile?.experience || 'Expert',
        industry: selectedPersona?.profile?.industry || 'IT',
        location: selectedPersona?.profile?.location || 'Dubai',
      };

      getJobMatches(jobMatchRequest);
    }
  }, [selectedPersona?.name]);

  useEffect(() => {
    const job = jobMatches?.find((el) => !el?.applyType);
    if (job) setJobInfo(job);
    if (appliedJobs === 3) setApplyJob(4);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobMatches]);

  const handleOpen = () => {
    setSkipJob(!skipJob);
    if (skipJob && jobInfo) {
      applyForJob(jobInfo?.id, 'skipped');
      if (appliedJobs === 2) setApplyJob(4);
      showNotification('Thank you for providing feedback', 'success');
    }
  };

  const handleBack = () => {
    if (applyJob > 0) {
      return setApplyJob(applyJob - 1);
    }
    router.back();
  };

  const handleApply = () => {
    setApplyJob(applyJob + 1);
  };

  const handleNextJob = () => {
    setApplyJob(0);
  };

  const handleBasicDetails = async ({
    email,
    mobile,
  }: {
    email?: string;
    mobile?: string;
  }) => {
    const res = await generateCoverLetter({
      email,
      mobile: `${mobile}`,
      name: user?.fullName,
      companyName: jobInfo?.companyName,
      role: selectedPersona?.name,
    });

    console.log(res);

    setApplyJob(applyJob + 1);
  };

  const handleSendApplication = () => {
    if (jobInfo) {
      applyForJob(jobInfo?.id, 'applied');
    }

    if (appliedJobs === 2) {
      return setApplyJob(applyJob + 2);
    }
    setApplyJob(applyJob + 1);
  };

  const handleHistory = () => {
    router.push('/V03/dashboard/career-opportunities/applied-jobs');
  };

  // Show loading screen when fetching job matches
  if (isLoading === 'jobMatches' || (isLoading && jobMatches.length === 0)) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
        <Loader />
        <p className="text-neutral-500 !text-white relative bottom-[20rem]">
          {isLoading === 'jobMatches'
            ? 'Finding your top job matches...'
            : 'Loading your job matches...'}
        </p>
      </div>
    );
  }

  if (jobMatches.length === 0) {
    return (
      <div className="w-full h-100vh flex justify-center items-center">
        <h1 className="text-[18px] font-semibold text-neutral-900 pt-10">
          No job matches found. Please check back later.
        </h1>
      </div>
    );
  }

  return (
    <div className="w-full h-100vh flex justify-center items-center flex-col relative">
      {/* Loading overlay for cover letter generation */}
      {isLoading === 'generatingMessage' && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
          <Loader />
          <p className="text-neutral-500 !text-white relative bottom-[20rem]">
            Generating your cover letter...
          </p>
        </div>
      )}

      <div className="w-full  inline-flex flex-col justify-center items-start overflow-hidden mb-20 items-center ">
        {applyJob < 3 && (
          <div className="w-full flex justify-between items-center py-[12px] px-8 border-b-[1px] border-neutral-200 rounded-tl-[20px] rounded-tr-[20px] bg-white border-[1px] border-neutral-200">
            <div onClick={handleBack} className="cursor-pointer">
              <ArrowBackIcon />
            </div>
            <div>
              <h1 className="font-medium text-[16px] text-[--headingTextColor]">
                {applyJob > 0 ? `Apply Now` : `Top Job Matches`}
              </h1>
            </div>
            <div onClick={handleHistory} className="cursor-pointer">
              {applyJob === 0 && <HistoryIcon />}
            </div>
          </div>
        )}

        {applyJob === 0 && (
          <JobDetail job={jobInfo} handleApply={handleApply} />
        )}
        {(applyJob === 1 || applyJob === 2) && (
          <ApplyForJob
            job={jobInfo}
            step={applyJob}
            handleBasicDetails={handleBasicDetails}
            handleSendApplication={handleSendApplication}
          />
        )}
        {applyJob === 3 && (
          <JobApplied handleNextJob={handleNextJob} appliedJobs={appliedJobs} />
        )}
        {appliedJobs === 3 && applyJob === 4 && <ApplidAllJob />}
      </div>
      {applyJob === 0 && (
        <div className="w-full sm:w-[568px] md:w-[568px] lg:w-[600px] rounded-[20px] border-[1px] border-neutral-200 inline-flex justify-center items-start overflow-hidden  bg-white p-4 gap-4 fixed top-[89%]">
          <Button
            variant={'outline'}
            className="w-[35%] h-[48px] border-neutral-100"
            onClick={handleOpen}
          >
            Skip
          </Button>
          <Button className="w-[65%] h-[48px]" onClick={handleApply}>
            Apply now
          </Button>
        </div>
      )}

      <SkipJobModal open={skipJob} handleOpen={handleOpen} />
    </div>
  );
};

export default TopJobMatches;
