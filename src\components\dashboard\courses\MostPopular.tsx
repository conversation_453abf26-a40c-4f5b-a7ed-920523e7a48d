'use client';

import { Button } from '@/components/ui/button';
import type { Course } from '@/types/courseType';
import CourseCard from './CourseCard';
import PopularCourseCard from './PopularCourseCard';
import { Card } from '@/components/ui/card';
import { Loader } from '@/components/common/Loader';

interface PopularCoursesSectionProps {
  courses: Course[];
  isLoading: boolean;
  hasMore: boolean;
  onShowMore: () => void;
}

export default function PopularCoursesSection({
  courses,
  isLoading,
  hasMore,
  onShowMore,
}: PopularCoursesSectionProps) {
  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  if (!courses || courses.length === 0) {
    return (
      <Card className="text-center py-12 rounded-lg">
        <h3 className="text-lg font-medium">No popular courses found</h3>
        <p className="text-muted-foreground mt-1">
          Check back later for updated popular courses
        </p>
      </Card>
    );
  }

  const featuredCourse = courses[0];
  const standardCourses = courses.slice(1);

  return (
    <div className="space-y-6">
      {featuredCourse && (
        <>
          <h2 className="text-xl font-semibold">
            Most Popular Courses This Month
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-3">
              <PopularCourseCard course={featuredCourse} featured />
            </div>
            {standardCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        </>
      )}

      {hasMore && (
        <div className="mt-6">
          <Button
            variant="outline"
            onClick={onShowMore}
            className="px-6 items-start"
          >
            Show more
          </Button>
        </div>
      )}
    </div>
  );
}
