'use client';

import React, { useState } from 'react';
import { AlertIcon } from '@/assets';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export const ActionsAlert: React.FC = () => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(true);
  const router = useRouter();

  const hideDropdown = () => setIsDropdownVisible(false);

  return (
    <div className="w-full transition-all duration-300">
      {isDropdownVisible && (
        <div className="bg-warning-200 z-[999999] text-black px-4 py-6 gap-3 flex items-center text-[16px] font-medium absolute w-full">
          <Image src={AlertIcon} alt="alertIcon" />
          <span>You have 4 pending actions.</span>

          <Button
            className="px-6 py-0 bg-transparent text-black border border-[#000] rounded-full text-[16px] font-medium hover:bg-transparent"
            onClick={() => router.push('/dashboard/pending-actions')}
          >
            View
          </Button>

          <Button
            className="px-3 py-1 bg-transparent text-black text-[16px] font-medium hover:bg-transparent"
            onClick={hideDropdown}
          >
            Hide
          </Button>
        </div>
      )}
    </div>
  );
};
