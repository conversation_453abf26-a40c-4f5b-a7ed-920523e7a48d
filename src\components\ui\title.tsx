'use client';

import { cn } from '@/lib/utils';
import type { ReactNode } from 'react';
import React from 'react';

interface TitleProps {
  children: ReactNode;
  variant: 'h1' | 'h2' | 'h3' | 'h4' | 'h5';
  className?: string;
}

const Title: React.FC<TitleProps> = ({ children, variant, className = '' }) => {
  const baseClass = 'font-semibold';

  const VariantTag = variant;

  const variantClass = {
    h1: 'text-[2.5rem]/[160%]',
    h2: 'text-[2rem]/[160%]',
    h3: 'text-[1.75rem]/[160%]',
    h4: 'text-[1.25rem]/[160%]',
    h5: 'text-[1.25rem]/[160%]',
  };

  const combinedClasses = cn(baseClass, variantClass[variant], className);

  return <VariantTag className={combinedClasses}>{children}</VariantTag>;
};

export default Title;
