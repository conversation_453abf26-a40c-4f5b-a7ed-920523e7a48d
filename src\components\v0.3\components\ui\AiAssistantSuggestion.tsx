import AiAssistantSparklesIcon from '@/components/icons/AiAssistantSparklesIcon';
import { But<PERSON> } from '@/components/ui/button';
import React from 'react';

function AiAssistantSuggestion() {
  return (
    <div className="mb-8">
      <h3 className="font-medium text-[#111827] mb-3">Ask your AI Assistant</h3>
      <div className="flex flex-wrap gap-3">
        <Button
          variant="outline"
          className="border-[1.5px] border-primary-500 text-primary-500 hover:bg-transparent rounded-lg"
        >
          <AiAssistantSparklesIcon />
          Walk me through my next task
        </Button>
        <Button
          variant="outline"
          className="border-[1.5px] border-primary-500 text-primary-500 hover:bg-transparent rounded-lg"
        >
          <AiAssistantSparklesIcon />
          Something else...
        </Button>
      </div>
    </div>
  );
}

export default AiAssistantSuggestion;
