import Image from 'next/image';
import Link from 'next/link';
import type { Course } from '@/types/courseType';
import FallbackImage from '../../../assets/images/ws/dashboard1.svg';
import { formatDate, formatUSD, normalizeText } from '@/utils';
import { Button } from '@/components/ui/button';
import { CheckIcon } from 'lucide-react';

interface CourseCardProps {
  course: Course;
  featured?: boolean;
  completed?: boolean;
}

export default function CourseCard({
  course,
  featured = false,
  completed = false,
}: CourseCardProps) {
  const formattedPrice = formatUSD(course.courseFee || 0);

  const getDurationInWeeks = () => {
    if (course.isSelfPaced) return 'Self-paced';

    if (course.duration && course.durationUnit) {
      if (course.durationUnit === 'Weeks') return `${course.duration} Weeks`;
      if (course.durationUnit === 'Days')
        return `${Math.ceil(Number.parseInt(course.duration) / 7)} Weeks`;
      if (course.durationUnit === 'Months')
        return `${Number.parseInt(course.duration) * 4} Weeks`;
    }

    return 'N/A';
  };

  const getExperienceLevelColor = () => {
    switch (course.experienceLevel?.toLowerCase()) {
      case 'beginner':
        return 'bg-success-100 text-neutral-900 font-medium';
      case 'intermediate':
        return 'bg-warning-100 text-neutral-900 font-medium';
      case 'advanced':
        return 'bg-destructive-100 text-neutral-900 font-medium';
      default:
        return 'bg-neutral-900 text-white font-medium';
    }
  };

  const getLocationType = () => {
    if (course.deliveryMode === 'Online') return 'Online';
    if (course.deliveryMode === 'Hybrid') return 'Hybrid';
    return course.city || 'In Person';
  };

  return (
    <div
      className={`bg-white rounded-lg overflow-hidden shadow-xs border border-neutral-50 hover:shadow-md transition-shadow ${featured ? 'h-full' : ''}`}
    >
      <Link href={`/dashboard/upskilling/all-courses/${course?.id}`}>
        <div className="p-4 space-y-3">
          <div className="relative">
            <Image
              src={course?.coverImage || FallbackImage}
              alt={course?.name}
              width={featured ? 1200 : 400}
              height={featured ? 400 : 240}
              className={`w-full ${featured ? 'h-64' : 'h-48'} object-cover rounded-md`}
            />
          </div>

          <div className=""></div>
          <div className="space-y-1">
            <h3 className="font-semibold text-[20px]  line-clamp-2 leading-[28px] text-neutral-900">
              {course.name}
            </h3>
            <p className="text-neutral-500 text-[16px] font-normal leading-[24px]">
              {course.partner?.name || 'Training Provider Name'}
            </p>
          </div>

          <div className="font-semibold text-[24px] text-[--bodyTextColor] leading-[24px]">
            USD {formattedPrice}
          </div>

          <div className="space-y-4">
            <div className="items-center space-x-2">
              <span
                className={`px-3 py-[6px] rounded text-[16px] font-medium  ${getExperienceLevelColor()}`}
              >
                {course.experienceLevel || 'N/A'}
              </span>
              <span className="bg-neutral-100 px-3 py-[6px] rounded text-[16px] text-neutral-900 font-medium">
                {course.deliveryMode || 'N/A'}
              </span>
            </div>

            <div className="flex text-[16px] font-medium space-x-2 text-[--bodyTextColor]">
              <div className="bg-neutral-100 px-2 py-[6px] rounded font-medium">
                Next Start: {formatDate(course.startDate) || 'N/A'}
              </div>
              <div className="flex items-center">
                <span className="bg-neutral-100 px-3 py-[6px] rounded font-medium">
                  {getDurationInWeeks()}
                </span>
              </div>
            </div>
          </div>
          <div className="text-[16px] text-[--bodyTextColor] font-normal capitalize">
            Location: {normalizeText(getLocationType())}
          </div>
        </div>
      </Link>

      {completed && (
        <div className="flex justify-center pb-4 px-4">
          <Button variant="outline" className="w-full">
            <CheckIcon className="mr-2 h-4 w-4 text-[--buttonColor]" />
            Completed
          </Button>
        </div>
      )}
    </div>
  );
}
