import { DeletePersonalData } from '@/constants/PRIVACY_DEFINITION';
import React from 'react';

function DeletePersonal() {
  return (
    <div>
      <h5 className="text-blue-300 text-[16px] font-medium mt-4">
        Delete Your Personal Data
      </h5>

      <ul className="flex flex-col gap-3 mt-1">
        {DeletePersonalData.map((item) => (
          <li
            key={item.par1}
            className="text-[16px] font-normal leading-6 text-neutral-900"
          >
            <span className="font-normal text-[16px] text-neutral-900">
              {item.par1}
            </span>{' '}
            <br /> <br />
            <span className="font-normal text-[16px] text-neutral-900">
              {' '}
              {item.par2}{' '}
            </span>
            <br /> <br />
            <span className="font-normal text-[16px] text-neutral-900">
              {' '}
              {item.par3}{' '}
            </span>
            <br /> <br />
            <span className="font-normal text-[16px] text-neutral-900">
              {' '}
              {item.par4}{' '}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default DeletePersonal;
