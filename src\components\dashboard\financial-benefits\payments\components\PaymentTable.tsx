'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Card, CardContent } from '@/components/ui/card';
import { PaginationControls } from '@/components/common/PaginationController';

const payments = [
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Jan 2025',
    status: 'Upcoming',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Jan 2025',
    status: 'Upcoming',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Dec 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Dec 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Nov 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Nov 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Oct 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Oct 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Sep 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Sep 2024',
    status: 'Paid',
  },
];

// Pagination Config
const itemsPerPage = 5;

const PaymentTable: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate the pagination
  const totalPages = Math.ceil(payments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPayments = payments.slice(startIndex, endIndex);

  return (
    <Card className="p-4 shadow-md my-5">
      <CardContent>
        <Table>
          <TableHeader className="bg-[#F1F2F4] text-neutral-900 font-semibold text-[16px]">
            <TableRow>
              <TableHead>Reference</TableHead>
              <TableHead>Benefit</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Payment Date</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPayments.map((payment) => (
              <TableRow
                key={payment.reference}
                className="text-neutral-500 text-[16px] font-normal"
              >
                <TableCell>{payment.reference}</TableCell>
                <TableCell>{payment.benefit}</TableCell>
                <TableCell>{payment.amount}</TableCell>
                <TableCell>{payment.frequency}</TableCell>
                <TableCell>{payment.paymentDate}</TableCell>
                <TableCell
                  className={
                    payment.status === 'Upcoming'
                      ? 'text-green-600'
                      : 'text-gray-600'
                  }
                >
                  {payment.status}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {payments.length > 0 && (
          <PaginationControls
            page={currentPage}
            totalPages={totalPages}
            handlePageChange={setCurrentPage}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default PaymentTable;
