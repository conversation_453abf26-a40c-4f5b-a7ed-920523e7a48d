/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  getAdminValuesProps,
  GetProgramsListProps,
  Params,
  SavedCourses,
} from '@/types';
import adminValuesService from '@/services/settings/adminValues.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import userService from '@/services/user/user.service';
import type { CourseFilters } from '@/types/courseType';
import courseServices from '@/services/courses/courses.service';
import assessmentService from '@/services/assessments';
import programBuilderService from '@/services/programBuilder';
import jobService from '@/zustand/services/job.services';
import appliedJobService from '@/zustand/services/appliedJob.service';

const useFetchJobs = (params: {
  page?: number;
  size?: number;
  search?: string;
  location?: string;
  jobType?: string;
  monthlyFrom?: string;
  status?: 'Active' | 'Closed';
}) => {
  return useQuery({
    queryKey: ['jobs', params],
    queryFn: () => jobService.getJobs(params),
  });
};

const useAdminValues = (params: Partial<getAdminValuesProps> = {}) => {
  return useQuery({
    queryKey: ['adminValues', params],
    queryFn: () => adminValuesService.getAll(params),
  });
};

const useCvs = (userId: string) => {
  return useQuery({
    queryKey: ['usercvs', userId],
    queryFn: () => userService.getCvs(userId),
  });
};

const useCourses = (params: Partial<CourseFilters>) => {
  return useQuery({
    queryKey: ['courses', params],
    queryFn: async () => await courseServices.all(params),
  });
};

const useCurrentAssessment = () => {
  return useQuery({
    queryKey: ['currentAssessment'],
    queryFn: () => assessmentService.getCurrentAssessment(),
  });
};

const useCreateAssessment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => assessmentService.createNewAssessment(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currentAssessment'] });
    },
  });
};

const useUpdateAssessment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (answers: Array<{ id: string; answerValue: number }>) =>
      assessmentService.updateAssessment(answers),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currentAssessment'] });
      queryClient.invalidateQueries({ queryKey: ['assessmentScores'] });
    },
  });
};

const useAssessmentScores = () => {
  return useQuery({
    queryKey: ['assessmentScores'],
    queryFn: () => assessmentService.getAssessmentScores(),
  });
};

const useRevokeAssessment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => assessmentService.revokeAssessment(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currentAssessment'] });
      queryClient.invalidateQueries({ queryKey: ['assessmentScores'] });
    },
  });
};

const useGetProgramByUniversityId = (universityId: string) => {
  return useQuery({
    queryKey: ['programs', universityId],
    queryFn: () => programBuilderService.getProgramByUniversityId(universityId),
    enabled: !!universityId,
  });
};

const useGetProgramInfoAndCoursesWithScore = (
  programId: string,
  hubId: string
) => {
  return useQuery({
    queryKey: ['programInfo', programId, hubId],
    queryFn: () =>
      programBuilderService.getProgramInfoAndCoursesWithScore(programId, hubId),
    enabled: !!programId && !!hubId,
  });
};

const useGetProgramInfoAndCourses = (programId: string, _current: boolean) => {
  return useQuery({
    queryKey: ['programInfoAndCourses', programId],
    queryFn: () => programBuilderService.getProgramInfoAndCourses(programId),
    enabled: !!programId,
  });
};

const useGetProgramId = (programId: string) => {
  return useQuery({
    queryKey: ['programId', programId],
    queryFn: () => programBuilderService.getProgramId(programId),
    enabled: !!programId,
  });
};

const useGetCoursePdf = (_p0?: string, _p1?: { enabled: boolean }) => {
  return useMutation({
    mutationFn: (data: any) => programBuilderService.getCoursePdf(data),
  });
};

const useGetUniversitiesLookup = () => {
  return useQuery({
    queryKey: ['universitiesLookup'],
    queryFn: programBuilderService.getUniversitiesLookup,
  });
};

const useGetUniversityPrograms = () => {
  return useQuery({
    queryKey: ['universityPrograms'],
    queryFn: programBuilderService.getUniversityPrograms,
  });
};

const useGetUniversityList = () => {
  return useQuery({
    queryKey: ['universityList'],
    queryFn: programBuilderService.getUniversityList,
  });
};

const useGetProgramDegrees = () => {
  return useQuery({
    queryKey: ['programDegrees'],
    queryFn: programBuilderService.getProgramDegrees,
  });
};

const useGetProgramLevels = () => {
  return useQuery({
    queryKey: ['programLevels'],
    queryFn: programBuilderService.getProgramLevels,
  });
};

const useGetProgramsByUniversity = (
  universityId: string,
  faculties?: string[]
) => {
  return useQuery({
    queryKey: ['programsByUniversity', universityId, faculties],
    queryFn: () =>
      programBuilderService.getProgramsByUniversity(universityId, faculties),
    enabled: !!universityId,
  });
};

const useGetUserCourses = () => {
  return useQuery({
    queryKey: ['userCourses'],
    queryFn: programBuilderService.getUserCourses,
  });
};

const useGetUserPrograms = () => {
  return useQuery({
    queryKey: ['userPrograms'],
    queryFn: programBuilderService.getUserPrograms,
  });
};

const useGetProgramsWithPagination = (params: GetProgramsListProps) => {
  return useQuery({
    queryKey: ['programsPaginated', params],
    queryFn: () => programBuilderService.getProgramsWithPagination(params),
    enabled: !!params,
  });
};

const useSaveUserProgramCourses = () => {
  // eslint-disable-next-line no-unused-vars
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: SavedCourses) =>
      programBuilderService.saveUserProgramCourses(data),
  });
};

const useGetDepartmentsByUniversityIdV2 = (universityId: string) => {
  return useQuery({
    queryKey: ['departmentsByUniversityV2', universityId],
    queryFn: () =>
      programBuilderService.getDepartmentsByUniversityIdV2(universityId),
    enabled: !!universityId,
  });
};

const useDeleteUserProgramCourses = () => {
  return useMutation({
    mutationFn: (data: { programId: string; courseIds: string[] }) =>
      programBuilderService.deleteUserProgramCourses(data),
  });
};

const useRequestProgramsForUniversity = () => {
  return useMutation({
    mutationFn: (data: {
      universityId: string;
      otherUniversityName?: string;
      programName: string;
      programUrl: string;
    }) => programBuilderService.requestProgramsForUniversity(data),
  });
};

const useAppliedJobs = (params: Params) => {
  return useQuery({
    queryKey: ['appliedJobs', params],
    queryFn: () => appliedJobService.getAppliedJobs(params),
  });
};

const useGetEnrollmentById = (userId: string, enabled = true) => {
  return useQuery({
    queryKey: ['enrollment', userId],
    queryFn: () => courseServices.getCourseEnrollments(userId),
    enabled: !!userId && enabled,
  });
};

const useUpdateEnrollmentComplete = () => {
  return useMutation({
    mutationFn: (courseId: string) =>
      courseServices.getCourseUpdateEnrollments(courseId),
  });
};

export {
  useAdminValues,
  useCvs,
  useCourses,
  useCurrentAssessment,
  useAssessmentScores,
  useUpdateAssessment,
  useRevokeAssessment,
  useCreateAssessment,
  useGetProgramByUniversityId,
  useGetProgramInfoAndCoursesWithScore,
  useGetProgramInfoAndCourses,
  useGetProgramId,
  useGetCoursePdf,
  useGetUniversitiesLookup,
  useGetUniversityPrograms,
  useGetDepartmentsByUniversityIdV2,
  useGetUniversityList,
  useGetProgramDegrees,
  useGetProgramsByUniversity,
  useGetProgramLevels,
  useGetProgramsWithPagination,
  useSaveUserProgramCourses,
  useGetUserCourses,
  useGetUserPrograms,
  useDeleteUserProgramCourses,
  useRequestProgramsForUniversity,
  useFetchJobs,
  useAppliedJobs,
  useGetEnrollmentById,
  useUpdateEnrollmentComplete,
};
