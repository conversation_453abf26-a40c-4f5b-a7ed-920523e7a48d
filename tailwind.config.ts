/* eslint-disable @typescript-eslint/no-require-imports */
import type { Config } from 'tailwindcss'

export default {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-montserrat)', 'sans-serif'],
      },
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: {
          '50': 'var(--primary_50)',
          '100': 'var(--primary_100)',
          '200': 'var(--primary_200)',
          '300': 'var(--primary_300)',
          '400': 'var(--primary_400)',
          '500': 'var(--primary_500Main)',
          '600': 'var(--primary_600)',
          '700': 'var(--primary_700)',
          '800': 'var(--primary_800)',
          '900': 'var(--primary_900)',
        },
        neutral: {
          '50': 'var(--neutral_50)',
          '100': 'var(--neutral_100)',
          '200': 'var(--neutral_200)',
          '300': 'var(--neutral_300)',
          '400': 'var(--neutral_400)',
          '500': 'var(--neutral_500)',
          '600': 'var(--neutral_600)',
          '700': 'var(--neutral_700)',
          '800': 'var(--neutral_800)',
          '900': 'var(--neutral_900)',
        },
        gray: {
          '20': '#6C757D',
          '40': '#F0F2F4',
          '50': '#F9FAFB',
          '100': '#C8D0D9',
          '200': '#E5E7EB',
          '300': '#D1D5DB',
          '400': '#9CA3AF',
          '500': '#6B7280',
          '600': '#4B5563',
          '700': '#374151',
          '800': '#1F2937',
          '900': '#111827',
        },
        black: {
          '50': '#0000000',
          '100': '#495057',
          '200': '#1D506B',
        },
        blue: {
          '50': '#E6ECEF',
          '100': '#B4C5CE',
          '200': '#829EAD',
          '300': '#4F778C',
          '400': '#1D506B',
          '500': '#043C5B',
          '600': '#033049',
          '700': '#022437',
          '800': '#021E2E',
        },
        success: {
          '50': 'var(--success_50)',
          '100': 'var(--success_100)',
          '200': 'var(--success_200)',
          '300': 'var(--success_300)',
          '400': 'var(--success_400)',
          '500': 'var(--success_500Main)',
          '600': 'var(--success_600)',
          '700': 'var(--success_700)',
          '800': 'var(--success_800)',
          '900': 'var(--success_900)',
        },
        error: {
          '400': '#BB3D54',
          '500': '#8E0F33',
          '600': '#7A0A35',
        },
        destructive: {
          '50': 'var(--destructive_50)',
          '100': 'var(--destructive_100)',
          '200': 'var(--destructive_200)',
          '400': 'var(--destructive_400)',
          '500': 'var(--destructive_500Main)',
          '600': 'var(--destructive_600)',
          '700': 'var(--destructive_700)',
          '800': 'var(--destructive_800)',
          '900': 'var(--destructive_900)',
        },
        warning: {
          '50': 'var(--warning_50)',
          '100': 'var(--warning_100)',
          '200': 'var(--warning_200)',
          '300': 'var(--warning_300)',
          '400': 'var(--warning_400)',
          '500': 'var(--warning_500Main)',
          '600': 'var(--warning_600)',
          '700': 'var(--warning_700)',
          '800': 'var(--warning_800)',
          '900': 'var(--warning_900)',
        },
        lightGray: {
          '50': '#E9ECEF',
          '100': '#CED4DA',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar-hide'),
    require('@tailwindcss/typography'),
  ],
} satisfies Config
