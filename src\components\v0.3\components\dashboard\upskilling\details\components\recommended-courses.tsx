'use client';
import CourseCard from './CourseCard';
import useAiSkillDetailsStore from '@/zustand/store/ai/aiSkillDetailsStore';

function RecommendedCoursesCard() {
  const { skillDetails } = useAiSkillDetailsStore();
  const skillName = skillDetails?.skill;
  const courses = skillDetails?.courses.slice(0, 3) ?? [];

  return (
    <div className="space-y-2">
      <h3
        className="text-[18px] font-semibold leading-[28px]"
        style={{ color: 'var(--headingTextColor)' }}
      >
        Recommended {skillName} Courses
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {courses.map((course, idx) => (
          <CourseCard key={idx} course={course} />
        ))}
      </div>
    </div>
  );
}

export default RecommendedCoursesCard;
