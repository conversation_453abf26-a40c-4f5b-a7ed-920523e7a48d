'use client';

import LightIcon from '@/components/icons/LightIcon';
import ChevronArrowRight from '@/components/icons/svg/ChevronArrowRight';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import useAiSkillDetailsStore from '@/zustand/store/ai/aiSkillDetailsStore';
import { useRouter } from 'next/navigation';
import { Loader } from '@/components/common/Loader';
import { useQuery } from '@tanstack/react-query';
import aiService from '@/services/ai';

function SkillRecommendationCard({
  title,
  badgeTitle,
  description,
}: {
  title: string;
  badgeTitle?: string;
  description: string;
}) {
  const router = useRouter();
  const { setSkillDetails } = useAiSkillDetailsStore();

  // Use React Query for automatic caching and deduplication
  const {
    data: skillDetailsData,
    isLoading: isSkillsPending,
    isError: isSkillsError,
    refetch: generateSkillDetails,
  } = useQuery({
    queryKey: ['skillDetails', title],
    queryFn: () => aiService.generateUpskillingSkillDetails(title),
    enabled: false, // Don't auto-fetch, only on user click
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  const handleClick = () => {
    // If we already have data, navigate directly
    if (skillDetailsData?.data) {
      setSkillDetails(skillDetailsData.data);
      router.push(
        `/V03/dashboard/upskilling/recommendations/${encodeURIComponent(title)}`
      );
      return;
    }

    // Otherwise, fetch the data
    generateSkillDetails().then((result) => {
      if (result.data?.data) {
        setSkillDetails(result.data.data);
        router.push(
          `/V03/dashboard/upskilling/recommendations/${encodeURIComponent(title)}`
        );
      }
    }).catch((error) => {
      console.error('Failed to generate skill details:', error);
    });
  };

  return (
    <>
      {/* Full-screen loading overlay when generating skill details */}
      {isSkillsPending && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000]/40">
          <Loader />
          <p className="text-neutral-500 !text-white relative bottom-[20rem]">
            Generating upskilling plan for {title}...
          </p>
        </div>
      )}

      <Card
        onClick={handleClick}
        className={`bg-white border border-neutral-200 w-full rounded-[20px] p-6 flex items-center justify-between ${isSkillsPending ? 'opacity-80 pointer-events-none' : 'cursor-pointer'
          } hover:shadow-lg transition-all duration-300`}
      >
        <div className="space-y-2 flex-1">
          <div className="flex items-center gap-2">
            <h3
              className="text-[24px] font-semibold leading-[28px]"
              style={{ color: 'var(--headingTextColor)' }}
            >
              {title}
            </h3>
            {badgeTitle && (
              <Badge
                variant="blue"
                className="!text-white text-[12px] font-medium space-x-2 px-2 py-[2px]"
              >
                <LightIcon />
                {badgeTitle}
              </Badge>
            )}
          </div>
          <p
            className="text-[16px] font-normal"
            style={{ color: 'var(--bodyTextColor)' }}
          >
            {description}
          </p>

          {/* Show error state */}
          {isSkillsError && (
            <div className="text-sm text-red-600 mt-2">
              Failed to generate details. Please try again.
            </div>
          )}
        </div>

        <div className="flex-shrink-0">
          <ChevronArrowRight />
        </div>
      </Card>
    </>
  );
}

export default SkillRecommendationCard;
