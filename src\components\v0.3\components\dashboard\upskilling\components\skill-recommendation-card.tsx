'use client';

import LightIcon from '@/components/icons/LightIcon';
import ChevronArrowRight from '@/components/icons/svg/ChevronArrowRight';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { useGenerateUpskillingSkillDetails } from '@/mutations/aiMutation';
import useAiSkillDetailsStore from '@/zustand/store/ai/aiSkillDetailsStore';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import type { IUpskillingPlan } from '@/types/aiTypes';

function SkillRecommendationCard({
  title,
  badgeTitle,
  description,
}: {
  title: string;
  badgeTitle?: string;
  description: string;
}) {
  const router = useRouter();
  const {
    mutate: generateSkillDetails,
    isPending: isSkillsPending,
    isError: isSkillsError,
  } = useGenerateUpskillingSkillDetails();

  const { setSkillDetails } = useAiSkillDetailsStore();

  const handleClick = () => {
    try {
      generateSkillDetails(title, {
        onSuccess: (data: { data: IUpskillingPlan }) => {
          setSkillDetails(data?.data);
          router.push(
            `/V03/dashboard/upskilling/recommendations/${encodeURIComponent(title)}`
          );
        },
        onError: (error) => {
          console.error('Failed to generate skill details:', error);
        },
      });
    } catch (error) {
      console.error('Failed to generate skill details:', error);
    }
  };

  return (
    <>
      <Card
        onClick={handleClick}
        className={`bg-white border border-neutral-200 w-full rounded-[20px] p-6 flex items-center justify-between ${
          isSkillsPending ? 'opacity-80 pointer-events-none' : 'cursor-pointer'
        } hover:shadow-lg transition-all duration-300`}
      >
        <div className="space-y-2 flex-1">
          <div className="flex items-center gap-2">
            <h3
              className="text-[24px] font-semibold leading-[28px]"
              style={{ color: 'var(--headingTextColor)' }}
            >
              {title}
            </h3>
            {badgeTitle && (
              <Badge
                variant="blue"
                className="!text-white text-[12px] font-medium space-x-2 px-2 py-[2px]"
              >
                <LightIcon />
                {badgeTitle}
              </Badge>
            )}
          </div>
          <p
            className="text-[16px] font-normal"
            style={{ color: 'var(--bodyTextColor)' }}
          >
            {description}
          </p>

          {/* Show error state */}
          {isSkillsError && (
            <div className="text-sm text-red-600 mt-2">
              Failed to generate details. Please try again.
            </div>
          )}
        </div>

        <div className="flex-shrink-0">
          {isSkillsPending ? (
            <Loader2 className="animate-spin h-6 w-6 text-neutral-500" />
          ) : (
            <ChevronArrowRight />
          )}
        </div>
      </Card>
    </>
  );
}

export default SkillRecommendationCard;
