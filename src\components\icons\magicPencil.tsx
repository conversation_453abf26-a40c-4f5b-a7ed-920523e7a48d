import React from 'react';

interface MagicPencilProps {
  stroke?: string;
}

function MagicPencil({ stroke = '#043C5B' }: MagicPencilProps) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.6471 10.0967C12.6515 10.0752 12.6822 10.0752 12.6866 10.0967C12.9148 11.213 13.7872 12.0854 14.9035 12.3136C14.925 12.318 14.925 12.3487 14.9035 12.3531C13.7872 12.5813 12.9148 13.4537 12.6866 14.57C12.6822 14.5915 12.6515 14.5915 12.6471 14.57C12.4189 13.4537 11.5465 12.5813 10.4302 12.3531C10.4087 12.3487 10.4087 12.318 10.4302 12.3136C11.5465 12.0854 12.4189 11.213 12.6471 10.0967Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M9.87823 4.12128L3.04287 10.9566C2.52217 11.4773 2.52217 12.3216 3.04287 12.8423C3.56357 13.363 4.40779 13.363 4.92849 12.8423L11.7639 6.0069M9.87823 4.12128L11.0567 2.94277C11.5774 2.42207 12.4217 2.42207 12.9424 2.94277C13.4631 3.46347 13.4631 4.30769 12.9424 4.82839L11.7639 6.0069M9.87823 4.12128L11.7639 6.0069"
        stroke="#043C5B"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M3.08677 1.32883C3.15629 0.988697 3.64228 0.988698 3.7118 1.32883C3.86534 2.08005 4.45241 2.66712 5.20363 2.82066C5.54376 2.89018 5.54376 3.37617 5.20363 3.44569C4.45241 3.59923 3.86534 4.1863 3.7118 4.93752C3.64228 5.27765 3.15629 5.27765 3.08677 4.93752C2.93323 4.1863 2.34616 3.59923 1.59494 3.44569C1.25481 3.37617 1.25481 2.89018 1.59494 2.82066C2.34616 2.66712 2.93323 2.08005 3.08677 1.32883Z"
        fill="#043C5B"
      />
    </svg>
  );
}

export default MagicPencil;
