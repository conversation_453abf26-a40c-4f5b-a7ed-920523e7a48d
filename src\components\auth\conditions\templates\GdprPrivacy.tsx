import {
  ChangePrivacyPolicy,
  ChildrenPrivacy,
  Contact,
  LegalsGDPR,
  LinkOtherWebsites,
  ProtectionRights,
  RightGDPR,
} from '@/constants/PRIVACY_DEFINITION';
import Link from 'next/link';
import React from 'react';

function GDPRPrivacy() {
  return (
    <>
      <div>
        <h5 className="text-blue-300 text-[16px] font-bold mt-4">
          GDPR Privacy
        </h5>

        <h5 className="text-blue-300 text-[16px] font-medium mt-4 ">
          Legal Basis for Processing Personal Data under GDPR
        </h5>

        {LegalsGDPR.map((point) => (
          <div key={point.par} className="text-neutral-900 text-[16px]">
            {point.par && <p className="mt-2">{point.par}</p>}
            {point.par1 && <p className="mt-2">{point.par1}</p>}

            {point.title && point.def && (
              <ul className="list-disc list-inside mt-2 ml-10">
                <li>
                  <span className="font-semibold">{point.title}: </span>
                  {point.def}
                </li>
              </ul>
            )}
          </div>
        ))}
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4 ">
          Your Rights under the GDPR
        </h5>

        {RightGDPR.map((point) => (
          <div key={point.par} className="text-neutral-900 text-[16px]">
            {point.par && <p className="mt-2">{point.par}</p>}
            {point.par1 && <p className="mt-2">{point.par1}</p>}

            {point.title && point.def && (
              <ul className="list-disc list-inside mt-2 ml-10">
                <li>
                  <span className="font-semibold">{point.title}: </span>
                  {point.def}
                </li>
              </ul>
            )}
          </div>
        ))}
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Exercising of Your GDPR Data Protection Rights
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {ProtectionRights.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 mt-4">
                {item.par2}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Children`s Privacy
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {ChildrenPrivacy.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 mt-4">
                {item.par2}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Links to Other Websites
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {LinkOtherWebsites.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 mt-4">
                {item.par2}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Changes to this Privacy Policy
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {ChangePrivacyPolicy.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 mt-4">
                {item.par2}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 mt-4">
                {item.par3}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Contact Us
        </h5>

        {Contact.map((point) => (
          <div key={point.par1} className="text-neutral-900 text-[16px]">
            {point.par1 && <p className="mt-2">{point.par1}</p>}

            {point.title && point.def && (
              <ul className="list-disc list-inside mt-2">
                <li>
                  <span className="font-semibold">{point.title} </span>
                  {point.def && (
                    <Link
                      href={point.def}
                      className="text-blue-500 underline ml-1"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {point.def}
                    </Link>
                  )}
                </li>
              </ul>
            )}
          </div>
        ))}
      </div>
    </>
  );
}

export default GDPRPrivacy;
