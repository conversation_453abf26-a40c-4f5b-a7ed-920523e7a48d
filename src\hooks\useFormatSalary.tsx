import { useMemo } from 'react';

const useFormatSalary = () => {
  return useMemo(
    () =>
      (from?: number, to?: number, currency = '$') => {
        const formatNumber = (num: number) => num.toLocaleString();

        if (!from && !to) return 'Salary not specified';
        if (from && to)
          return `${currency} ${formatNumber(from)} - ${formatNumber(to)} per month`;
        return from
          ? `${currency}${formatNumber(from)} per month`
          : `Up to ${currency} ${formatNumber(to || 0)} per month`;
      },
    []
  );
};

export default useFormatSalary;
