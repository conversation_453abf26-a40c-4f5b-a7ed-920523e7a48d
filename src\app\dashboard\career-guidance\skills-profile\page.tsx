import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import SearchSkills from '@/components/dashboard/template/SearchSkills';
import React from 'react';

function ProfilePage() {
  return (
    <section className="py-10 px-10">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'Career Planner', href: '/dashboard' },
          {
            label: 'Skills Profile',
            href: '/dashboard/career-guidance/skills-profile',
          },
        ]}
      />

      <div className="bg-white w-[80%] mx-auto rounded-md mt-10 p-10">
        <h2 className="font-semibold leading-[32px] text-[20px] text-[#111827]">
          Skills Profile
        </h2>

        <p className="text-[15px] font-normal leading-[24px] text-[#646A77] mt-4">
          Skills combined from your uploaded CV and Study Path to deliver
          smarter, more accurate job recommendations.
        </p>

        <SearchSkills />
      </div>
    </section>
  );
}

export default ProfilePage;
