'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import FileUpload from '@/components/dashboard/career-jobs/job-application/JobAssistantMessage';
import type { Job } from '@/types/jobTypes';
import { useFetchJobs } from '@/queries';
import { Loader } from '@/components/common/Loader';

export default function JobApplicationPage() {
  const params = useParams();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  const { data: jobsData, isLoading } = useFetchJobs({
    page: 1,
  });

  const jobs = jobsData?.data || [];

  useEffect(() => {
    if (jobs.length > 0 && params.id) {
      const job = jobs.find((job) => job.id === params.id);
      if (job) {
        setSelectedJob(job);
      }
    }
  }, [params.id, jobs]);

  if (isLoading) {
    return (
      <section className="">
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
          <Loader />
        </div>
      </section>
    );
  }

  return (
    <section className="">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Career Opportunities',
            href: '/dashboard',
          },
          {
            label: 'Recommended Jobs',
            href: '/dashboard/career-opportunities/jobs',
          },
          {
            label: 'Apply for a Job',
            href: `/dashboard/career-opportunities/jobs/apply-for-a-job/${selectedJob?.id}`,
          },
        ]}
      />

      <div className="flex items-center justify-center p-4">
        <div className="w-full">
          <div className="relative">
            <h1 className="text-[28px] font-medium text-left mb-8 capitalize">
              Apply for {selectedJob?.title} -{' '}
              {selectedJob?.companyName || 'N/A'}
            </h1>
          </div>
        </div>
      </div>

      <FileUpload />
    </section>
  );
}
