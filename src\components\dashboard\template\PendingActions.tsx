'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

import { ArrowRight } from 'lucide-react';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';

const steps = [
  {
    id: 1,
    title: 'Upload missing documents',
    path: '/dashboard/pending-actions/upload-missing-document',
    description:
      'Employment contract and most recent payslip required for the National Savings Program.',
    date: '15 Jan 2025',
  },
  {
    id: 2,
    title: 'Take a career test',
    path: '/dashboard/pending-actions/upload-missing-document',
    description:
      'Discover your unique strengths and preferences to connect with roles that truly suit you.',
    date: '13 Jan 2025',
  },
  {
    id: 3,
    title: 'Upload your CV',
    path: '/dashboard/pending-actions/upload-missing-document',
    description:
      'Extract key skills to receive smarter and more accurate job recommendations.',
    date: '13 Jan 2025',
  },
  {
    id: 4,
    title: 'Build your study path',
    path: '/dashboard/pending-actions/upload-missing-document',
    description:
      'Design your education roadmap with expert guidance on courses and certifications to boost your career potential.',
    date: '13 Jan 2025',
  },
];

function PendingActions() {
  const router = useRouter();

  const handleRedirect = (path: string) => {
    router.push(path);
  };

  return (
    <div className="text-black w-[97%] mx-auto  duration-300">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'Pending Actions', href: '/dashboard/pending-actions' },
        ]}
      />

      <h1 className=" py-6 font-semibold text-[40px] leading-[48px]">
        You have 4 pending actions
      </h1>

      <ul className="mt-2 space-y-4 text-[16px]">
        {steps.map((step) => (
          <button
            key={step.id}
            onClick={() => handleRedirect(step.path)}
            className="flex border p-4 justify-between rounded-md w-full items-center border-neutral-200 cursor-pointer transition-all shadow-md bg-white"
          >
            <div>
              <p className="text-[20px] text-left leading-[28px] font-medium ">
                {step.title}
              </p>
              <p className="text-[16px] text-left  font-normal leading-[24px] text-neutral-500 py-1">
                {step.description}
              </p>
              <p className="text-[14px] text-left  font-normal leading-[20spx] text-neutral-500 ">
                {step.date}
              </p>
            </div>

            <ArrowRight
              onClick={() => handleRedirect(step.path)}
              className="cursor-pointer"
            />
          </button>
        ))}
      </ul>
      <p className="py-8 font-normal  text-[20px] leading-[16px] ">
        Need assistance?{' '}
        <span className="  font-semibold text-[20px] leading-[16px] text-primary-500">
          Contact Support
        </span>
      </p>
    </div>
  );
}

export default PendingActions;
