'use client';

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { AdminValue } from '@/types';

interface DropdownInputProps {
  selectedJobType?: string;
  selectedSalary: string;
  onFilterChange: (_jobType: string, _monthlyFrom: string) => void;
}

const DropdownInput: React.FC<DropdownInputProps> = ({
  selectedJobType,
  selectedSalary,
  onFilterChange,
}) => {
  const getJobType = useAdminValues({
    category: AdminValuesCategories?.jobType?.category,
  });
  const JobTypeList = getJobType.data?.data?.data?.customValues || [];

  return (
    <div className="flex flex-wrap gap-4 items-center justify-end w-full">
      <div className="flex-1">
        <Label
          htmlFor="jobType"
          className="text-[16px] font-medium leading-[16px] text-neutral-900"
        >
          Job Type:
        </Label>
        <Select
          value={selectedJobType}
          onValueChange={(value) => {
            onFilterChange(value, selectedSalary);
          }}
        >
          <SelectTrigger id="jobType" className="mt-3 rounded-[8px] h-[48px]">
            <SelectValue
              className="text-[16px] text-neutral-500 font-normal"
              placeholder="Please Select"
            />
          </SelectTrigger>
          <SelectContent>
            {JobTypeList.map((type: AdminValue, index: number) => (
              <SelectItem key={index} value={type?.value}>
                {type?.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Minimum Salary Input */}
      <div className="flex-1">
        <Label
          htmlFor="minSalary"
          className="text-[16px] font-medium leading-[16px] text-neutral-900"
        >
          Minimum <span className="hidden md:inline">Monthly</span> Salary:
        </Label>
        <input
          type="number"
          id="minSalary"
          value={selectedSalary}
          onChange={(e) => {
            const value = e.target.value || '';
            onFilterChange(selectedJobType ?? '', value);
          }}
          placeholder="Enter minimum salary"
          className="mt-3 p-2 border border-gray-300 w-full h-[48px] rounded-[8px]"
        />
      </div>
    </div>
  );
};

export default DropdownInput;
