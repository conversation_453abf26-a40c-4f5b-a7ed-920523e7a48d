import React from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import SpaceshipIcon from '@/components/icons/JobIcon';
import CvIcon from '@/components/icons/CvIcon';
import StudyIcon from '@/components/icons/StudyIcon';
import { useRouter } from 'next/navigation';
import useSettingsStore from '@/zustand/store/settingsStore';

function UpdateCareerSection() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;

  return (
    <div>
      <SectionHeader title="Update your career profile" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActionCard
          onClick={() => router.push('/dashboard/profile')}
          icon={<CvIcon stroke={brandColor} />}
          title={'Upload your CV'}
          description={
            'Upload your latest CV and refine your career profile for smarter job recommendations.'
          }
        />
        <ActionCard
          icon={<StudyIcon stroke={brandColor} />}
          onClick={() => router.push('/dashboard/career-guidance/study-path')}
          title={'Discover personalised learning paths'}
          description={
            'Find courses, certifications, and training programs that align with your career goals.'
          }
        />
        <ActionCard
          icon={<SpaceshipIcon stroke={brandColor} />}
          onClick={() => router.push('/dashboard/career-guidance/career-test')}
          title={'Take a career test'}
          description={
            'Take a self-assessment to identify strengths and uncover opportunities for growth.'
          }
        />
      </div>
    </div>
  );
}

export default UpdateCareerSection;
