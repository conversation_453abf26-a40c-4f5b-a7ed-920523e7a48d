import type { ReactNode } from 'react';

interface JourneyStepProps {
  number?: number;
  title?: string;
  description?: string;
  isLoading: boolean;
  children?: ReactNode;
  isLast?: boolean;
}

export default function JourneyStep({
  number,
  title,
  description,
  isLoading,
  children,
  isLast = false,
}: JourneyStepProps) {
  const hasTitle = <PERSON><PERSON>an(title);
  const hasDescription = Boolean(description);
  const hasHeaderContent = hasTitle || hasDescription;

  return (
    <div
      className={`relative pl-10 pb-10 ${!isLast ? 'border-l-2 border-primary-500' : ''}`}
    >
      <div className="absolute -left-5 top-0">
        <div className="w-10 h-10 bg-neutral-200 rounded-full flex items-center justify-center">
          <div className="w-8 h-8 rounded-full bg-white border-2 border-primary-500 text-primary-500 flex items-center justify-center text-sm font-semibold shadow-sm">
            {number}
          </div>
        </div>
      </div>
      <div
        className={`ml-2 ${
          !hasHeaderContent ? 'flex items-center min-h-[4rem]' : ''
        }`}
      >
        <div>
          {hasTitle && (
            <h3 className="text-lg font-semibold text-neutral-900">{title}</h3>
          )}
          {hasDescription && (
            <p className="text-[16px] text-primary-500 mb-4">{description}</p>
          )}

          {isLoading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-40 bg-primary-100 rounded-lg"></div>
            </div>
          ) : (
            children
          )}
        </div>
      </div>
    </div>
  );
}
