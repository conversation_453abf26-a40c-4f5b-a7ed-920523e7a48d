import React from 'react';

interface StepperProps {
  currentStep: number;
  steps: number;
}

const Stepper: React.FC<StepperProps> = ({ currentStep, steps }) => {
  return (
    <div className="flex items-center justify-center space-x-4">
      {Array.from({ length: steps }).map((_, index) => {
        return (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="flex items-center">
            <div
              className={`w-6 h-2 border rounded-full ${index <= currentStep
                  ? 'bg-primary-500 border-primary-500'
                  : 'bg-transparent border-gray-400'
                }`}
            ></div>
          </div>
        );
      })}
    </div>
  );
};

export default Stepper;
