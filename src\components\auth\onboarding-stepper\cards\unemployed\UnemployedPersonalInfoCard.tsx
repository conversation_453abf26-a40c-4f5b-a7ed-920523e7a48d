import React from 'react';

type Props = {
  employmentDetails: {
    status: string;
    jobStartDate: string;
    profession: string;
    category: string;
    monthlySalary: string;
    pensionableSalary: string;
  };
};

const UnemployedPersonalInfoCard: React.FC<Props> = ({ employmentDetails }) => {
  return (
    <div className="bg-white border rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-2">Name</h2>
      <div>
        <div className="py-2 space-y-2 mb-3">
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>First name:</span>
            <span className="font-medium text-gray-900">
              {employmentDetails.status}
            </span>
          </div>
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>Last name:</span>
            <span className="font-medium text-gray-900">
              {employmentDetails.jobStartDate}
            </span>
          </div>
        </div>
        <hr />
        {/* Salary Details */}
        <h2 className="text-lg font-medium text-gray-900 mt-4">Nationality</h2>
        <div className="py-2 space-y-2">
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>National ID:</span>
            <span className="font-medium text-gray-900">289123456789</span>
          </div>
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>Nationality:</span>
            <span className="font-medium text-gray-900">Country</span>
          </div>
        </div>
        <hr />
        <h2 className="text-lg font-medium text-gray-900 mt-4">Contact</h2>
        <div className="py-2 space-y-2">
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>Mobile:</span>
            <span className="font-medium text-gray-900">289123456789</span>
          </div>
          <div className="flex justify-between text-[16px] text-gray-600">
            <span>Email:</span>
            <span className="font-medium text-gray-900">
              <EMAIL>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnemployedPersonalInfoCard;
