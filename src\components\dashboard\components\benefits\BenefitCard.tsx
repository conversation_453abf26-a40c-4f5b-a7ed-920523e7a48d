import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ExternalLink } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

interface BenefitCardProps {
  banner?: string;
  title: string;
  description: string;
  amount: string;
  onClick?: () => void;
  onViewDetails?: () => void;
}

export const BenefitCard: React.FC<BenefitCardProps> = ({
  title,
  description,
  amount,
  banner = '/images/ws/people.png',
  onClick,
  onViewDetails,
}) => {
  return (
    <Card className="border rounded-lg shadow-md bg-white">
      <CardHeader className="p-0">
        <Image
          height={100}
          width={100}
          src={banner}
          quality={100}
          className="rounded-t-lg"
          style={{
            width: '100%',
            height: 'auto',
          }}
          alt={'benefit banner'}
          priority
        />
      </CardHeader>
      <CardContent className="space-y-2 p-4">
        <CardTitle className="text-[20px] font-semibold text-[--headingTextColor]">
          {title}
        </CardTitle>
        <CardDescription className="text-[16px] font-normal text-[--bodyTextColor]">
          {description}
        </CardDescription>
        <h6 className="mt-1 text-[16px] font-semibold">
          How much you&apos;ll receive:
        </h6>
        <div className="bg-success-50 rounded w-fit px-2 py-1">
          <p className="font-medium text-success-500 text-[16px]">{amount}</p>
        </div>
        <div className="text-[14px] font-normal text-neutral-900 space-y-1">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              className="rounded border border-gray-300 accent-primary-500"
            />
            <label htmlFor="id">I confirm my information is up to date</label>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              className="rounded border border-gray-300 accent-primary-500"
            />
            <label htmlFor="id">
              I have read and agree to the terms and conditions
            </label>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-3 px-4 pb-4 pt-0">
        <Button
          onClick={onClick}
          className="px-4 py-3 w-full font-medium text-[16px]"
        >
          Claim Benefit
        </Button>
        <Button
          onClick={onViewDetails}
          variant="outline"
          className="px-4 py-3 w-full font-medium  flex justify-center items-center gap-2"
        >
          View More Details <ExternalLink className="w-3 h-3" />
        </Button>
      </CardFooter>
    </Card>
  );
};
