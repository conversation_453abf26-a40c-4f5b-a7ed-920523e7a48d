
'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';

import RadarChart, {
  type RadarChartItem,
} from '@/components/charts/RadarChart';
import AssessmentsTabs from './assessmentResultTabs';
import { Loader } from '@/components/common/Loader';
import type { AssessmentScore } from '@/types';
import assessmentService from '@/services/assessments';
import { Card } from '@/components/ui/card';
import Text from '@/components/ui/text';
import Title from '@/components/ui/title';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import RetakeSvg from '@/components/icons/svg/RetakeSvgIcon';
import { DownloadIcon } from 'lucide-react';
import ProfileCompletionProcess from '@/components/dashboard/career-jobs/job-card/career-profile-completeness/ProfileCompleteness';

const COLORS = [
  '#022437',
  '#033049',
  '#043C5B',
  '#1D506B',
  '#4F778C',
  '#829EAD',
];

const AssessmentsResultsPage: React.FC = () => {
  const router = useRouter();
  // const searchParams = useSearchParams();
  // const { user } = useAuthStore();

  // State management
  const [assessmentScores, setAssessmentScores] = useState<AssessmentScore[]>(
    []
  );
  const [isLoading, setIsLoading] = useState({
    scores: false,
    revoke: false,
    export: false,
  });
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  // const [profileCompleted, setProfileCompleted] = useState(false);

  const chartData = useMemo<RadarChartItem[]>(() => {
    return assessmentScores.map((item, index) => ({
      category: item.name,
      value: item.score,
      columnSettings: {
        fill: COLORS[index % COLORS.length],
      },
    }));
  }, [assessmentScores]);

  const topThreeInterests = useMemo(() => {
    return assessmentScores.slice(0, 3);
  }, [assessmentScores]);

  const fetchAssessmentData = async () => {
    try {
      setIsLoading((prev) => ({ ...prev, scores: true }));
      const scores = await assessmentService.getAssessmentScores();
      setAssessmentScores(scores.sort((a, b) => b.score - a.score));
    } catch (error) {
      console.error('Failed to fetch assessment scores:', error);
    } finally {
      setIsLoading((prev) => ({ ...prev, scores: false }));
    }
  };

  // Data fetching
  useEffect(() => {
    fetchAssessmentData();
  }, []);

  // Handlers
  const handleJobRecommendation = () => {
    router.push('/dashboard/career-opportunities/jobs');
  };

  const handleProfileComplete = () => {
    // setProfileCompleted(true);
  };

  const handleRetakeTest = async () => {
    try {
      setIsLoading((prev) => ({ ...prev, revoke: true }));
      await assessmentService.revokeAssessment();
      window.location.replace(
        '/dashboard/career-guidance/career-test/introduction'
      );
    } catch (error) {
      console.error('Failed to revoke assessment:', error);
    } finally {
      setIsLoading((prev) => ({ ...prev, revoke: false }));
    }
  };

  const handleExportResults = async () => {
    try {
      setIsLoading((prev) => ({ ...prev, export: true }));
      const pdfBlob = await assessmentService.exportTestResults();
      downloadPDF(pdfBlob, 'Test Results');
    } catch (error) {
      console.error('Failed to export results:', error);
    } finally {
      setIsLoading((prev) => ({ ...prev, export: false }));
    }
  };

  const downloadPDF = (blob: Blob, filename: string) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  if (isLoading.scores) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader />
      </div>
    );
  }

  return (
    <div className="py-4 min-h-full md:py-5 md:px-6 lg:p-2 w-full">
      <Card className="rounded-md p-4">
        <div className="flex flex-wrap mb-4 md:justify-between md:items-center md:mb-6">
          <Title
            className="!text-blue-400 w-full mb-4 md:mb-0 md:w-auto"
            variant="h3"
          >
            Career Test Results
          </Title>

          <div className="flex">
            <Button
              className="mr-5 border-2"
              variant="outline"
              onClick={() => setIsResetDialogOpen(true)}
              disabled={isLoading.revoke}
            >
              {isLoading.revoke ? (
                <p> Loading...</p>
              ) : (
                <>
                  <RetakeSvg />
                  Retake Test
                </>
              )}
            </Button>

            <Button
              variant="outline"
              className="border-2"
              onClick={handleExportResults}
              disabled={isLoading.export}
            >
              {isLoading.export ? (
                <p>Loading...</p>
              ) : (
                <>
                  <DownloadIcon />
                  Download Report
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <Text className="text-neutral-700 font-semibold mb-3" variant="large">
            Here are your Interest Assessment results!
          </Text>
          <Text className="mb-3 text-neutral-600">
            Think of your interests as work you like to do.
          </Text>
          <Text className="mb-3 text-neutral-600">
            Your interests can help you find careers you might like to explore.
            The more a career meets your interests, the more likely it will be
            satisfying and rewarding to you.
          </Text>

          {topThreeInterests.length > 0 && (
            <Text className="text-neutral-600 lg:mb-3">
              Your Top 3 interests are: <br />
              {topThreeInterests.map((interest, index) => (
                <React.Fragment key={interest.id}>
                  &nbsp;&nbsp; {index + 1}. {interest.name}: {interest.score}{' '}
                  <br />
                </React.Fragment>
              ))}
            </Text>
          )}

          <Text className="hidden text-neutral-600 lg:block">
            You can click on any interest below to learn more.
          </Text>
        </div>

        <div className="flex flex-wrap mt-4 md:mt-6">
          <div className="w-full order-3 xl:w-1/2 xl:order-1">
            <Card className="bg-neutral-50 rounded-md px-2 py-6">
              <AssessmentsTabs assessmentScores={assessmentScores} />
            </Card>
          </div>

          <div className="w-full order-2 my-4 block md:my-6 xl:hidden">
            <Text className="text-neutral-600">
              You can click on any interest below to learn more.
            </Text>
          </div>

          <div className="flex w-full order-1 xl:w-1/2 xl:order-2 items-center justify-center">
            <div className="w-full p-4 md:p-6">
              <Title
                className="text-center !text-blue-400 mb-6 md:mb-8 lg:mb-10"
                variant="h2"
              >
                Your Results
              </Title>

              {chartData.length > 0 && <RadarChart data={chartData} />}

              <button
                onClick={handleJobRecommendation}
                className="mx-auto mt-10 flex justify-center items-center bg-primary-500 h-[48px] py-[14px] px-[28px] text-white rounded-full text-[18px]"
              >
                Show job recommendations
              </button>
            </div>
          </div>
        </div>
      </Card>

      <ProfileCompletionProcess
        showSearchInput={false}
        onComplete={handleProfileComplete}
      />

      {/* Reset Confirmation Dialog */}
      <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-blue-400">
              Retake Assessment
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to retake the assessment? Your current
              results will be lost.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsResetDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRetakeTest}
              className="text-white"
              disabled={isLoading.revoke}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AssessmentsResultsPage;
