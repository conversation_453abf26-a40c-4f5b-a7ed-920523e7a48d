import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import React from 'react';
import InterviewWarmp from '@/assets/images/dashboard/interviewWarmup.svg';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ExternalLinkIcon } from 'lucide-react';

function InterviewWarmup() {
  const handleWarmpRedirect = (): void => {
    window.open(
      'https://www.cloudskillsboost.google/interview_warmup',
      '_blank',
      'noopener,noreferrer'
    );
  };

  return (
    <Card className="mx-auto bg-neutral-50 border border-neutral-200 rounded-[28px] py-[32px] px-[44px]  my-6 justify-center items-center">
      <Card className="bg-transparent mx-auto flex flex-col space-y-2">
        <Image className="" src={InterviewWarmp} alt="warmpup" />
        <CardContent className="mx-auto text-[18px] font-normal leading-[28px] text-[--bodyTextColor] text-left">
          Get ready to ace your next interview! Practice answering
          industry-specific questions, review your responses, and improve your
          skills in a no-pressure environment.
        </CardContent>

        <CardFooter>
          <Button
            onClick={handleWarmpRedirect}
            className="h-[48px] py-[14px] px-[28px] w-full mt-5 cursor-pointer text-[16px] font-medium"
            variant="outline"
          >
            Start Practicing Now <ExternalLinkIcon />
          </Button>
        </CardFooter>
      </Card>
    </Card>
  );
}

export default InterviewWarmup;
