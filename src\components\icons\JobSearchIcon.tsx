import React from 'react';

interface JobSearchIconProps {
  stroke?: string;
}

function JobSearchIcon({ stroke = 'var(--primary-500)' }: JobSearchIconProps) {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M31.5 31.5L26.523 26.523M26.523 26.523C27.7445 25.3015 28.5 23.614 28.5 21.75C28.5 18.0221 25.4779 15 21.75 15C18.0221 15 15 18.0221 15 21.75C15 25.4779 18.0221 28.5 21.75 28.5C23.614 28.5 25.3015 27.7445 26.523 26.523ZM42 24C42 33.9411 33.9411 42 24 42C14.0589 42 6 33.9411 6 24C6 14.0589 14.0589 6 24 6C33.9411 6 42 14.0589 42 24Z"
        stroke={stroke}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default JobSearchIcon;
