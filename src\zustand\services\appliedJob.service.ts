import axiosClient from '@/utils/axiosClient';
import type { AppliedJob } from '@/types/jobTypes';

const API_URL = '/jobs/applicants';

const appliedJobService = {
  getAppliedJobs: async ({
    page = 1,
    size = 10,
    search = '',
    location = '',
    jobType = '',
    monthlyFrom = '',
    userId = '',
    status = '',
    ascending = false,
  }: {
    page?: number;
    size?: number;
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: string;
    userId?: string;
    status?: string;
    ascending?: boolean;
  }): Promise<{
    data: AppliedJob[];
    page: number;
    size: number;
    total: number;
  }> => {
    try {
      const response = await axiosClient.get<{
        data: AppliedJob[];
        page: number;
        size: number;
        total: number;
      }>(API_URL, {
        params: {
          page,
          size,
          search,
          location,
          jobType,
          monthlyFrom,
          userId,
          status,
          ascending,
        },
      });

      return {
        ...response.data,
        data: response.data.data.map((job) => ({
          ...job,
          job: {
            ...job.job,
            companyName: job.job.companyName ?? 'Unknown Company',
          },
        })),
      };
    } catch (error) {
      console.error('Error fetching jobs:', error);
      throw new Error('Failed to fetch jobs');
    }
  },

  updateAppliedJob: async (
    id: string,
    updatedJob: Partial<AppliedJob>
  ): Promise<AppliedJob> => {
    try {
      const response = await axiosClient.put<{ data: AppliedJob }>(
        `${API_URL}/${encodeURIComponent(id)}`,
        updatedJob
      );
      return response.data.data;
    } catch (error) {
      console.error(`Error updating job with ID ${id}:`, error);
      throw new Error('Failed to update job');
    }
  },

  deleteAppliedJob: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`${API_URL}/${encodeURIComponent(id)}`);
    } catch (error) {
      console.error(`Error deleting job with ID ${id}:`, error);
      throw new Error('Failed to delete job');
    }
  },
};

export default appliedJobService;
