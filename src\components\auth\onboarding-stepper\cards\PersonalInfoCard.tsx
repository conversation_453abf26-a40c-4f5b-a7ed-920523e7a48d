/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import UpdateModal from '@/components/modals/UpdateModal';
import { Button } from '@/components/ui/button';
import type React from 'react';
import { useState } from 'react';

type Props = {
  employmentDetails: {
    first_name: string;
    last_name: string;
    status: string;
    jobStartDate: string;
    profession: string;
    category: string;
    monthlySalary: string;
    pensionableSalary: string;
  };
};

const PersonalInfoCard: React.FC<Props> = ({ employmentDetails }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitUpdate = (data: any) => {
    // Handle the update request submission
    console.warn('Personal info update requested:', data);
    // You would typically send this data to your backend
  };

  return (
    <div className="bg-white text-left px-7 pb-4 rounded-lg">
      <h2 className="text-[20px] font-semibold text-neutral-900 leading-[28px] mt-6">
        Name
      </h2>
      <div>
        {/* Employment Details */}
        <div className="py-2 space-y-3 mb-3">
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              First name:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.first_name}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Last name:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.last_name}
            </span>
          </div>
        </div>
        <hr />
        {/* Salary Details */}
        <h2 className="text-[20px] font-semibold text-neutral-900 leading-[28px] mt-6">
          Nationality
        </h2>
        <div className="py-2 space-y-3">
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              National ID:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              289123456789
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Nationality:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Dubai
            </span>
          </div>
        </div>
        <hr />
        <h2 className="text-[20px] font-semibold text-neutral-900 leading-[28px] mt-6">
          Contact
        </h2>
        <div className="py-2 space-y-3">
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Mobile:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              +965 5000 1234
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Email:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              <EMAIL>
            </span>
          </div>
        </div>

        <Button
          className="w-full py-[14px] text-[18px] font-semibold mt-4"
          variant="outline"
          onClick={handleOpenModal}
        >
          My details require updating
        </Button>
      </div>

      <UpdateModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitUpdate}
        type="personal"
      />
    </div>
  );
};

export default PersonalInfoCard;
