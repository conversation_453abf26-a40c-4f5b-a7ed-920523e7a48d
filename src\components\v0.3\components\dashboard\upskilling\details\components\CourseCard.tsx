import Image from 'next/image';
import { formatUSD } from '@/utils';
import { Button } from '@/components/ui/button';
import { CheckIcon } from 'lucide-react';
import type { ICourse } from '@/types/aiTypes';

interface CourseCardProps {
  course: ICourse;
  featured?: boolean;
  completed?: boolean;
}

export default function CourseCard({
  course,
  featured = false,
  completed = false,
}: CourseCardProps) {
  const formattedPrice =
    typeof course?.price !== 'number'
      ? course?.price
      : formatUSD(Number(course.price) || 0);

  const getExperienceLevelColor = () => {
    switch (course.level?.toLowerCase()) {
      case 'beginner':
        return 'bg-success-100 text-neutral-900 font-medium';
      case 'intermediate':
        return 'bg-warning-100 text-neutral-900 font-medium';
      case 'advanced':
        return 'bg-destructive-100 text-neutral-900 font-medium';
      default:
        return 'bg-neutral-900 text-white font-medium';
    }
  };

  // Generate unique image URL using course ID or title as seed
  const getUniqueImageUrl = () => {
    const seed = course.title || Math.random().toString(36).substring(7);
    const width = featured ? 1200 : 400;
    const height = featured ? 400 : 240;
    return `https://picsum.photos/seed/${seed}/${width}/${height}`;
  };

  return (
    <div
      className={`bg-white rounded-lg overflow-hidden shadow-xs border border-neutral-50 hover:shadow-sm transition-shadow ${featured ? 'h-full' : ''}`}
    >
      <div className="p-4 space-y-3">
        <div className="relative">
          <Image
            src={getUniqueImageUrl()}
            alt={course?.title}
            width={featured ? 1200 : 400}
            height={featured ? 400 : 240}
            className={`w-full ${featured ? 'h-64' : 'h-48'} object-cover rounded-md`}
          />
        </div>

        <div className="space-y-3">
          <div className="space-y-1">
            <h3 className="font-semibold text-[18px]  line-clamp-2 leading-[24px] text-neutral-900">
              {course.title}
            </h3>
            <p className="text-neutral-500 text-[16px] font-normal leading-[24px]">
              {course.provider || 'Training Provider Name'}
            </p>
          </div>

          <div className="space-y-2">
            <div className="items-center space-x-2">
              <span
                className={`px-[6px] py-[2px] rounded-[8px] text-[12px] font-medium  ${getExperienceLevelColor()}`}
              >
                {course.level || 'N/A'}
              </span>
              <span className="bg-neutral-100 px-[6px] py-[2px] rounded-[8px] text-[12px] font-medium  text-neutral-900">
                {course.mode || 'N/A'}
              </span>
            </div>

            <div className="flex text-[16px] font-medium space-x-2 text-[--bodyTextColor]">
              <div className="bg-neutral-100 px-[6px] py-[2px] rounded-[8px] text-[12px] font-medium">
                Next Start: {course.nextStartDate || 'N/A'}
              </div>
              <div className="flex items-center">
                <span className="bg-neutral-100 px-[6px] py-[2px] rounded-[8px] text-[12px] font-medium">
                  {course?.duration}
                </span>
              </div>
            </div>
          </div>
          <div className="font-semibold text-[18px] text-[--bodyTextColor] leading-[28px]">
            {formattedPrice}
          </div>
        </div>
      </div>

      {completed && (
        <div className="flex justify-center pb-4 px-4">
          <Button variant="outline" className="w-full">
            <CheckIcon className="mr-2 h-4 w-4 text-[--buttonColor]" />
            Completed
          </Button>
        </div>
      )}
    </div>
  );
}
