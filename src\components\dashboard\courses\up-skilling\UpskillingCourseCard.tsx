import { Button } from '@/components/ui/button';
import type { Course } from '@/types/courseType';
import Image from 'next/image';
import { CheckCircle } from 'lucide-react';

interface UpskillingCourseCardProps {
  course: Course;
  isCompleted: boolean;
}

export default function UpskillingCourseCard({
  course,
  isCompleted,
}: UpskillingCourseCardProps) {
  const mockPrices = {
    USD: isCompleted ? '$300' : Math.random() > 0.5 ? '$599' : '$1,000',
  };

  const mockDuration = Math.floor(Math.random() * 10) + 1;

  return (
    <div className="border rounded-lg overflow-hidden bg-white">
      <div className="relative h-40 w-full">
        <Image
          src={
            course.coverImage ||
            '/placeholder.svg?height=160&width=320&query=course'
          }
          alt={course.name || 'Course image'}
          fill
          className="object-cover"
        />
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-lg mb-1">{course.name}</h3>
        <p className="text-sm text-gray-600 mb-3">
          {course.description?.substring(0, 60)}...
        </p>

        <div className="flex justify-between items-center mb-3">
          <div>
            <div className="font-bold text-lg">{mockPrices.USD}</div>
            <div className="text-xs text-gray-500">Beginner</div>
          </div>

          {isCompleted ? (
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-5 w-5 mr-1" />
              <span>Completed</span>
            </div>
          ) : (
            <Button size="sm" variant="outline">
              Enroll Now
            </Button>
          )}
        </div>

        <div className="flex justify-between text-xs text-gray-500 border-t pt-2">
          <span>Open Until 31 Dec 2023</span>
          <span>{mockDuration} Months</span>
        </div>
      </div>
    </div>
  );
}
