'use client';

import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { ChevronLeft, ChevronRight } from 'lucide-react';

type PaginationControlsProps = {
  page: number;
  totalPages: number;
  handlePageChange: (_page: number) => void;
};

export function PaginationControls({
  page,
  totalPages,
  handlePageChange,
}: PaginationControlsProps) {
  const getPageRange = () => {
    const range = [];
    const maxPagesToShow = 3;

    let start = Math.max(2, page - 1);
    let end = Math.min(totalPages - 1, page + 1);

    if (page <= maxPagesToShow) {
      start = 2;
      end = Math.min(maxPagesToShow + 1, totalPages - 1);
    } else if (page >= totalPages - maxPagesToShow + 1) {
      start = Math.max(totalPages - maxPagesToShow, 2);
      end = totalPages - 1;
    }

    for (let i = start; i <= end; i++) {
      range.push(i);
    }

    return range;
  };

  const pageRange = getPageRange();

  return (
    <div className="flex justify-center w-full mt-4">
      <Pagination>
        <PaginationContent>
          {/* Previous Button */}
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageChange(Math.max(page - 1, 1))}
              className={`w-8 h-8 flex items-center justify-center rounded-full border transition 
                ${page === 1
                  ? 'pointer-events-none opacity-50 !text-primary-500'
                  : 'rounded-full border border-primary-500  !text-primary-500 hover:bg-primary-400'
                }`}
            >
              <ChevronLeft className="h-4 w-4 text-primary-500" />
            </PaginationPrevious>
          </PaginationItem>

          {/* First Page */}
          <PaginationItem>
            <PaginationLink
              isActive={page === 1}
              onClick={() => handlePageChange(1)}
              className={`w-8 h-8 flex items-center justify-center rounded-full 
                ${page === 1
                  ? 'rounded-full border border-primary-500 !text-white w-8 h-8 flex items-center justify-center bg-primary-500 hover:bg-primary-500'
                  : '!text-primary-500 w-8 h-8 flex items-center justify-center rounded-full border border-transparent hover:border-primary-500  hover:bg-primary-400'
                }`}
            >
              1
            </PaginationLink>
          </PaginationItem>

          {pageRange[0] > 2 && (
            <PaginationItem>
              <span className="!text-primary-500 px-2">...</span>
            </PaginationItem>
          )}

          {pageRange.map((pageNum) => (
            <PaginationItem key={`page-${pageNum}`}>
              <PaginationLink
                isActive={pageNum === page}
                onClick={() => handlePageChange(pageNum)}
                className={`w-8 h-8 flex items-center justify-center rounded-full 
                  ${pageNum === page
                    ? 'rounded-full border border-primary-500 !text-white w-8 h-8 flex items-center justify-center bg-primary-500 hover:bg-primary-500'
                    : '!text-primary-500 w-8 h-8 flex items-center justify-center rounded-full border border-transparent hover:border-primary-500  hover:bg-primary-400'
                  }`}
              >
                {pageNum}
              </PaginationLink>
            </PaginationItem>
          ))}

          {pageRange[pageRange.length - 1] < totalPages - 1 && (
            <PaginationItem>
              <span className="!text-primary-500 px-2">...</span>
            </PaginationItem>
          )}

          {totalPages > 1 && (
            <PaginationItem>
              <PaginationLink
                isActive={page === totalPages}
                onClick={() => handlePageChange(totalPages)}
                className={`w-8 h-8 flex items-center justify-center rounded-full 
                  ${page === totalPages
                    ? 'rounded-full border border-primary-500 !text-white w-8 h-8 flex items-center justify-center bg-primary-500 hover:bg-primary-500'
                    : '!text-primary-500 w-8 h-8 flex items-center justify-center rounded-full border border-transparent hover:border-primary-500  hover:bg-primary-400'
                  }`}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageChange(Math.min(page + 1, totalPages))}
              className={`w-8 h-8 flex items-center justify-center rounded-full border transition 
                ${page === totalPages
                  ? 'pointer-events-none opacity-50 !text-primary-500 border-none'
                  : 'rounded-full border !border-primary-500 text-primary-500 hover:bg-primary-400'
                }`}
            >
              <ChevronRight className="h-4 w-4 !text-primary-500" />
            </PaginationNext>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
