import axiosClient from '@/utils/axiosClient';

const API_URL = '/user/';

class UserService {
  async getCvs(userId: string) {
    const response = await axiosClient.get(`${API_URL}${userId}/cv`);
    return response.data;
  }
  async uploadCv({
    cv,
    userId,
  }: {
    cv: { FileName: string; Base64Content: string };
    userId: string;
  }) {
    const response = await axiosClient.post(
      `${API_URL}${userId}/cv/upload`,
      cv
    );
    return response.data;
  }
  async deleteCv({ id, userId }: { id: string; userId: string }) {
    const response = await axiosClient.delete(`${API_URL}${userId}/cv/${id}`);
    return response.data;
  }

  async updateUser({
    userId,
    data,
  }: {
    userId: string;
    data: {
      id?: string;
      userName?: string;
      employmentStatus?: string;
      email?: string;
      phoneNumber?: string;
    };
  }) {
    const response = await axiosClient.put(`/users/${userId}`, data);
    return response.data;
  }
}

const userService = new UserService();
export default userService;
