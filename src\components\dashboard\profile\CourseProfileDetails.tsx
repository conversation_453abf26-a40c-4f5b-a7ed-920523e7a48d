/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useGetEnrollmentById } from '@/queries';
import CourseBookSvg from '@/components/icons/CourseBookSvg';
import { MoreVerticalIcon } from 'lucide-react';
import CourseOptionsModal from './CourseOptionDialog';

function CourseProfile() {
  const router = useRouter();
  const userId = useAuthStore((state) => state.user?.id);
  const { data, isLoading, error } = useGetEnrollmentById(
    userId || '',
    !!userId
  );
  const enrollments = data?.data || [];

  const [activeEnrollment, setActiveEnrollment] = useState<any | null>(null);

  const handleBrowseCourses = () => {
    router.push('/dashboard/upskilling/all-courses');
  };

  const handleToggleOptions = (enrollment: any) => {
    setActiveEnrollment((prev: any) => {
      return prev?.enrollmentDetails?.id === enrollment.enrollmentDetails.id
        ? null
        : enrollment;
    });
  };

  return (
    <div className="border border-neutral-200 rounded-md p-4 mt-4">
      <div className="flex flex-col space-y-4">
        <h1 className="text-[20px] font-semibold leading-[28px] text-neutral-900">
          My Courses
        </h1>

        {isLoading ? (
          <p className="text-[16px] text-neutral-500">Loading courses...</p>
        ) : error ? (
          <p className="text-[16px] text-destructive-500">
            Failed to load courses.
          </p>
        ) : enrollments.length > 0 ? (
          <div className="list-inside text-[16px] text-neutral-700 space-y-6">
            {enrollments.map((enrollment: any) => (
              <div
                key={enrollment.enrollmentDetails.id}
                className="relative group"
              >
                <div className="flex items-center justify-between">
                  <div className="flex flex-col gap-1 text-[18px] font-semibold leading-[28px] text-neutral-700">
                    <div className="flex items-center gap-2">
                      <CourseBookSvg />
                      {enrollment.courseDetails?.name || 'Untitled Course'}
                    </div>
                    <p className="text-[16px] font-normal text-neutral-500 ml-8">
                      {enrollment.courseDetails?.TrainingProvider ||
                        'No Training Provider Available'}{' '}
                      |{' '}
                      {enrollment.enrollmentDetails?.isCompleted
                        ? 'Completed'
                        : 'Pending'}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleOptions(enrollment);
                    }}
                    className="border rounded-full px-2.5 py-0 items-center border-neutral-200"
                  >
                    <MoreVerticalIcon className="text-neutral-400 hover:text-neutral-600 cursor-pointer" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <p className="text-[16px] text-neutral-500 font-normal leading-[24px]">
              There are no courses added yet.
            </p>

            <Button
              variant={'outline'}
              className="mt-3 h-[48px] py-[14px] px-[28px] text-[16px] w-[200px]"
              onClick={handleBrowseCourses}
            >
              Browse Courses
            </Button>
          </>
        )}

        {activeEnrollment && (
          <div className="absolute right-0 z-50">
            <CourseOptionsModal
              enrollment={activeEnrollment}
              onClose={() => setActiveEnrollment(null)}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseProfile;
