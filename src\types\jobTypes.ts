export interface Job {
  id: string;
  title: string;
  description?: string;
  location?: string;
  monthlyFrom?: number;
  monthlyTo?: number;
  currency?: string;
  postedDate?: string;
  expiryDate?: string;
  companyName?: string;
  jobType?: string;
  jobMode?: string;
  jobPosition?: string;
  industry?: string;
  postedAt?: string;
  startDate?: string;
  companyLogo?: string;
  company: {
    id: string;
    name: string;
    logo: string;
  };
  status?: 'Active' | 'Closed';
}

export interface JobState {
  jobs: Job[];
  isLoading: boolean;
  currentJob: Job | null;
  page: number;
  size: number;
  total: number;
  searchTerm: string;
  location: string;
  jobType: string;
  monthlyFrom: string;
  status?: 'Active' | 'Closed';
}

export type JobAction =
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' }
  | { type: 'SET_JOBS'; payload: Job[] }
  | { type: 'ADD_JOB'; payload: Job }
  | { type: 'UPDATE_JOB'; payload: Job }
  | { type: 'DELETE_JOB'; payload: string };

export interface AppliedJob {
  id: string;
  userId: string;
  jobId: string;
  appliedDate: string;
  status: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  attributes: any[];
  cv: string | null;
  job: {
    id: string;
    title: string;
    description: string;
    location: string;
    monthlyFrom: number;
    monthlyTo: number;
    expiryDate: string;
    postedDate: string;
    jobType: string;
    jobMode: string;
    jobPosition: string;
    industry: string;
    experienceLevel: string;
    companyName: string;
    companyLogo?: string;
    currency: string;
    createdById: string;
    lastModifiedById: string;
    company: {
      id: string;
      name: string;
      logo: string;
    };
  };
  user: {
    employmentStatus?: string | null;
    educationLevel: string | null;
    experienceLevel: string | null;
    industry: string | null;
    specialization: string;
    subSpecialization: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    attributes: any[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    cVs: any[];
    id: string;
    userName: string;
    normalizedUserName: string;
    email: string;
    normalizedEmail: string;
    emailConfirmed: boolean;
    passwordHash: string;
    securityStamp: string;
    concurrencyStamp: string;
    phoneNumber: string | null;
    phoneNumberConfirmed: boolean;
    twoFactorEnabled: boolean;
    lockoutEnd: string | null;
    lockoutEnabled: boolean;
    accessFailedCount: number;
  };
}

export interface AppliedJobState {
  jobs: AppliedJob[];
  currentJob: AppliedJob | null;
  isLoading: boolean;
  page: number;
  size: number;
  total: number;
  searchTerm: string;
  location: string;
  jobType: string;
  monthlyFrom: string;
  ascending?: boolean;
}
