import React from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import CvIcon from '@/components/icons/CvIcon';
import StudyIcon from '@/components/icons/StudyIcon';
import { useRouter } from 'next/navigation';
import useSettingsStore from '@/zustand/store/settingsStore';

function DevelopCareerSection() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  const router = useRouter();

  return (
    <div>
      <SectionHeader title="Develop your career profile" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActionCard
          icon={<StudyIcon stroke={brandColor} />}
          onClick={() => router.push('/dashboard/career-guidance/career-test')}
          title={'Take a career test'}
          description={
            'Discover your strengths and preferences to find roles that suit you best.'
          }
        />
        <ActionCard
          onClick={() => router.push('/dashboard/career-profile')}
          icon={<CvIcon stroke={brandColor} />}
          title={'Upload your CV'}
          description={
            'Highlight key skills from your CV for smarter job recommendations.'
          }
        />
        <ActionCard
          icon={<StudyIcon stroke={brandColor} />}
          onClick={() =>
            router.push('/dashboard/career-guidance/program-builder')
          }
          title={'Build your study path'}
          description={
            'Create an education roadmap with expert guidance on courses and certifications.'
          }
        />
      </div>
    </div>
  );
}

export default DevelopCareerSection;
