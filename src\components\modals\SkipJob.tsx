'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface SkipJobModalProps {
  open: boolean;
  handleOpen: () => void;
}

const SkipJobModal: React.FC<SkipJobModalProps> = ({ open, handleOpen }) => {
  return (
    <Dialog open={open} onOpenChange={handleOpen}>
      <DialogContent className="sm:max-w-[600px] shadow-none">
        <DialogHeader>
          <DialogTitle className="text-left">
            <div className="flex flex-col gap-3">
              <h4 className="font-[600] text-[28px]">Skip</h4>
              <p className="!text-neutral-500 font-[400] text-[18px]">
                Let us know why so we can improve your job matches
              </p>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 rounded-[8px]"
              >
                Not relevant to my interests
              </Button>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 rounded-[8px]"
              >
                Salary is too low
              </Button>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 rounded-[8px]"
              >
                Location doesn’t work for me
              </Button>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 rounded-[8px]"
              >
                Not ideal working arrangement
              </Button>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 rounded-[8px]"
              >
                Other
              </Button>
              <Button
                onClick={handleOpen}
                variant="outline"
                className="w-full px-[28px] py-[14px] h-[48px] !border-1 border-neutral-300 bg-transparent hover:bg-transparent !text-neutral-700 mt-3"
              >
                Cancel
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default SkipJobModal;
