'use client';
import { InfoIcon } from 'lucide-react';
import AiMagicStar from '@/assets/images/dashboard/aiMagicStar.svg';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import MagicPencil from '@/components/icons/magicPencil';
import useSettingsStore from '@/zustand/store/settingsStore';
import type { IJobMatch } from '@/types';
import { getCompanyLogo } from '@/utils/jobImageUtils';

interface JobDetailsPropsType {
  handleApply: () => void;
  job: IJobMatch | null;
}

const responsibilities = [
  'Collect, process, and interpret structured and unstructured data',
  'Design and maintain dashboards, reports, and metrics',
  'Collaborate with cross-functional teams to support product decisions',
  'Apply statistical techniques to identify trends and generate insights',
  'Present findings in a clear, visual, and impactful way',
];

const JobDetail: React.FC<JobDetailsPropsType> = ({ job, handleApply }) => {
  const { appearanceSettings } = useSettingsStore();

  const strokeColor = appearanceSettings?.buttonColor;

  return (
    <div className="w-full p-8 gap-8 inline-flex flex-col justify-center relative bg-white w-full rounded-bl-[20px] rounded-br-[20px] border-[1px] border-t-0 border-neutral-200">
      <div>
        <div className="rounded-full overflow-hidden w-[48px] h-[48px]">
          <Image
            src={getCompanyLogo(job?.companyName || job?.id || 'default', 48)}
            alt={`${job?.companyName || 'Company'} Logo`}
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
        <h3 className="font-[600] text-[24px] text-[--headingTextColor]">
          {job?.title}
        </h3>
        <p className="font-medium !text-neutral-500">{`${job?.companyName} | ${job?.location}`}</p>
        <div className="flex flex-wrap gap-4 mt-6">
          <div className="py-[6px] px-[12px] bg-neutral-100 rounded-[8px]">
            <p className="font-medium text-[14px] text-[--bodyTextColor]">
              {job?.currencySymbol}
              {job?.monthlyFrom}-{job?.currencySymbol}
              {job?.monthlyTo}
            </p>
          </div>
          <div className="py-[6px] px-[12px] bg-neutral-100 rounded-[8px]">
            <p className="font-medium text-[14px] text-[--bodyTextColor]">
              {job?.jobType}
            </p>
          </div>
          <div className="py-[6px] px-[12px] bg-neutral-100 rounded-[8px]">
            <p className="font-medium text-[14px] text-[--bodyTextColor]">
              {job?.jobMode}
            </p>
          </div>
          <div className="py-[6px] px-[12px]  bg-[--success_100] rounded-[8px]">
            <p className="font-medium text-[14px] !text-[--success_600] flex items-center">
              {job?.benefits && 'Benefits Available'}{' '}
              <InfoIcon className="!size-4 pl-1" />
            </p>
          </div>
        </div>
      </div>

      <div className="rounded-[20px] border-[1px] border-neutral-200 p-4">
        <div
          className="font-bold uppercase leading-tight tracking-wide flex gap-2 pb-1"
          style={{
            background: 'linear-gradient(135deg, #4568DC 0%, #B06AB3 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            color: 'transparent',
          }}
        >
          <Image src={AiMagicStar} alt="ai_magic_star" width={20} height={20} />{' '}
          Why this role suits you
        </div>
        <p>{job?.whyRoleSuits}</p>
      </div>

      <Button className="max-w-[154px] h-[48px]" onClick={handleApply}>
        Apply now
      </Button>

      <div className="border-b-[1px] border-neutral-200 pb-8">
        <h3 className="font-[600] text-[18px] text-[--headingTextColor] pb-[12px]">
          Ask your AI Assistant
        </h3>
        <div className="flex flex-wrap gap-4">
          <Button variant={'outline'} className="rounded-[8px]">
            <MagicPencil stroke={strokeColor} /> Tell me more about this company
          </Button>
          <Button variant={'outline'} className="rounded-[8px]">
            <MagicPencil stroke={strokeColor} /> Something else...
          </Button>
        </div>
      </div>

      <div>
        <h3 className="font-[600] text-[18px] text-[--headingTextColor]">
          Job Description
        </h3>
        <p className="font-[400] text-[16px]">{job?.description}</p>
        <br />
      </div>
      <div>
        <h3 className="font-[600] text-[18px] text-[--headingTextColor]">
          Responsibilities
        </h3>
        <ul className="list-disc pl-6">
          {responsibilities.map((item) => (
            <li key={item} className="font-[400] text-[16px] py-1">
              {item}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default JobDetail;
// This component is used to display detailed information about a job.
