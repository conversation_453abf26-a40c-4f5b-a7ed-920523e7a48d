export interface CareerLevel {
  id: string;
  title: string;
  salaryRange: string;
  isCurrent?: boolean;
}

export interface GrowthStep {
  id: string;
  title: string;
  description: string;
  tasks: Task[];
  isExpanded?: boolean;
}

export interface Task {
  id: string;
  title: string;
  completed: boolean;
}

export interface RoleStats {
  jobPosts: number;
  experienceRange: string;
  description: string;
}

export interface EducationLevel {
  level: string;
  percentage: number;
  color: string;
}

export interface SalaryBracket {
  level: string;
  amount: number;
  period: string;
}

export interface Employer {
  id: string;
  name: string;
  jobCount: number;
}

export interface Skill {
  id: string;
  name: string;
  description: string;
  demandPercentage: number;
  category: 'primary' | 'secondary';
}

export interface JobTrend {
  month: string;
  count: number;
}
