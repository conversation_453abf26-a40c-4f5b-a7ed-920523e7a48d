import React from 'react';

function CareerTestSvg() {
  return (
    <div>
      <svg className="block mx-auto my-5" width="200" height="181">
        <g clipPath="url(#clip0_1724_14043)">
          <path
            d="M81.8083 25.9409H0.288574V71.8984H81.8083V25.9409Z"
            fill="#1D506B"
          />
          <path
            d="M87.8266 67.7946H5.75977V21.29H87.8266V67.7946ZM6.30688 67.2475H87.2795V21.8372H6.30688V67.2475Z"
            fill="#3F3D56"
          />
          <path
            d="M193.693 5.15039H112.173V51.1078H193.693V5.15039Z"
            fill="#F2F2F2"
          />
          <path
            d="M199.711 47.0046H117.644V0.5H199.711V47.0046ZM118.191 46.4575H199.164V1.04711H118.191V46.4575Z"
            fill="#3F3D56"
          />
          <path
            d="M140.381 180.5H36.1559C36.0833 180.5 36.0137 180.471 35.9624 180.42C35.9111 180.369 35.8823 180.299 35.8823 180.227C35.8823 180.154 35.9111 180.085 35.9624 180.033C36.0137 179.982 36.0833 179.953 36.1559 179.953H140.381C140.453 179.953 140.523 179.982 140.574 180.033C140.626 180.085 140.654 180.154 140.654 180.227C140.654 180.299 140.626 180.369 140.574 180.42C140.523 180.471 140.453 180.5 140.381 180.5Z"
            fill="#CCCCCC"
          />
          <path
            d="M101.403 115.868C101.427 115.373 101.558 114.89 101.789 114.452C102.019 114.013 102.343 113.631 102.737 113.331C103.131 113.032 103.587 112.822 104.071 112.718C104.554 112.613 105.055 112.616 105.538 112.726L110.017 106.414L114.516 108.2L108.066 117.025C107.828 117.823 107.305 118.507 106.596 118.946C105.887 119.385 105.042 119.548 104.221 119.406C103.4 119.263 102.659 118.824 102.14 118.172C101.621 117.52 101.359 116.7 101.403 115.868Z"
            fill="#A0616A"
          />
          <path
            d="M106.316 110.439L114.279 97.0468L111.035 88.3955L115.454 76.6123L115.617 76.6822C115.723 76.728 118.234 77.8483 118.606 81.8605L123.684 97.8189L110.132 115.4L106.316 110.439Z"
            fill="#3F3D56"
          />
          <path
            d="M63.0693 61.2046C63.5606 61.2662 64.0324 61.4347 64.4514 61.6984C64.8705 61.962 65.2267 62.3144 65.4949 62.7306C65.763 63.1468 65.9367 63.6167 66.0036 64.1073C66.0705 64.5979 66.0291 65.0971 65.8821 65.57L71.8287 70.5232L69.6999 74.871L61.4006 67.7581C60.6226 67.4587 59.9816 66.8842 59.5988 66.1437C59.2161 65.4032 59.1184 64.548 59.324 63.7402C59.5297 62.9323 60.0246 62.228 60.7149 61.7607C61.4052 61.2934 62.2429 61.0955 63.0693 61.2046Z"
            fill="#A0616A"
          />
          <path
            d="M68.1013 66.5225L80.8381 75.497L89.7144 72.9314L101.121 78.2477L101.038 78.4048C100.984 78.5078 99.6733 80.9243 95.6444 80.9851L79.3411 84.8137L62.8608 69.9433L68.1013 66.5225Z"
            fill="#3F3D56"
          />
          <path
            d="M91.407 175.657H86.9792L84.8726 158.578L91.4078 158.578L91.407 175.657Z"
            fill="#A0616A"
          />
          <path
            d="M82.5118 174.392H92.3558V179.769H78.4395V178.464C78.4395 177.384 78.8685 176.349 79.6322 175.585C80.396 174.821 81.4318 174.392 82.5118 174.392Z"
            fill="#2F2E41"
          />
          <path
            d="M109.105 175.657H104.677L102.57 158.578L109.105 158.578L109.105 175.657Z"
            fill="#A0616A"
          />
          <path
            d="M100.21 174.392H110.054V179.769H96.1372V178.464C96.1372 177.384 96.5663 176.349 97.33 175.585C98.0937 174.821 99.1295 174.392 100.21 174.392Z"
            fill="#2F2E41"
          />
          <path
            d="M110.679 113.316L110.318 138.599L109.957 171.827L102.011 171.104L98.0383 125.235L92.9819 171.104H84.7788L83.9526 124.874L86.8419 114.761L110.679 113.316Z"
            fill="#2F2E41"
          />
          <path
            d="M86.0498 114.915L84.8394 114.863L90.462 83.889L88.1598 74.527C88.0402 74.0399 88.1033 73.5259 88.3374 73.0822C88.5714 72.6386 88.96 72.2963 89.4296 72.1201L93.5718 70.5669L95.0351 66.5425L106.581 67.7866L106.865 70.9318C108.116 71.333 118.419 74.7643 117.708 78.674C117.007 82.533 112.864 94.6739 112.653 95.2892L111.928 112.701L111.839 112.745C111.731 112.799 103.773 116.712 95.1274 116.711C92.0829 116.711 88.9513 116.226 86.0498 114.915Z"
            fill="#3F3D56"
          />
          <path
            d="M107.65 60.2255C109.852 56.1969 108.372 51.1459 104.343 48.9437C100.315 46.7415 95.2639 48.2221 93.0617 52.2506C90.8595 56.2791 92.34 61.3302 96.3685 63.5324C100.397 65.7346 105.448 64.254 107.65 60.2255Z"
            fill="#A0616A"
          />
          <path
            d="M107.089 64.285C105.505 65.9726 102.563 65.0667 102.356 62.7612C102.34 62.5822 102.341 62.402 102.36 62.2233C102.466 61.202 103.056 60.2749 102.915 59.1966C102.883 58.9283 102.783 58.6725 102.625 58.4536C101.362 56.7631 98.3986 59.2098 97.2071 57.6794C96.4766 56.7409 97.3354 55.2635 96.7747 54.2146C96.0348 52.8304 93.8431 53.5132 92.4686 52.7551C90.9394 51.9117 91.0309 49.5654 92.0375 48.1383C93.2652 46.3979 95.4176 45.4692 97.5432 45.3353C99.6688 45.2015 101.78 45.7761 103.764 46.5495C106.019 47.4282 108.255 48.6425 109.642 50.6251C111.33 53.0362 111.492 56.2776 110.648 59.0969C110.135 60.8119 108.383 62.9069 107.089 64.285Z"
            fill="#2F2E41"
          />
        </g>
        <defs>
          <clipPath id="clip0_1724_14043">
            <rect
              width="199.423"
              height="180"
              fill="white"
              transform="translate(0.288574 0.5)"
            />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

export default CareerTestSvg;
