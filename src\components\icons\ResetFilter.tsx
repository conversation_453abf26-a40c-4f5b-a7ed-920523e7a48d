import React from 'react';

function ResetFilterIcon({ fill = '#1D506B' }: { fill?: string }) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 20.75C10.0772 20.75 8.23311 19.9862 6.87348 18.6265C5.51384 17.2669 4.75 15.4228 4.75 13.5C4.75 11.5772 5.51384 9.73311 6.87348 8.37348C8.23311 7.01384 10.0772 6.25 12 6.25H14.5C14.6989 6.25 14.8897 6.32902 15.0303 6.46967C15.171 6.61032 15.25 6.80109 15.25 7C15.25 7.19891 15.171 7.38968 15.0303 7.53033C14.8897 7.67098 14.6989 7.75 14.5 7.75H12C10.8628 7.75 9.75105 8.08723 8.80547 8.71905C7.85989 9.35087 7.1229 10.2489 6.68769 11.2996C6.25249 12.3502 6.13862 13.5064 6.36048 14.6218C6.58235 15.7372 7.12998 16.7617 7.93414 17.5659C8.73829 18.37 9.76284 18.9177 10.8782 19.1395C11.9936 19.3614 13.1498 19.2475 14.2004 18.8123C15.2511 18.3771 16.1491 17.6401 16.781 16.6945C17.4128 15.7489 17.75 14.6372 17.75 13.5C17.75 13.3011 17.829 13.1103 17.9697 12.9697C18.1103 12.829 18.3011 12.75 18.5 12.75C18.6989 12.75 18.8897 12.829 19.0303 12.9697C19.171 13.1103 19.25 13.3011 19.25 13.5C19.2474 15.422 18.4827 17.2645 17.1236 18.6236C15.7645 19.9827 13.922 20.7474 12 20.75Z"
        fill={fill}
      />
      <path
        d="M11.9992 10.7502C11.9006 10.7506 11.803 10.7314 11.712 10.6936C11.621 10.6558 11.5384 10.6003 11.4692 10.5302C11.3287 10.3895 11.2498 10.1989 11.2498 10.0002C11.2498 9.8014 11.3287 9.61078 11.4692 9.47015L13.9392 7.00015L11.4692 4.53015C11.3955 4.46149 11.3364 4.37869 11.2954 4.28669C11.2544 4.19469 11.2324 4.09538 11.2306 3.99468C11.2288 3.89397 11.2473 3.79394 11.2851 3.70056C11.3228 3.60717 11.3789 3.52233 11.4501 3.45112C11.5214 3.3799 11.6062 3.32375 11.6996 3.28603C11.793 3.24831 11.893 3.22979 11.9937 3.23156C12.0944 3.23334 12.1937 3.25538 12.2857 3.29637C12.3777 3.33736 12.4605 3.39647 12.5292 3.47015L15.5292 6.47015C15.6696 6.61078 15.7485 6.8014 15.7485 7.00015C15.7485 7.1989 15.6696 7.38953 15.5292 7.53015L12.5292 10.5302C12.4599 10.6003 12.3774 10.6558 12.2864 10.6936C12.1954 10.7314 12.0977 10.7506 11.9992 10.7502Z"
        fill={fill}
      />
    </svg>
  );
}

export default ResetFilterIcon;
