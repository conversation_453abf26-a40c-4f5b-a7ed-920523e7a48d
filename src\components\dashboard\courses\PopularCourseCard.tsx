import Image from 'next/image';
import Link from 'next/link';
import type { Course } from '@/types/courseType';
import FallbackImage from '../../../assets/images/ws/dashboard1.svg';
import { formatDate } from '@/utils';

interface CourseCardProps {
  course: Course;
  featured?: boolean;
}

export default function PopularCourseCard({
  course,
  featured = false,
}: CourseCardProps) {
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(course.courseFee || 0);

  const getDurationInWeeks = () => {
    if (course.isSelfPaced) return 'Self-paced';

    if (course.duration && course.durationUnit) {
      if (course.durationUnit === 'Weeks') return `${course.duration} Weeks`;
      if (course.durationUnit === 'Days')
        return `${Math.ceil(Number.parseInt(course.duration) / 7)} Weeks`;
      if (course.durationUnit === 'Months')
        return `${Number.parseInt(course.duration) * 4} Weeks`;
    }

    return 'N/A';
  };

  const getLocationType = () => {
    if (course.deliveryMode === 'Online') return 'Online';
    if (course.deliveryMode === 'Hybrid') return 'Hybrid';
    return course.city || 'In Person';
  };

  const getExperienceLevelColor = () => {
    switch (course.experienceLevel?.toLowerCase()) {
      case 'beginner':
        return 'bg-success-100 text-neutral-900';
      case 'intermediate':
        return 'bg-warning-100 text-neutral-900';
      case 'advanced':
        return 'bg-destructive-100 text-neutral-900';
      default:
        return 'bg-neutral-900 text-white';
    }
  };

  function normalizeText(input: string, separator = ' ') {
    return input.replace(/[-_]+/g, separator).replace(/\s+/g, ' ').trim();
  }

  return (
    <div
      className={`bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow ${featured ? 'h-full' : ''}`}
    >
      <Link href={`/dashboard/upskilling/all-courses/${course.id}`}>
        <div className="flex">
          <div className="relative w-[40%] p-4">
            <div className="">
              <Image
                src={course.coverImage || FallbackImage}
                alt={course.name}
                width={featured ? 1200 : 400}
                height={featured ? 400 : 240}
                className={`w-full ${featured ? 'h-64' : 'h-48'} object-cover rounded-lg`}
              />
            </div>
          </div>

          <div className="p-4 w-[60%]">
            <div className="relative h-[24px] w-[101px]">
              <div className="relative h-full w-full">
                <Image
                  src={course.partner?.logo || '/placeholder.svg'}
                  alt="Company logo"
                  fill
                  className="object-cover rounded-lg"
                  quality={100}
                />
              </div>
            </div>
            <h3 className="font-semibold text-[20px] my-1 text-neutral-900 leading-[24px] line-clamp-2">
              {course.name || 'N/A'}
            </h3>
            <p className="text-neutral-500 text-[16px] mb-3 font-normal leading-[24px]">
              {course.partner?.name || 'Training Provider Name'}
            </p>

            <div className="font-semibold text-[24px] leading-[32px] text-neutral-700">
              {' '}
              USD {formattedPrice}
            </div>

            <div className="flex gap-5 items-center mt-3">
              <div className="flex items-center gap-2 flex-wrap">
                <span
                  className={`px-2 py-1 rounded text-[16px] ${getExperienceLevelColor()}`}
                >
                  {course.experienceLevel || 'N/A'}
                </span>

                <span className="bg-neutral-100 px-2 py-1 rounded text-neutral-900 text-[16px]">
                  {course.deliveryMode || 'N/A'}
                </span>

                <div className="bg-neutral-100 px-2 py-1 rounded text-[16px] text-neutral-900 ">
                  Next Start: {formatDate(course.startDate) || 'No Next Date'}
                </div>
                <div className="flex items-center">
                  <span className="bg-neutral-100 px-2 py-1 rounded text-[16px] text-neutral-900 ">
                    {getDurationInWeeks()}
                  </span>
                </div>
              </div>
            </div>

            <div className="mt-2 text-[16px] text-neutral-500 font-normal leading-[24px] capitalize">
              Location: {normalizeText(getLocationType())}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
