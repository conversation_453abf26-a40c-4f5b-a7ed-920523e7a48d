'use client';

import React from 'react';

interface UserCardIconProps {
  width?: number
  height?: number
  className?: string
  strokeColor?: string
}

const UserCardIcon: React.FC<UserCardIconProps> = ({
  width = 48,
  height = 48,
  className = '',
  strokeColor = '#043C5B',
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill="none"
      className={className}
    >
      <path
        d="M30 18H37.5M30 24H37.5M30 30H37.5M9 39H39C41.4853 39 43.5 36.9853 43.5 34.5V13.5C43.5 11.0147 41.4853 9 39 9H9C6.51472 9 4.5 11.0147 4.5 13.5V34.5C4.5 36.9853 6.51472 39 9 39ZM21 18.75C21 20.8211 19.3211 22.5 17.25 22.5C15.1789 22.5 13.5 20.8211 13.5 18.75C13.5 16.6789 15.1789 15 17.25 15C19.3211 15 21 16.6789 21 18.75ZM23.5877 31.4227C21.6979 32.4295 19.5404 33 17.2497 33C14.959 33 12.8015 32.4295 10.9116 31.4227C11.8597 28.8415 14.3397 27 17.2497 27C20.1597 27 22.6396 28.8415 23.5877 31.4227Z"
        stroke={strokeColor}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default UserCardIcon;
