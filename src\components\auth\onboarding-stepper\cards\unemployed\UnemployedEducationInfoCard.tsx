import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { AdminValue } from '@/types';

const UnemployedEducationInfoCard = () => {
  const getEducationLevel = useAdminValues({
    category: AdminValuesCategories?.educationLevel?.category,
  });
  const JobType = getEducationLevel.data?.data?.data?.customValues || [];

  return (
    <div className="bg-white border rounded-lg p-6">
      <form action="">
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="company size"
            className="text-neutral-500 font-medium"
          >
            Education Level:
          </Label>
          <Select>
            <SelectTrigger className="">
              <SelectValue
                placeholder={
                  getEducationLevel?.isLoading ? 'Loading' : 'Select level'
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {JobType.map((item: AdminValue) => (
                  <SelectItem key={item.id} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="company size"
            className="text-neutral-500 font-medium"
          >
            University you attended for your most recent degree:
          </Label>
          <Select>
            <SelectTrigger className="">
              <SelectValue placeholder="Please Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="1-100">1-100</SelectItem>
                <SelectItem value="100-200">100-200</SelectItem>
                <SelectItem value="200-500">200-500</SelectItem>
                <SelectItem value="500-1000">500-1000</SelectItem>
                <SelectItem value="1000-2000">1000-2000</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="company size"
            className="text-neutral-500 font-medium"
          >
            Specialisation:
          </Label>
          <Select>
            <SelectTrigger className="">
              <SelectValue placeholder="Please Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="1-100">1-100</SelectItem>
                <SelectItem value="100-200">100-200</SelectItem>
                <SelectItem value="200-500">200-500</SelectItem>
                <SelectItem value="500-1000">500-1000</SelectItem>
                <SelectItem value="1000-2000">1000-2000</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="company size"
            className="text-neutral-500 font-medium"
          >
            Sub-specialisation(s) (up to 3):
          </Label>
          <Select>
            <SelectTrigger className="">
              <SelectValue placeholder="Please Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="1-100">1-100</SelectItem>
                <SelectItem value="100-200">100-200</SelectItem>
                <SelectItem value="200-500">200-500</SelectItem>
                <SelectItem value="500-1000">500-1000</SelectItem>
                <SelectItem value="1000-2000">1000-2000</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </form>
    </div>
  );
};

export default UnemployedEducationInfoCard;
