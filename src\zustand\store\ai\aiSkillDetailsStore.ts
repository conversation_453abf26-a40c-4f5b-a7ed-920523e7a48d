import type { IUpskillingPlan } from '@/types/aiTypes';
import { create } from 'zustand';

interface SkillDetailsState {
  skillDetails: IUpskillingPlan | null;
  currentSkillName: string | null;
  isGenerating: boolean;
  setSkillDetails: (_skillDetails: IUpskillingPlan) => void;
  getSkillDetails: () => IUpskillingPlan | null;
  getCurrentSkillName: () => string | null;
  setIsGenerating: (_isGenerating: boolean) => void;
  clearSkillDetails: () => void;
}

const useAiSkillDetailsStore = create<SkillDetailsState>((set, get) => ({
  skillDetails: null,
  currentSkillName: null,
  isGenerating: false,
  setSkillDetails: (skillDetails: IUpskillingPlan) => set({ skillDetails, isGenerating: false }),
  getSkillDetails: () => get().skillDetails,
  getCurrentSkillName: () => get().currentSkillName,
  setIsGenerating: (isGenerating: boolean) => set({ isGenerating }),
  clearSkillDetails: () => set({ skillDetails: null, currentSkillName: null, isGenerating: false }),
}));

export default useAiSkillDetailsStore;
