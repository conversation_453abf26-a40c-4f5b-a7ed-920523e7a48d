import type { IUpskillingPlan } from '@/types/aiTypes';
import { create } from 'zustand';

interface SkillDetailsState {
  skillDetails: IUpskillingPlan | null;
  currentSkillName: string | null;
  setSkillDetails: (_skillDetails: IUpskillingPlan) => void;
  getSkillDetails: () => IUpskillingPlan | null;
  getCurrentSkillName: () => string | null;
  clearSkillDetails: () => void;
}

const useAiSkillDetailsStore = create<SkillDetailsState>((set, get) => ({
  skillDetails: null,
  currentSkillName: null,
  setSkillDetails: (skillDetails: IUpskillingPlan) => set({ skillDetails }),
  getSkillDetails: () => get().skillDetails,
  getCurrentSkillName: () => get().currentSkillName,
  clearSkillDetails: () => set({ skillDetails: null, currentSkillName: null }),
}));

export default useAiSkillDetailsStore;
