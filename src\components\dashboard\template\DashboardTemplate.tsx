'use client';
import React from 'react';
import SearchInput from '@/components/common/SearchInput';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import DevelopCareerSection from './DevelopCareerSection';
import FindNextJob from './FindNextJob';
import EligibleBenefits from './EligibleBenefits';
import UpskillSection from './UpskillSection';
import AdvanceCareerSection from './AdvanceCareerSection';
import UpdateCareerSection from './UpdateCareerSection';

const DashboardTemplate = () => {
  const user = useAuthStore((state) => state.user);

  return (
    <div>
      <div className="space-y-8">
        <div className="space-y-5 leading-[30px]">
          <h4 className="text-neutral-900 font-semibold text-[40px] leading-[48px]">
            Welcome, what would you like to do today?
          </h4>
          <p className="text-neutral-700 text-[20px] font-normal md:max-w-[870px] leading-[30px]">
            Explore tools and resources to enhance your career growth and make
            the most of your financial benefits. Choose from the options below
            to get started.
          </p>
        </div>
        <SearchInput />
        {user?.employmentStatus === 'Unemployed' && <DevelopCareerSection />}
        {user?.employmentStatus === 'Unemployed' && <FindNextJob />}
        {user?.employmentStatus !== 'Unemployed' && <EligibleBenefits />}
        <UpskillSection />
        {user?.employmentStatus !== 'Unemployed' && <UpdateCareerSection />}
        {user?.employmentStatus !== 'Unemployed' && <AdvanceCareerSection />}
        {user?.employmentStatus === 'Unemployed' && <EligibleBenefits />}
      </div>

      <p className="py-8 font-normal text-[20px] leading-[16px]">
        Need assistance?{' '}
        <span className="font-semibold text-[20px] leading-[16px] text-primary-500">
          Contact Support
        </span>
      </p>
    </div>
  );
};

export default DashboardTemplate;
