import React from 'react';

interface AssistantTooltipProps {
  imgUrl?: string
  avatarPosition?:
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right'
  arrowPosition?:
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right'
  children?: React.ReactNode
}

const AiTooltip: React.FC<AssistantTooltipProps> = ({
  arrowPosition = 'top-center',
  children,
}) => {
  const arrowClasses = {
    'top-left':
      'absolute bottom-full left-0 transform -translate-x-1/2 -mb-3 ml-14',
    'top-center':
      'absolute bottom-full left-1/2 transform -translate-x-1/2 -mb-3',
    'top-right': 'absolute bottom-full right-0 transform translate-x-1/2 -mb-3',
    'bottom-left': 'absolute top-full left-0 transform -translate-x-1/2 mt-3',
    'bottom-center':
      'absolute top-full left-1/2 transform -translate-x-1/2 mt-3',
    'bottom-right': 'absolute top-full right-[10px] -mt-3 ml-14',
  };

  return (
    <div className={`relative flex items-center justify-center w-full`}>
      <div className="relative p-4 bg-white rounded-xl shadow text-center w-full mt-14">
        <div
          className={`w-3 h-4 bg-white rotate-45  ${arrowClasses[arrowPosition]}`}
        ></div>

        {children}
      </div>
    </div>
  );
};

export default AiTooltip;
