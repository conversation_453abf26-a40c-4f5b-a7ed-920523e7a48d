/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { CalendarIcon, IterationCcw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import type { CourseFilters } from '@/types/courseType';
import type { Cities, Topic } from '@/types';

const EXPERIENCE_LEVELS = [
  'All',
  'Beginner',
  'Intermediate',
  'Advanced',
] as const;
const DELIVERY_MODES = ['All', 'In Person', 'Online', 'Hybrid'] as const;
const DURATION_OPTIONS = [
  'Less than 2 hours',
  'Less than 1 week',
  '1-4 weeks',
  '1-3 months',
  '3-6 months',
  '6-12 months',
  '1+ years',
] as const;

interface CourseFiltersProps {
  filters: CourseFilters;
  mainTopics: Topic[];
  cities: Cities[];
  isLoading: boolean;
  ApplyFilters: (_filters: CourseFilters) => void;
  onResetFilters: () => void;
}

export function CourseFilter({
  filters,
  mainTopics,
  cities,
  isLoading,
  ApplyFilters,
  onResetFilters,
}: CourseFiltersProps) {
  const [localFilters, setLocalFilters] = useState<CourseFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleLocalChange = (key: keyof CourseFilters, value: any) => {
    setLocalFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleApplyFilters = () => {
    ApplyFilters(localFilters);
  };

  const handleResetFilters = () => {
    onResetFilters();
  };

  return (
    <form
      className="space-y-6 p-4  z-[9999999999]"
      onSubmit={(e) => {
        e.preventDefault();
        handleApplyFilters();
      }}
    >
      <div>
        <Label htmlFor="course-name">Course Name:</Label>
        <Input
          id="course-name"
          placeholder="Search keyword..."
          className="mt-1"
          value={localFilters.search || ''}
          onChange={(e) => handleLocalChange('search', e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="main-topic">Main Topic:</Label>
        <Select
          value={localFilters.mainTopic || 'All'}
          onValueChange={(value) => handleLocalChange('mainTopic', value)}
        >
          <SelectTrigger id="main-topic" className="mt-1">
            <SelectValue placeholder="Select topic" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All</SelectItem>
            {mainTopics.map((topic) => (
              <SelectItem key={topic?.id} value={topic?.value}>
                {topic?.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label>Delivery Mode:</Label>
        <div className="mt-1 space-y-2">
          {DELIVERY_MODES.map((mode) => (
            <div key={mode} className="flex items-center space-x-2">
              <Checkbox
                id={`mode-${mode}`}
                checked={
                  localFilters.deliveryMode === mode ||
                  (mode === 'All' && !localFilters.deliveryMode)
                }
                onCheckedChange={(checked) =>
                  handleLocalChange(
                    'deliveryMode',
                    checked ? (mode === 'All' ? undefined : mode) : undefined
                  )
                }
              />
              <Label htmlFor={`mode-${mode}`} className="font-normal">
                {mode}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <Label htmlFor="experience-level">Experience Level:</Label>
        <Select
          value={localFilters.experienceLevel || 'All'}
          onValueChange={(value) => handleLocalChange('experienceLevel', value)}
        >
          <SelectTrigger id="experience-level" className="mt-1">
            <SelectValue placeholder="Select level" />
          </SelectTrigger>
          <SelectContent>
            {EXPERIENCE_LEVELS.map((level) => (
              <SelectItem key={level} value={level}>
                {level}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="city">City:</Label>
        <Select
          value={localFilters.city || 'All'}
          onValueChange={(value) => handleLocalChange('city', value)}
        >
          <SelectTrigger id="city" className="mt-1">
            <SelectValue placeholder="Select city" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">Please Select</SelectItem>
            {cities.map((city) => (
              <SelectItem key={city?.id} value={city?.value}>
                {city?.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <div>
          <Label>Start Date From:</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full mt-1 text-left font-normal justify-between hover:bg-[transparent]   border-neutral-200 rounded-[6px]"
              >
                {localFilters.startDate ? (
                  format(new Date(localFilters.startDate), 'PPP')
                ) : (
                  <span>DD/MM/YYYY</span>
                )}
                <CalendarIcon className="h-4 w-4 text-[--bodyTextColor]" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={
                  localFilters.startDate
                    ? new Date(localFilters.startDate)
                    : undefined
                }
                onSelect={(date) => handleLocalChange('startDate', date)}
                initialFocus
              />
              <div className="p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-destructive-500"
                  onClick={() => handleLocalChange('startDate', undefined)}
                >
                  Clear
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        <div>
          <Label>Start Date To:</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full mt-1 text-left font-normal justify-between hover:bg-[transparent]   border-neutral-200 rounded-[6px]"
              >
                {localFilters.endDate ? (
                  format(new Date(localFilters.endDate), 'PPP')
                ) : (
                  <span>DD/MM/YYYY</span>
                )}
                <CalendarIcon className="h-4 w-4 text-[--bodyTextColor]" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={
                  localFilters.endDate
                    ? new Date(localFilters.endDate)
                    : undefined
                }
                onSelect={(date) => handleLocalChange('endDate', date)}
                initialFocus
              />
              <div className="p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-destructive-500"
                  onClick={() => handleLocalChange('endDate', undefined)}
                >
                  Clear
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div>
        <Label htmlFor="duration">Duration:</Label>
        <Select
          value={localFilters.duration || 'All'}
          onValueChange={(value) => handleLocalChange('duration', value)}
        >
          <SelectTrigger id="duration" className="mt-1">
            <SelectValue placeholder="Select duration" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All" style={{ color: 'red !important' }}>
              Select Duration
            </SelectItem>
            {DURATION_OPTIONS.map((duration) => (
              <SelectItem
                key={duration}
                value={duration}
                className="!text-[--buttonColor]"
              >
                {duration}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="courseFee">Course Fee (max):</Label>
        <Input
          id="courseFee"
          type="number"
          placeholder="Enter Amount"
          className="mt-1"
          min="0"
          step="1"
          value={localFilters.courseFee || ''}
          onChange={(e) =>
            handleLocalChange(
              'courseFee',
              e.target.value ? e.target.value : undefined
            )
          }
        />
      </div>

      <div className="pt-4 space-y-2">
        <Button
          className="w-full"
          onClick={handleApplyFilters}
          disabled={isLoading}
          type="submit"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Searching...
            </>
          ) : (
            'Search Courses'
          )}
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={handleResetFilters}
          disabled={isLoading}
        >
          <IterationCcw className="text-[--buttonColor]" />
          Reset Filters
        </Button>
      </div>
    </form>
  );
}
