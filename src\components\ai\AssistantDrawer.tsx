'use client';

import { useState } from 'react';
import { ChatInput } from './ChatInput';
import Image from 'next/image';
import AIAssistantIcon from '@/assets/images/ws/assistant.svg';
import CollapseButton from '@/components/icons/CollapseButton';
import ExpandButton from '@/components/icons/ExpandButton';
import useSettingsStore from '@/zustand/store/settingsStore';
import { AiMessage } from './AiMessage';

const initialMessages = [
  {
    id: 1,
    text: 'Thanks for uploading your CV!',
    isUser: false,
    timestamp: new Date(),
  },
  {
    id: 2,
    text: 'I see you have experience as a [Job Title], and skills like [Skill 1], [Skill 2], [Skill 3].',
    isUser: false,
    timestamp: new Date(),
  },
  {
    id: 3,
    text: 'Let’s fine-tune a few things to find you the best job matches. Are you looking to continue in [Job Title from CV], or explore something new?',
    isUser: false,
    timestamp: new Date(),
  },
];

export const CareerAssistant = () => {
  const { appearanceSettings } = useSettingsStore();
  const [messages, setMessages] = useState(initialMessages);

  const [isMinimized, setIsMinimized] = useState(true);
  const { brandColor } = appearanceSettings;

  const handleSendMessage = (message: string) => {
    const newMessage = {
      id: messages.length + 1,
      text: message,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages([...messages, newMessage]);
    setIsMinimized(false);
  };

  return (
    <div className="relative bg-white h-screen right-0 z-50">
      <div
        className={`transform transition-all duration-300 ease-in-out h-[76vh] ${isMinimized ? 'translate-x-full' : 'translate-x-0'
          } ${isMinimized ? 'w-20 p-6' : 'w-[480px]'}`}
      >
        <div className="bg-white  border border-gray-200 flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <Image
                className="w-20 h-20"
                src={AIAssistantIcon}
                alt="assistantIcon"
              />
              <span className="font-semibold text-[28px] text-neutral-900 leading-[36px] -tracking-[0.56px]">
                {' '}
                e-Firstname
              </span>
            </div>
            <CollapseButton
              fill={brandColor}
              onClick={() => setIsMinimized(true)}
            />
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <AiMessage
                key={message.id}
                text={message.text}
                isUser={message.isUser}
              />
            ))}
          </div>

          <div className="p-4 pt-0 border-neutral-200">
            <ChatInput onSendMessage={handleSendMessage} />
          </div>
        </div>
      </div>

      {isMinimized && (
        <div className="absolute right-0 left-0 top-0 flex items-center justify-center mt-2">
          <div className="p-2 mx-aut0 rounded-full cursor-pointer transition hover:scale-105 flex flex-col items-center justify-center">
            <ExpandButton
              fill={brandColor}
              onClick={() => setIsMinimized(false)}
            />
          </div>
        </div>
      )}

      {/* AI Chat box  */}
      {/* {isMinimized && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 w-[788px]">
          <div className="bg-white shadow-lg px-4 py-2 rounded-[28px] h-[120px]">
            <CloseChatInput onSendMessage={handleSendMessage} />
          </div>
        </div>
      )} */}
    </div>
  );
};
