import React from 'react';

function CustomTabPanelSvg1() {
  return (
    <div>
      <svg
        width="258"
        height="240"
        viewBox="0 0 258 240"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1500_5274)">
          <path
            d="M173.5 170.139H81.1665C73.4053 170.139 67.0913 163.825 67.0913 156.064V63.7304C67.0913 55.9693 73.4053 49.6553 81.1665 49.6553H173.5C181.261 49.6553 187.575 55.9693 187.575 63.7304V156.064C187.575 163.825 181.261 170.139 173.5 170.139ZM81.1665 51.9073C74.6367 51.9073 69.3433 57.2007 69.3433 63.7304V156.064C69.3433 162.593 74.6367 167.887 81.1665 167.887H145.844C167.647 167.887 185.323 150.211 185.323 128.407V63.7304C185.323 57.2007 180.029 51.9073 173.5 51.9073L81.1665 51.9073Z"
            fill="#F2F2F2"
          />
          <path
            d="M91.1209 61.5093C86.7509 61.5093 83.1958 65.0643 83.1958 69.4344V81.8389C83.1958 86.209 86.7509 89.764 91.1209 89.764H103.525C107.895 89.764 111.451 86.209 111.451 81.8389V69.4344C111.451 65.0643 107.895 61.5093 103.525 61.5093H91.1209Z"
            fill="#F2F2F2"
          />
          <path
            d="M91.1209 96.4297C86.7509 96.4297 83.1958 99.9848 83.1958 104.355V116.759C83.1958 121.129 86.7509 124.684 91.1209 124.684H103.525C107.895 124.684 111.451 121.129 111.451 116.759V104.355C111.451 99.9848 107.895 96.4297 103.525 96.4297H91.1209Z"
            fill="#F2F2F2"
          />
          <path
            d="M91.1209 131.35C86.7509 131.35 83.1958 134.905 83.1958 139.275V151.68C83.1958 156.05 86.7509 159.605 91.1209 159.605H103.525C107.895 159.605 111.451 156.05 111.451 151.68V139.275C111.451 134.905 107.895 131.35 103.525 131.35H91.1209Z"
            fill="#F2F2F2"
          />
          <path
            d="M99.3467 31.2451C93.8403 31.2451 89.3608 35.7246 89.3608 41.231V56.8611C89.3608 62.3674 93.8403 66.8469 99.3467 66.8469H114.977C120.483 66.8469 124.963 62.3674 124.963 56.8611V41.231C124.963 35.7246 120.483 31.2451 114.977 31.2451H99.3467Z"
            fill="#1D506B"
          />
          <path
            d="M150.805 74.9248C145.298 74.9248 140.819 79.4043 140.819 84.9107V100.541C140.819 106.047 145.298 110.527 150.805 110.527H166.435C171.941 110.527 176.421 106.047 176.421 100.541V84.9107C176.421 79.4043 171.941 74.9248 166.435 74.9248H150.805Z"
            fill="#1D506B"
          />
          <path
            d="M128.433 232.247L125.258 232.221L123.853 219.688L128.539 219.727L128.433 232.247Z"
            fill="#A0616A"
          />
          <path
            d="M118.263 237.297C118.256 238.078 118.886 238.722 119.67 238.729L125.983 238.781L127.089 236.541L127.495 238.793L129.877 238.814L129.275 230.784L128.446 230.728L125.066 230.495L123.975 230.422L123.956 232.695L118.885 236.134C118.499 236.397 118.267 236.831 118.263 237.297Z"
            fill="#2F2E41"
          />
          <path
            d="M144.589 232.247L141.413 232.221L140.008 219.688L144.694 219.727L144.589 232.247Z"
            fill="#A0616A"
          />
          <path
            d="M134.418 237.297C134.411 238.078 135.041 238.722 135.826 238.729L142.139 238.781L143.244 236.541L143.65 238.793L146.032 238.814L145.43 230.784L144.602 230.728L141.221 230.495L140.131 230.422L140.111 232.695L135.04 236.134C134.654 236.397 134.422 236.831 134.418 237.297Z"
            fill="#2F2E41"
          />
          <path
            d="M115.973 130.918L128.844 131.324L141.037 134.034C141.037 134.034 139.818 135.524 143.882 139.589C143.882 139.589 149.302 149.073 145.508 158.286L143.882 195.137C143.882 195.137 148.623 221.115 145.001 229.383L140.214 229.682L132.114 195.437L128.166 158.557L126.27 194.866C126.27 194.866 133.526 219.788 128.944 229.682L123.76 229.383L114.889 194.866C114.889 194.866 111.096 155.034 111.637 147.447C112.179 139.86 115.973 130.918 115.973 130.918Z"
            fill="#2F2E41"
          />
          <path
            d="M159.197 72.6869C157.437 73.1425 156.38 74.9382 156.836 76.6977C157.045 77.5039 157.537 78.1598 158.176 78.5931L158.175 78.5976L157.926 84.6434L162.552 86.7844L162.922 77.481L162.889 77.482C163.291 76.7673 163.429 75.9039 163.208 75.0478C162.752 73.2883 160.956 72.2313 159.197 72.6869Z"
            fill="#A0616A"
          />
          <path
            d="M133.339 94.2641C132.367 93.3007 131.803 91.9946 131.782 90.5743C131.754 88.6689 132.708 86.9395 134.336 85.9483C136.21 84.8068 138.554 84.9223 140.307 86.2422L154.836 97.1809L157.424 81.4229L162.982 83.212L161.656 103.731C161.563 105.166 160.752 106.431 159.486 107.113C158.22 107.796 156.718 107.779 155.468 107.068L134.472 95.1226C134.051 94.8826 133.671 94.5932 133.339 94.2641Z"
            fill="#3F3D56"
          />
          <path
            d="M111.186 63.8725C112.115 65.4348 111.601 67.4542 110.039 68.383C109.323 68.8085 108.512 68.928 107.753 68.7857L107.75 68.7891L103.67 73.2582L98.8774 71.5213L105.165 64.6545L105.188 64.6779C105.406 63.8879 105.915 63.1772 106.675 62.7253C108.238 61.7966 110.257 62.3102 111.186 63.8725Z"
            fill="#A0616A"
          />
          <path
            d="M112.948 97.2124L89.628 90.9132C88.2395 90.5381 87.1608 89.4927 86.7423 88.1166C86.3238 86.7405 86.638 85.2715 87.5826 84.1869L101.087 68.6816L106.294 71.3241L97.0378 84.3372L115.056 86.7978C117.231 87.0948 118.976 88.663 119.504 90.7933C119.962 92.6431 119.422 94.5435 118.061 95.8766C117.046 96.8704 115.726 97.401 114.357 97.4008C113.89 97.4008 113.416 97.3389 112.948 97.2124Z"
            fill="#3F3D56"
          />
          <path
            d="M146.194 136.339L113.584 137.835C112.689 136.475 112.931 134.43 114.185 131.869C118.921 122.2 115.325 103.908 112.905 94.3826C112.226 91.7101 114.049 89.0533 116.792 88.779L119.867 88.4715L122.709 79.7954H132.663L136.793 84.5132L143.801 88.7707C142.525 101.91 137.09 120.465 144.833 129.77C146.527 131.805 147.007 134.005 146.194 136.339Z"
            fill="#3F3D56"
          />
          <path
            d="M126.42 79.9961C131.775 79.9961 136.116 75.6552 136.116 70.3005C136.116 64.9458 131.775 60.605 126.42 60.605C121.065 60.605 116.725 64.9458 116.725 70.3005C116.725 75.6552 121.065 79.9961 126.42 79.9961Z"
            fill="#A0616A"
          />
          <path
            d="M137.391 64.0942C138.74 59.1761 124.038 54.9587 121.181 58.9193C120.784 58.3562 119.316 58.0142 118.642 58.1869C117.968 58.3596 117.402 58.7914 116.849 59.2095C116.089 59.7933 115.302 60.3978 114.835 61.2373C114.365 62.0733 114.293 63.2133 114.925 63.9387C115.426 64.5156 116.306 67.5813 117.059 67.7333C117.584 67.8404 118.026 67.9268 118.406 67.9889C118.745 67.4949 119.608 66.8744 119.546 66.2768C120.057 66.6153 119.871 67.2109 119.768 67.8197C119.424 69.8467 111.691 85.5384 116.12 80.9144C116.559 81.1735 117.101 81.4153 117.727 81.6364C118.787 80.0369 119.658 78.1508 120.231 76.1782L120.235 76.2146C120.433 77.9289 121.685 79.3354 123.343 79.8156C129.991 81.7417 135.369 78.9154 137.219 73.8186C136.542 72.4437 136.247 72.593 136.308 72.5521C137.153 71.9823 138.315 72.355 138.678 73.3075C138.786 73.5884 138.883 73.8282 138.967 74.0086C139.931 70.5609 141.077 71.0586 137.391 64.0942Z"
            fill="#2F2E41"
          />
          <path
            d="M132.805 60.5259L132.126 57.0539C132.069 56.7621 132.014 56.4454 132.146 56.1787C132.315 55.8361 132.75 55.6941 133.128 55.7487C133.506 55.8033 133.789 56.1487 134.165 56.2153C135.473 56.4465 137.203 55.1543 137.671 58.4233C137.867 59.7863 140.042 59.9303 140.768 61.1C141.494 62.2698 141.584 63.9582 140.592 64.9138C139.801 65.6771 138.529 65.7652 137.501 65.3753C136.473 64.9854 135.648 64.1979 134.927 63.3674C134.206 62.537 133.553 61.6383 132.722 60.9186"
            fill="#2F2E41"
          />
          <path
            d="M137.909 65.8068C135.256 65.4362 133.448 64.4714 132.534 62.9392C131.339 60.9337 132.14 58.7254 132.174 58.6323L132.735 58.8408C132.728 58.8611 132.001 60.8826 133.052 62.638C133.869 64.0036 135.531 64.8704 137.992 65.2143L137.909 65.8068Z"
            fill="#1D506B"
          />
          <path
            d="M150.359 28.2912C142.56 28.2912 136.214 21.945 136.214 14.1456C136.214 6.34618 142.56 0 150.359 0C158.159 0 164.505 6.34618 164.505 14.1456C164.505 21.945 158.159 28.2912 150.359 28.2912ZM150.359 5.65825C145.679 5.65825 141.872 9.4654 141.872 14.1456C141.872 18.8258 145.679 22.633 150.359 22.633C155.04 22.633 158.847 18.8258 158.847 14.1456C158.847 9.4654 155.04 5.65825 150.359 5.65825Z"
            fill="#1D506B"
          />
          <path
            d="M172.124 37.9045C171.43 37.9045 170.735 37.6517 170.19 37.1392L156.913 24.6968C155.772 23.629 155.714 21.8387 156.783 20.699C157.849 19.5552 159.64 19.4986 160.781 20.5692L174.057 33.0115C175.199 34.0793 175.257 35.8697 174.187 37.0093C173.631 37.6047 172.878 37.9045 172.124 37.9045Z"
            fill="#1D506B"
          />
          <path
            d="M232.128 231.93C233.09 231.99 233.621 230.795 232.894 230.098L232.821 229.81C232.831 229.787 232.84 229.764 232.85 229.741C233.823 227.422 237.12 227.438 238.084 229.761C238.94 231.822 240.03 233.888 240.298 236.068C240.419 237.03 240.365 238.01 240.151 238.955C242.157 234.573 243.212 229.789 243.212 224.977C243.212 223.768 243.146 222.559 243.011 221.353C242.9 220.367 242.746 219.386 242.548 218.415C241.474 213.164 239.15 208.163 235.797 203.986C234.185 203.105 232.84 201.728 232.028 200.077C231.737 199.481 231.508 198.841 231.4 198.188C231.583 198.212 232.092 195.418 231.953 195.247C232.209 194.859 232.666 194.666 232.946 194.288C234.334 192.405 236.247 192.734 237.246 195.292C239.38 196.369 239.4 198.155 238.091 199.872C237.258 200.965 237.144 202.443 236.413 203.613C236.488 203.709 236.566 203.803 236.642 203.899C238.02 205.667 239.213 207.568 240.218 209.565C239.934 207.345 240.353 204.672 241.068 202.949C241.882 200.985 243.408 199.331 244.751 197.634C246.365 195.595 249.674 196.485 249.959 199.069C249.962 199.094 249.964 199.119 249.967 199.144C249.767 199.257 249.572 199.377 249.381 199.503C248.292 200.223 248.668 201.912 249.959 202.111L249.988 202.116C249.916 202.834 249.792 203.547 249.612 204.248C251.336 210.913 247.614 213.341 242.301 213.45C242.184 213.51 242.07 213.57 241.952 213.627C242.491 215.143 242.921 216.698 243.24 218.274C243.525 219.666 243.724 221.073 243.835 222.487C243.973 224.27 243.961 226.062 243.811 227.843L243.82 227.78C244.202 225.819 245.266 223.987 246.812 222.718C249.115 220.827 252.368 220.13 254.852 218.61C256.048 217.878 257.581 218.824 257.372 220.21L257.362 220.276C256.992 220.427 256.631 220.601 256.282 220.797C256.083 220.909 255.887 221.029 255.696 221.155C254.607 221.876 254.983 223.564 256.274 223.763L256.303 223.768C256.324 223.771 256.342 223.774 256.363 223.777C255.729 225.284 254.845 226.682 253.738 227.888C252.66 233.707 248.031 234.259 243.08 232.564H243.077C242.536 234.922 241.745 237.226 240.728 239.421H232.338C232.308 239.328 232.281 239.231 232.254 239.138C233.03 239.186 233.812 239.141 234.576 239C233.953 238.236 233.331 237.466 232.708 236.702C232.693 236.687 232.681 236.672 232.669 236.657C232.353 236.266 232.034 235.878 231.719 235.487L231.719 235.487C231.699 234.287 231.843 233.09 232.128 231.93Z"
            fill="#F2F2F2"
          />
          <path
            d="M0 239.446C0 239.753 0.246784 240 0.554097 240H256.772C257.079 240 257.326 239.753 257.326 239.446C257.326 239.138 257.079 238.892 256.772 238.892H0.554097C0.246784 238.892 0 239.138 0 239.446Z"
            fill="#CCCCCC"
          />
        </g>
        <defs>
          <clipPath id="clip0_1500_5274">
            <rect width="257.391" height="240" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

export default CustomTabPanelSvg1;
