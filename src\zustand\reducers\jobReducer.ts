import type { Job, JobState } from '@/types/jobTypes';

type JobAction =
  | {
      type: 'SET_JOBS';
      payload: { jobs: Job[]; page: number; size: number; total: number };
    }
  | { type: 'SET_USER_JOBS'; payload: Job[] }
  | { type: 'SET_CURRENT_JOB'; payload: Job | null }
  | { type: 'ADD_JOB'; payload: Job }
  | { type: 'UPDATE_JOB'; payload: Job }
  | { type: 'DELETE_JOB'; payload: string }
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' };

export const jobReducer = (state: JobState, action: JobAction): JobState => {
  switch (action.type) {
    case 'SET_JOBS':
      return {
        ...state,
        jobs: action.payload.jobs,
        page: action.payload.page,
        size: action.payload.size,
        total: action.payload.total,
        isLoading: false,
      };
    case 'SET_USER_JOBS':
      return {
        ...state,
        jobs: action.payload,
        isLoading: false,
      };
    case 'SET_CURRENT_JOB':
      return {
        ...state,
        currentJob: action.payload,
        isLoading: false,
      };
    case 'ADD_JOB':
      return {
        ...state,
        jobs: [action.payload, ...state.jobs],
      };
    case 'UPDATE_JOB':
      return {
        ...state,
        jobs: state.jobs.map((job) =>
          job.id === action.payload.id ? action.payload : job
        ),
      };
    case 'DELETE_JOB':
      return {
        ...state,
        jobs: state.jobs.filter((job) => job.id !== action.payload),
      };
    case 'START_LOADING':
      return { ...state, isLoading: true };
    case 'STOP_LOADING':
      return { ...state, isLoading: false };
    default:
      return state;
  }
};
