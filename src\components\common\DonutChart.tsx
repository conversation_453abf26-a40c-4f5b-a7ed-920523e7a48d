/* eslint-disable @typescript-eslint/no-explicit-any */
import type { FC } from 'react';
import { useLayoutEffect } from 'react';
import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

type DonutChartColorsProps = {
  categoryName: string;
  color: am5.Color;
};

type DonutChartProps = {
  name: string;
  categoryFieldName: string;
  valueFieldName: string;
  data: any[];
  colors?: DonutChartColorsProps[];
  disableExport?: boolean;
};

const DonutChart: FC<DonutChartProps> = ({
  name,
  data,
  categoryFieldName,
  valueFieldName,
  colors,
}) => {
  useLayoutEffect(() => {
    am5.addLicense('AM5M326788873');

    const root = am5.Root.new(`${name}-donutchart`);

    // If no colors specified
    if (colors) {
      root.setThemes([am5themes_Animated.new(root)]);
    } else {
      // Set slice color from the following list
      const defaultTheme = am5.Theme.new(root);
      root.setThemes([am5themes_Animated.new(root), defaultTheme]);

      defaultTheme
        .rule('ColorSet')
        .set('colors', [
          am5.color(0x022437),
          am5.color(0x1d506b),
          am5.color(0x829ead),
          am5.color(0xb4c5ce),
        ]);
    }

    const chart = root.container.children.push(
      am5percent.PieChart.new(root, {
        layout: root.horizontalLayout,
        innerRadius: am5.percent(50),
      })
    );

    const series = chart.series.push(
      am5percent.PieSeries.new(root, {
        valueField: valueFieldName,
        categoryField: categoryFieldName,
        alignLabels: false,
        legendValueText: "{value.formatNumber('###.0')}%",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{category}: {value.formatNumber('###.0')}%",
        }),
      })
    );

    series.labels.template.setAll({
      textType: 'circular',
    });

    series.slices.template.setAll({
      templateField: 'sliceSettings',
    });

    if (data && data.length > 0 && colors && colors.length > 0) {
      let tempData = data.map((a) => Object.assign({}, a));
      tempData = tempData.map((value: any) => {
        const colorSettings = colors.find(
          (c) => c.categoryName === value[categoryFieldName]
        );
        if (colorSettings) {
          value.sliceSettings = {
            stroke: colorSettings.color,
            fill: colorSettings.color,
          };
        }
        return value;
      });

      series.data.setAll(tempData);
    }

    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.percent(100),
        x: am5.percent(100),
        centerY: am5.percent(0),
        y: am5.percent(7),
        layout: root.verticalLayout,
        useDefaultMarker: true,
        paddingTop: 40,
      })
    );

    legend.labels.template.setAll({
      width: 60,
      textAlign: 'left',
      fontSize: 12,
    });

    legend.valueLabels.template.setAll({
      width: 20,
      textAlign: 'right',
      fontSize: 13,
    });

    legend.markerRectangles.template.setAll({
      cornerRadiusTL: 10,
      cornerRadiusTR: 10,
      cornerRadiusBL: 10,
      cornerRadiusBR: 10,
    });

    legend.data.setAll(series.dataItems);

    series.labels.template.set('visible', false);
    series.labels.template.set('forceHidden', true);

    series.appear(1000, 100);

    return () => root.dispose();
  }, [data]); // eslint-disable-line
  return <div id={`${name}-donutchart`} className="chart-container" />;
};

export default DonutChart;
