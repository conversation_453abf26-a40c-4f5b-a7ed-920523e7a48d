import { create } from 'zustand';
import jobMatchesService from '@/zustand/services/jobMatches.services';
import type { ICoverLetterInputs, IJobMatch, IJobMatchBody } from '@/types';

interface JobMatchesState {
  jobMatches: IJobMatch[];
  appliedJobs: number;
  reviewedJobs: number;
  allJobsReviewed: boolean;
  isLoading: boolean | string;
  message: string;
  getJobMatches: (_data: IJobMatchBody) => Promise<void>;
  applyForJob: (_jobId: string, _applyType: string) => Promise<void>;
  generateCoverLetter: (_data: ICoverLetterInputs) => Promise<void>;
  clearJobMatches: () => void;
}

const useJobMatchesStore = create<JobMatchesState>((set, get) => ({
  jobMatches: [],
  isLoading: false,
  appliedJobs: 0,
  reviewedJobs: 0,
  allJobsReviewed: false,
  message: '',

  getJobMatches: async (data) => {
    // Don't fetch if all jobs are already reviewed
    const state = get();
    if (state.allJobsReviewed) {
      console.log('All jobs already reviewed, skipping API call');
      return;
    }

    try {
      set({ isLoading: 'jobMatches' });
      const response = await jobMatchesService.getJobMatches(data);
      set({
        jobMatches: response?.data?.jobs || [],
        appliedJobs: 0,
        reviewedJobs: 0,
        allJobsReviewed: false,
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to get jobs', error);
      set({ isLoading: false, jobMatches: [] });
    }
  },

  applyForJob: async (jobId: string, applyType) => {
    try {
      set((state) => {
        const updatedMatches = state.jobMatches.map((job) =>
          job.id === jobId ? { ...job, applyType } : job
        );

        const newReviewedJobs = state.reviewedJobs + 1;
        const newAppliedJobs = applyType === 'applied' ? state.appliedJobs + 1 : state.appliedJobs;
        const allReviewed = newReviewedJobs >= 3;

        return {
          jobMatches: updatedMatches,
          appliedJobs: newAppliedJobs,
          reviewedJobs: newReviewedJobs,
          allJobsReviewed: allReviewed,
        };
      });
    } catch (error) {
      console.error('Failed to apply for job', error);
    }
  },

  generateCoverLetter: async (data) => {
    try {
      set({ isLoading: 'generatingMessage' });
      const response = await jobMatchesService.generateCoverLetter(data);
      set({ isLoading: false, message: response?.data });
    } catch (error) {
      console.error('Failed to generate message', error);
      set({ isLoading: false, message: '' });
    }
  },

  clearJobMatches: () => {
    set({
      jobMatches: [],
      appliedJobs: 0,
      reviewedJobs: 0,
      allJobsReviewed: false,
      isLoading: false,
      message: '',
    });
  },
}));

export default useJobMatchesStore;
