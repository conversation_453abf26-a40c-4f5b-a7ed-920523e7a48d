import { create } from 'zustand';
import jobMatchesService from '@/zustand/services/jobMatches.services';
import type { ICoverLetterInputs, IJobMatch, IJobMatchBody } from '@/types';

interface JobMatchesState {
  jobMatches: IJobMatch[];
  appliedJobs: number;
  isLoading: boolean | string;
  message: string;
  getJobMatches: (_data: IJobMatchBody) => Promise<void>;
  applyForJob: (_jobId: string, _applyType: string) => Promise<void>;
  generateCoverLetter: (_data: ICoverLetterInputs) => Promise<void>;
}

const useJobMatchesStore = create<JobMatchesState>((set) => ({
  jobMatches: [],
  isLoading: false,
  appliedJobs: 0,
  message: '',

  getJobMatches: async (data) => {
    try {
      set({ isLoading: 'jobMatches' });
      const response = await jobMatchesService.getJobMatches(data);
      set({
        jobMatches: response?.data?.jobs || [],
        appliedJobs: 0,
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to get jobs', error);
      set({ isLoading: false, jobMatches: [] });
    }
  },

  applyForJob: async (jobId: string, applyType) => {
    try {
      set((state) => ({
        jobMatches: state.jobMatches.map((job) =>
          job.id === jobId ? { ...job, applyType } : job
        ),
        appliedJobs: state.appliedJobs + 1,
      }));
    } catch (error) {
      console.error('Failed to apply for job', error);
    }
  },

  generateCoverLetter: async (data) => {
    try {
      set({ isLoading: 'generatingMessage' });
      const response = await jobMatchesService.generateCoverLetter(data);
      set({ isLoading: false, message: response?.data });
    } catch (error) {
      console.error('Failed to generate message', error);
      set({ isLoading: false, message: '' });
    }
  },
}));

export default useJobMatchesStore;
