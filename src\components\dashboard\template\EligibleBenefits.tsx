'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { SectionHeader } from '../components/SectionHeader';
import { BenefitCard } from '../components/benefits/BenefitCard';
import { ActionCard } from '../components/ActionCard';
import ClockIcon from '@/components/icons/ClockIcon';
import { DashboardImage1, DashboardImage2, DashboardImage3 } from '@/assets';
import Inactive from '@/components/modals/InactiveBenefits';
import useSettingsStore from '@/zustand/store/settingsStore';

const benefits = [
  {
    title: 'Family Support Allowance Scheme',
    description:
      "Get financial support to help you manage your family's essential living expenses.",
    banner: DashboardImage1,
    amount: '$300 per month',
  },
  {
    title: 'National Savings Program',
    description:
      'Start saving securely and grow your funds with government-backed benefits.',
    banner: DashboardImage3,
    amount: '$100 per month',
  },
  {
    title: 'Housing Assistance',
    description:
      "Get financial support to help you manage your family's essential living expenses.",
    banner: DashboardImage2,
    amount: '$350 per month',
  },
];

function EligibleBenefits() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const [showModal, setShowModal] = useState(false);
  const brandColor = appearanceSettings?.brandColor;

  const handleBenefitClick = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const handleViewDetails = () => {
    router.push('/dashboard/financial-benefits/benefit-payments');
  };

  return (
    <div className="flex flex-col flex-grow">
      <SectionHeader title="Access eligible benefits" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {benefits.map((benefit) => (
          <BenefitCard
            key={benefit.title}
            title={benefit.title}
            description={benefit.description}
            amount={benefit.amount}
            onClick={handleBenefitClick}
            onViewDetails={handleViewDetails}
            banner={benefit.banner}
          />
        ))}
        <ActionCard
          onClick={() =>
            router.push('/dashboard/financial-benefits/eligible-benefits')
          }
          icon={<ClockIcon stroke={brandColor} />}
          title="View your benefits payments"
          description="Track your upcoming financial benefits and review payment history."
        />
      </div>

      {showModal && <Inactive onClose={closeModal} />}
    </div>
  );
}

export default EligibleBenefits;
