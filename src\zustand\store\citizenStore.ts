import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { useNotificationStore } from '@/zustand/store/notificationStore';

import type { IUser, CitizenState } from '@/types';
import citizenService from '../services/citizen.service';
import { citizenReducer } from '../reducers/citizenReducer';

const initialState: Omit<
  CitizenState,
  'registerCitizen' | 'setCitizen' | 'updateOnboardingData'
> = {
  citizen: null,
  isLoading: false,
  showLoader: false,
};

export const useCitizenStore = create<
  CitizenState & {
    registerPartner: (_partnerData: Omit<IUser, 'userId'>) => Promise<void>;
    setPartner: (_partner: IUser) => void;
    updateOnboardingData: (_data: Partial<IUser>) => void;
  }
>()(
  persist(
    (set) => ({
      ...initialState,

      registerPartner: async (partnerData) => {
        const { showNotification } = useNotificationStore.getState();
        try {
          set((state) => citizenReducer(state, { type: 'START_LOADING' }));
          set((state) => citizenReducer(state, { type: 'SHOW_LOADER' }));

          const registeredCitizen =
            await citizenService.registerCitizen(partnerData);
          set((state) =>
            citizenReducer(state, {
              type: 'SET_CITIZEN',
              payload: registeredCitizen,
            })
          );

          showNotification('Citizen registered successfully!', 'success');
        } catch (error) {
          console.error('Citizen registration failed', error);
          set((state) => citizenReducer(state, { type: 'STOP_LOADING' }));
          set((state) => citizenReducer(state, { type: 'HIDE_LOADER' }));

          showNotification(
            'Citizen registration failed. Please try again.',
            'error'
          );
          throw error;
        }
      },

      setPartner: (partner) => {
        set((state) =>
          citizenReducer(state, { type: 'SET_CITIZEN', payload: partner })
        );
      },

      updateOnboardingData: (data) => {
        set((state) => ({
          ...state,
          citizen: { ...state.citizen, ...data } as IUser,
        }));
      },
    }),
    {
      name: 'partner-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
