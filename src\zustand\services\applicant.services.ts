import axiosClient from '@/utils/axiosClient';
import type { Applicant, ApplicantResponse } from '@/types';

const API_URL = '/Jobs';

const applicantService = {
  getApplicants: async (
    sortField: string | null = null,
    ascending = true,
    status: string | null = null,
    createdById: string | null = null
  ): Promise<ApplicantResponse> => {
    let url = `${API_URL}/applicants/`;

    if (sortField) {
      url += `&sortField=${sortField}&sortOrder=${ascending ? 'asc' : 'desc'}`;
    }

    if (status) {
      url += `&status=${status}`;
    }

    if (createdById) {
      url += `&createdById=${createdById}`;
    }

    const response = await axiosClient.get<ApplicantResponse>(url);
    return response.data;
  },

  getApplicantById: async (id: string): Promise<Applicant> => {
    try {
      const response = await axiosClient.get<Applicant>(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching applicant with id ${id}:`, error);
      throw new Error('Failed to fetch applicant');
    }
  },

  addApplicant: async (applicant: Applicant): Promise<Applicant> => {
    const response = await axiosClient.post<Applicant>(API_URL, applicant);
    return response.data;
  },

  updateApplicant: async (
    applicantId: string,
    updatedApplicant: Partial<Applicant>
  ): Promise<Applicant> => {
    const response = await axiosClient.put<Applicant>(
      `${API_URL}/${applicantId}/application`,
      updatedApplicant
    );
    return response.data;
  },

  deleteApplicant: async (id: string): Promise<void> => {
    await axiosClient.delete(`${API_URL}/${id}`);
  },
};

export default applicantService;
