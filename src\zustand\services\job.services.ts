import axiosClient from '@/utils/axiosClient';
import type { Job } from '@/types/jobTypes';

const API_URL = '/jobs';

const jobService = {
  getJobs: async ({
    page = 1,
    size = 10,
    search = '',
    location = '',
    jobType = '',
    monthlyFrom = '',
    status = 'Active',
  }: {
    page?: number;
    size?: number;
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: string;
    status?: 'Active' | 'Closed';
  }): Promise<{ data: Job[]; page: number; size: number; total: number }> => {
    try {
      const response = await axiosClient.get<{
        data: Job[];
        page: number;
        size: number;
        total: number;
      }>(API_URL, {
        params: {
          page,
          size,
          search,
          location,
          jobType,
          monthlyFrom,
          status,
        },
      });

      return {
        ...response.data,
        data: response.data.data.map((job) => ({
          ...job,
          companyName: job.companyName ?? 'Unknown Company',
        })),
      };
    } catch (error) {
      console.error('Error fetching jobs:', error);
      throw new Error('Failed to fetch jobs');
    }
  },

  getJobsByUserId: async (userId: string): Promise<Job[]> => {
    try {
      const response = await axiosClient.get<{ data: Job[] }>(
        `${API_URL}/my/${encodeURIComponent(userId)}`
      );

      return response.data.data.map((job) => ({
        ...job,
        companyName: job.companyName ?? 'Unknown Company',
      }));
    } catch (error) {
      console.error(`Error fetching jobs for user ${userId}:`, error);
      throw new Error('Failed to fetch user jobs');
    }
  },

  // Fetch a single job by ID
  getJobById: async (id: string): Promise<Job> => {
    try {
      const response = await axiosClient.get<{ data: Job }>(
        `${API_URL}/${encodeURIComponent(id)}`
      );
      return {
        ...response.data.data,
        companyName: response.data.data.companyName ?? 'Unknown Company',
      };
    } catch (error) {
      console.error(`Error fetching job with ID ${id}:`, error);
      throw new Error('Failed to fetch job details');
    }
  },

  // Add a new job
  addJob: async (job: Omit<Job, 'id'>): Promise<Job> => {
    try {
      const response = await axiosClient.post<{ data: Job }>(API_URL, job);
      return response.data.data;
    } catch (error) {
      console.error('Error adding job:', error);
      throw new Error('Failed to add job');
    }
  },

  updateJob: async (id: string, updatedJob: Partial<Job>): Promise<Job> => {
    try {
      const response = await axiosClient.put<{ data: Job }>(
        `${API_URL}/${encodeURIComponent(id)}`,
        updatedJob
      );
      return response.data.data;
    } catch (error) {
      console.error(`Error updating job with ID ${id}:`, error);
      throw new Error('Failed to update job');
    }
  },

  deleteJob: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`${API_URL}/${encodeURIComponent(id)}`);
    } catch (error) {
      console.error(`Error deleting job with ID ${id}:`, error);
      throw new Error('Failed to delete job');
    }
  },
};

export default jobService;
