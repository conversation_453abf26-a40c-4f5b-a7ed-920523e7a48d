'use client';

import type { <PERSON> } from 'react';
import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import { Banner } from '@/assets';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({ subsets: ['latin'] });

export interface LandingPageProps {
  className?: string;
  children?: React.ReactNode;
}

const HeroSection: FC<LandingPageProps> = () => {
  const router = useRouter();

  const handleLoginRedirect = () => {
    router.push('/signup');
  };

  return (
    <section className="relative w-full h-lvh">
      <Image
        src={Banner}
        alt="Career Navigator Background"
        fill
        priority
        className="object-cover object-right w-full h-full -z-10"
      />

      <div className="absolute inset-0 flex flex-col items-center justify-center text-center text-black fade-in-animation w-[80%] md:w-[70%] lg:w-[50%] mx-auto">
        <p className="font-semibold text-primary-500 uppercase text-[14px] leading-[20px]">
          Welcome to the Career Navigator
        </p>
        <h3
          className={`${montserrat.className} font-semibold text-neutral-900 text-[32px] md:text-[52px] leading-tight my-4 lg:leading-[60px]`}
        >
          Your Guide on the Journey to a Fulfilling Career
        </h3>
        <p className="font-medium text-neutral-700 text-[18px] md:text-xl w-4/5 lg:w-[72%]">
          Empowering your ambitions and connecting you to opportunities that
          shape a brighter future.
        </p>

        <Button
          onClick={handleLoginRedirect}
          className="py-[14px] px-[28px] mt-10 mb-5"
        >
          Find your dream job!
        </Button>

        <p className="text-[16px] text-neutral-600 font-normal leading-[20px]">
          Over 2 million+ jobs matched
        </p>
      </div>
    </section>
  );
};

export default HeroSection;
