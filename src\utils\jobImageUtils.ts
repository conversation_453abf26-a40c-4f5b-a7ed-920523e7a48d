/**
 * Utility functions for generating unique job and company images
 */

// Function to generate unique image for each job
export const getJobImage = (jobId: string, companyName?: string, size: number = 48) => {
  // Create a seed based on job ID for consistent but unique images
  const seed = jobId || companyName || 'default';
  
  // Use different image services with the seed for variety
  const imageServices = [
    `https://picsum.photos/seed/${seed}/${size}/${size}`,
    `https://robohash.org/${seed}?set=set4&size=${size}x${size}`,
    `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&size=${size}`,
    `https://api.dicebear.com/7.x/shapes/svg?seed=${seed}&size=${size}`,
    `https://source.unsplash.com/${size}x${size}/?office,business&sig=${seed}`,
  ];
  
  // Use a simple hash to select which service to use
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  const serviceIndex = Math.abs(hash) % imageServices.length;
  return imageServices[serviceIndex];
};

// Function to generate company logo specifically
export const getCompanyLogo = (companyName: string, size: number = 48) => {
  if (!companyName) return getJobImage('default', 'company', size);
  
  // Clean company name for better seed
  const cleanName = companyName.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  // Company-specific image services
  const logoServices = [
    `https://api.dicebear.com/7.x/initials/svg?seed=${cleanName}&size=${size}&backgroundColor=random`,
    `https://robohash.org/${cleanName}?set=set4&size=${size}x${size}&bgset=bg1`,
    `https://picsum.photos/seed/${cleanName}/${size}/${size}`,
    `https://api.dicebear.com/7.x/shapes/svg?seed=${cleanName}&size=${size}&backgroundColor=random`,
  ];
  
  // Use company name hash to select service
  let hash = 0;
  for (let i = 0; i < cleanName.length; i++) {
    const char = cleanName.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  
  const serviceIndex = Math.abs(hash) % logoServices.length;
  return logoServices[serviceIndex];
};

// Function to get fallback image
export const getFallbackImage = (size: number = 48) => {
  return `https://api.dicebear.com/7.x/shapes/svg?seed=fallback&size=${size}&backgroundColor=e0e7ff`;
};

// Function to generate avatar for users
export const getUserAvatar = (userId: string, userName?: string, size: number = 48) => {
  const seed = userId || userName || 'user';
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&size=${size}`;
};
