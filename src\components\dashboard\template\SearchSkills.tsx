'use client';

import React, { useState } from 'react';
import { Search, X, PlusIcon } from 'lucide-react';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { CustomValues } from '@/types/customValues';

const SearchSkills = () => {
  const getSkillsClass = useAdminValues({
    category: AdminValuesCategories?.skillClassification?.category,
    subcategory: AdminValuesCategories?.skillClassification?.subcategory,
  });
  const skillClassification =
    getSkillsClass.data?.data?.data?.customValues || [];

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);

  const filteredSkills = skillClassification.filter((skill: CustomValues) =>
    skill.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const addSkill = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setSearchTerm('');
  };

  const removeSkill = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  return (
    <div className="mt-6">
      {selectedSkills.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          {selectedSkills.map((skill) => (
            <span
              key={skill}
              className="bg-[#cccdd1] text-black px-4 py-2 rounded-md text-[12px] font-medium inline-flex items-center justify-between"
            >
              {skill}
              <X
                onClick={() => removeSkill(skill)}
                className="ml-2 cursor-pointer text-[#71757c]"
                size={16}
              />
            </span>
          ))}
        </div>
      )}

      <div className="flex gap-2 items-center relative w-full">
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
          size={18}
        />

        <input
          type="text"
          placeholder="Search skill..."
          className="border px-10 py-2 w-[75%] rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />

        <button
          onClick={() => addSkill(searchTerm)}
          className={`flex items-center justify-center gap-2 px-2  ${
            searchTerm
              ? 'bg-[--buttonColor] text-white'
              : 'bg-[--buttonColor] text-white cursor-not-allowed'
          }`}
          disabled={!searchTerm}
        >
          <PlusIcon size={16} />
          <p className="text-[16px] !text-white">Add another skill</p>
        </button>
      </div>

      {searchTerm && (
        <div className="border mt-2 bg-white shadow-md rounded-md max-h-40 overflow-y-auto">
          {filteredSkills.length > 0 &&
            filteredSkills.map((skill: CustomValues) => (
              <button
                key={skill.id}
                className="p-2 hover:bg-gray-200 cursor-pointer"
                onClick={() => addSkill(skill.label)}
              >
                {skill.label}
              </button>
            ))}
        </div>
      )}
    </div>
  );
};

export default SearchSkills;
