'use client';

import { useJobStore } from '@/zustand/store/jobStore';
import { Button } from '@/components/ui/button';
import { Search, MapPin, RefreshCcw } from 'lucide-react';
import { useState } from 'react';
import type { Job } from '@/types/jobTypes';
import DropdownInput from './JobDropdown';

const JobSearchInput: React.FC<{
  setSelectedJob?: (_job: Job | null) => void;
}> = ({ setSelectedJob }) => {
  const {
    setSearchTerm,
    setLocation,
    setJobType,
    setMonthlyFrom,
    resetSearch,
    fetchJobs,
    isLoading,
    status,
  } = useJobStore();

  const [tempSearchTerm, setTempSearchTerm] = useState('');
  const [tempLocation, setTempLocation] = useState('');
  const [tempJobType, setTempJobType] = useState<string | undefined>(undefined);
  const [tempMonthlyFrom, setTempMonthlyFrom] = useState('');

  const handleFilterChange = (
    newJobType: string | undefined,
    newMonthlyFrom: string
  ) => {
    setTempJobType(newJobType);
    setTempMonthlyFrom(newMonthlyFrom);
  };

  const handleApplyFilters = () => {
    if (setSelectedJob) {
      setSelectedJob(null);
    }
    setSearchTerm(tempSearchTerm);
    setLocation(tempLocation);
    setJobType(tempJobType === undefined ? '' : tempJobType);
    setMonthlyFrom(tempMonthlyFrom);

    fetchJobs({
      page: 1,
      size: 10,
      search: tempSearchTerm,
      location: tempLocation,
      jobType: tempJobType === undefined ? '' : tempJobType,
      monthlyFrom: tempMonthlyFrom,
      status,
    });
  };

  const handleReset = () => {
    resetSearch();
    setTempSearchTerm('');
    setTempLocation('');
    setTempJobType('');
    setTempMonthlyFrom('');

    fetchJobs({ page: 1, size: 10 });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleApplyFilters();
    }
  };

  return (
    <>
      {/* Search Inputs */}
      <div className="mx-auto justify-center items-center mt-10">
        <div className="relative rounded-full border border-neutral-200 p-[2px] max-w-[738px] mx-auto">
          <div className="flex items-center w-full rounded-full bg-white px-4 py-2">
            <div className="flex items-center w-3/5">
              <Search className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
                placeholder="Search by job title, company, or keywords..."
                value={tempSearchTerm}
                onChange={(e) => setTempSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <span className="px-2 text-gray-400">|</span>
            <div className="flex items-center w-2/5">
              <MapPin className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
                placeholder="Location"
                value={tempLocation}
                onChange={(e) => setTempLocation(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row items-end  my-10 gap-4 mx-auto justify-center w-[85%]">
          <DropdownInput
            selectedJobType={tempJobType}
            selectedSalary={tempMonthlyFrom}
            onFilterChange={handleFilterChange}
          />

          <div className="flex items-center mt-4 md:mt-0 gap-2">
            <Button
              onClick={handleReset}
              variant="outline"
              className="h-[48px]"
              disabled={isLoading}
            >
              <RefreshCcw className="h-4 w-4 text-[--buttonColor]" />
              <span className="hidden sm:inline ml-1 !text-[--buttonColor]">
                Reset
              </span>
            </Button>

            <Button
              onClick={handleApplyFilters}
              className="h-[48px]"
              variant={'default'}
              disabled={isLoading}
            >
              <span className="inline ml-1 !text-white">Search Jobs</span>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobSearchInput;
