interface StatsCardProps {
  title: string;
  value: string;
  subtitle?: string;
}

export default function StatsCard({ title, value, subtitle }: StatsCardProps) {
  return (
    <div className=" w-full flex flex-col items-start border border-blue-100 rounded-lg p-4">
      <p className="text-2xl text-blue-400 font-bold">{value}</p>
      <h3 className="text-[16px] text-gray-500">{title}</h3>
      {subtitle && <p className="text-[16px] text-gray-400">{subtitle}</p>}
    </div>
  );
}
