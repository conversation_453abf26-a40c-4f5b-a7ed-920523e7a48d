'use client';

import { useState } from 'react';
import { ChatInput } from './ChatInput';
import Image from 'next/image';
import AIAssistantIcon from '@/assets/images/ws/assistant.svg';
import CollapseButton from '@/components/icons/CollapseButton';
import useSettingsStore from '@/zustand/store/settingsStore';
import { AiMessage } from './AiMessage';

const initialMessages = [
  {
    id: 1,
    text: '🔍 Find a job I qualify for',
    isUser: true,
    timestamp: new Date(),
  },
  {
    id: 2,
    text: 'I noticed you haven’t uploaded a CV yet. This helps me find better job matches for you. Want to upload it now?',
    isUser: false,
    timestamp: new Date(),
  },
];

interface CareerAssistantProps {
  isMinimized: boolean;
  onToggle: () => void;
}

export const CareerAssistant = ({
  isMinimized,
  onToggle,
}: CareerAssistantProps) => {
  const { appearanceSettings } = useSettingsStore();
  const [messages, setMessages] = useState(initialMessages);
  const { brandColor } = appearanceSettings;

  const handleSendMessage = (message: string) => {
    const newMessage = {
      id: messages.length + 1,
      text: message,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages([...messages, newMessage]);
  };

  return (
    <div className="relative bg-white h-screen right-0 z-50">
      <div
        className={`transform transition-all duration-300 ease-in-out h-[76vh] ${
          isMinimized ? 'translate-x-full' : 'translate-x-0'
        } ${isMinimized ? 'hidden p-6' : 'w-[480px]'}`}
      >
        <div className="bg-white border border-gray-200 flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <Image
                className="w-20 h-20"
                src={AIAssistantIcon || '/placeholder.svg'}
                alt="assistantIcon"
              />
              <span className="font-semibold text-[28px] text-neutral-900 leading-[36px] -tracking-[0.56px]">
                e-Firstname
              </span>
            </div>
            <CollapseButton fill={brandColor} onClick={onToggle} />
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <AiMessage
                key={message.id}
                text={message.text}
                isUser={message.isUser}
              />
            ))}
          </div>

          <div className="p-4 pt-0 border-neutral-200">
            <ChatInput onSendMessage={handleSendMessage} />
          </div>
        </div>
      </div>
    </div>
  );
};
