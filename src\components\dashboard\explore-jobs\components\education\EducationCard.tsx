import CaretDownRedIcon from '@/components/icons/CaretDownRed';
import CaretUpGreenIcon from '@/components/icons/CaretUpGreen';
import EqualToIcon from '@/components/icons/EqualToIcon';
import PlusIcon from '@/components/icons/PlusSvg';
import { educationTrendStaticData } from '@/constants';
import React from 'react';

function TopEducation() {
  return (
    <div className="bg-white rounded-lg p-5 space-y-4">
      <h4 className="text-blue-400 text-xl font-semibold">
        Top Educational Specializations (in demand last 3 years)
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {educationTrendStaticData.top_education.map((yearData) => (
          <div key={yearData.year} className="mb-6">
            <div
              className={`${yearData.data[0] && 'change' in yearData.data[0] ? 'bg-primary-500 text-white' : 'bg-lightGray-50 text-blue-400'} w-full p-2 rounded flex justify-center items-center font-semibold text-lg`}
            >
              {yearData.year}
            </div>

            {/* List of Educations */}
            <ul className="mt-4 space-y-2">
              {yearData.data.map((education) => (
                <li
                  key={education.rank}
                  className="flex items-center space-x-2 p-2 border-b"
                >
                  {'change' in education && education.change && (
                    <span
                      className={
                        education.change === 'up'
                          ? 'text-green-500'
                          : education.change === 'down'
                            ? 'text-red-500'
                            : education.change === 'same'
                              ? 'text-gray-500'
                              : education.change === 'new'
                                ? 'text-blue-500'
                                : ''
                      }
                    >
                      {education.change === 'up' ? (
                        <CaretUpGreenIcon />
                      ) : education.change === 'down' ? (
                        <CaretDownRedIcon />
                      ) : education.change === 'same' ? (
                        <EqualToIcon />
                      ) : education.change === 'new' ? (
                        <PlusIcon />
                      ) : null}
                    </span>
                  )}
                  <span className="bg-lightGray-50 text-blue-400 px-2 py-1 rounded text-[16px] font-semibold">
                    {education.rank}
                  </span>
                  <span className="text-[16px] font-normal text-gray-20">
                    {education.title}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      <div className="py-2 w-full">
        <div className="w-fit mx-auto flex space-x-3">
          <div className="flex items-center">
            <CaretUpGreenIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              up
            </h6>
          </div>
          <div className="flex items-center">
            <CaretDownRedIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              Down
            </h6>
          </div>
          <div className="flex items-center">
            <EqualToIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              Same
            </h6>
          </div>
          <div className="flex items-center">
            <PlusIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              New
            </h6>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TopEducation;
