# Docker Setup for Career Navigator Pro UI

This document provides instructions for running the Career Navigator Pro UI application using Docker.

## Prerequisites

- Docker installed on your machine
- Docker Compose (usually comes with Docker Desktop)

## Quick Start

### 1. Production Build

Build and run the production version:

```bash
# Build the Docker image
docker build -t career-navigator-ui .

# Run the container
docker run -p 3000:3000 career-navigator-ui
```

Or use Docker Compose:

```bash
# Build and run with Docker Compose
docker-compose up --build
```

The application will be available at `http://localhost:3000`

### 2. Development Build (with hot reloading)

For development with hot reloading:

```bash
# Run development environment
docker-compose --profile dev up --build career-navigator-dev
```

The development server will be available at `http://localhost:3001`

## Docker Commands

### Building Images

```bash
# Build production image
docker build -t career-navigator-ui .

# Build development image
docker build -f Dockerfile.dev -t career-navigator-ui:dev .
```

### Running Containers

```bash
# Run production container
docker run -p 3000:3000 --name career-navigator career-navigator-ui

# Run with environment variables
docker run -p 3000:3000 --env-file .env.production career-navigator-ui

# Run in detached mode
docker run -d -p 3000:3000 --name career-navigator career-navigator-ui
```

### Docker Compose Commands

```bash
# Start services
docker-compose up

# Start in detached mode
docker-compose up -d

# Build and start
docker-compose up --build

# Start development environment
docker-compose --profile dev up

# Stop services
docker-compose down

# View logs
docker-compose logs

# View logs for specific service
docker-compose logs career-navigator-ui
```

## Environment Variables

Create environment files for different environments:

### `.env.production`
```env
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://your-api-url.com
# Add other production environment variables
```

### `.env.development`
```env
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000
# Add other development environment variables
```

## Docker Image Optimization

The production Dockerfile uses multi-stage builds for optimization:

1. **deps stage**: Installs dependencies
2. **builder stage**: Builds the application
3. **runner stage**: Creates the final lightweight image

Key optimizations:
- Uses Alpine Linux for smaller image size
- Leverages Next.js standalone output
- Runs as non-root user for security
- Only includes necessary files in final image

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   
   # Use a different port
   docker run -p 3001:3000 career-navigator-ui
   ```

2. **Build failures**
   ```bash
   # Clear Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker build --no-cache -t career-navigator-ui .
   ```

3. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Debugging

```bash
# Access container shell
docker exec -it career-navigator /bin/sh

# View container logs
docker logs career-navigator

# Inspect container
docker inspect career-navigator
```

## Production Deployment

For production deployment, consider:

1. **Using a reverse proxy** (nginx, traefik)
2. **Setting up SSL/TLS certificates**
3. **Configuring health checks**
4. **Setting up monitoring and logging**
5. **Using container orchestration** (Docker Swarm, Kubernetes)

### Example with nginx reverse proxy

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  career-navigator-ui:
    build: .
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - career-navigator-ui
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

## Security Considerations

- The application runs as a non-root user (nextjs:nodejs)
- Sensitive files are excluded via `.dockerignore`
- Environment variables should be used for configuration
- Consider using Docker secrets for sensitive data in production
