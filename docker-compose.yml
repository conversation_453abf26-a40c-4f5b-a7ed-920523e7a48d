services:
  career-navigator-ui:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    # Add any environment variables your app needs
    # env_file:
    #   - .env.production
    restart: unless-stopped
    networks:
      - career-navigator-network

  # Optional: Add a development service
  career-navigator-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - HOSTNAME=0.0.0.0
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - career-navigator-network
    profiles:
      - dev

networks:
  career-navigator-network:
    driver: bridge
