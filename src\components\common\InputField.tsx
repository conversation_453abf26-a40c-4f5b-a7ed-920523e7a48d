import type { FC, ChangeEvent, FocusEvent } from 'react';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { ErrorMessage } from 'formik';

interface InputFieldProps {
  label?: string;
  error?: string;
  name: string;
  type?: string;
  value?: string;
  placeholder?: string;
  handleChange: (_e: ChangeEvent<HTMLInputElement>) => void;
  handleBlur: (_e: FocusEvent<HTMLInputElement>) => void;
  labelClassName?: string;
}

const InputField: FC<InputFieldProps> = ({
  label,
  error,
  name,
  value,
  type,
  placeholder,
  handleBlur,
  handleChange,
  labelClassName = '',
}) => {
  return (
    <>
      {label && (
        <Label
          htmlFor={name}
          className={`text-neutral-900 font-medium text-[16px] ${labelClassName}`}
        >
          {label}
        </Label>
      )}
      <Input
        id={name}
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        className="text-[16px] text-neutral-500 font-normal"
      />
      {error && (
        <ErrorMessage
          name={name}
          component="div"
          className="text-[16px] !text-destructive-500"
        />
      )}
    </>
  );
};

export default InputField;
