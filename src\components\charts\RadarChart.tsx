/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useRef } from 'react';

import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import * as am5radar from '@amcharts/amcharts5/radar';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

export interface RadarChartItem {
  category: string;
  value: number;
  columnSettings: { fill: unknown };
}

interface RadarChartProps {
  data: RadarChartItem[];
}

const RadarChart: React.FC<RadarChartProps> = ({ data }) => {
  const chartDiv = useRef<HTMLDivElement>(null);

  useEffect(() => {
    am5.addLicense('AM5M326788873');
    const root = am5.Root.new(chartDiv.current as HTMLDivElement);

    root.setThemes([am5themes_Animated.new(root)]);

    const chart = root.container.children.push(
      am5radar.RadarChart.new(root, {
        panX: false,
        panY: false,
        wheelX: 'none',
        wheelY: 'none',
        innerRadius: am5.percent(20),
      })
    );

    const xRenderer = am5radar.AxisRendererCircular.new(root, {
      minGridDistance: 50,
      radius: am5.percent(100),
    });

    xRenderer.labels.template.setAll({
      textType: 'regular',
      centerX: am5.p50,
      centerY: am5.p50,
      fontSize: 14,
      populateText: true,
      text: '{category} {value}',
      rotation: 0,
      radius: 13,
    });

    xRenderer.ticks.template.setAll({
      visible: true,
      strokeOpacity: 1,
      strokeWidth: 1,
      stroke: am5.color(0xced4da),
      length: 10,
    });

    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: 'category',
        renderer: xRenderer,
      })
    );

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 40,
        strictMinMax: true,
        renderer: am5radar.AxisRendererRadial.new(root, {
          minGridDistance: 40,
        }),
      })
    );

    // Hide radial labels
    yAxis.get('renderer').labels.template.set('forceHidden', true);

    // Axis label colors
    xAxis.get('renderer').labels.template.setAll({
      fill: am5.color(0x6c757d),
    });

    // Grid lines
    xAxis.get('renderer').grid.template.setAll({
      stroke: am5.color(0xf7f8f9),
      strokeWidth: 1,
      strokeOpacity: 1,
    });

    yAxis.get('renderer').grid.template.setAll({
      stroke: am5.color(0xf7f8f9),
      strokeWidth: 1,
      strokeOpacity: 1,
    });

    // Data columns
    const series = chart.series.push(
      am5radar.RadarColumnSeries.new(root, {
        xAxis,
        yAxis,
        valueYField: 'value',
        categoryXField: 'category',
      })
    );

    series.columns.template.setAll({
      templateField: 'columnSettings',
      strokeOpacity: 0,
      width: am5.p100,
    });

    // Load data
    series.data.setAll(data as any);
    xAxis.data.setAll(data as any);

    return () => root.dispose();
  }, [data]);

  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
        maxWidth: '500px',
        maxHeight: '500px',
        margin: '0 auto',
      }}
    >
      <div
        ref={chartDiv}
        style={{ height: 0, paddingBottom: '100%', maxWidth: '100%' }}
      ></div>
    </div>
  );
};

export default RadarChart;
