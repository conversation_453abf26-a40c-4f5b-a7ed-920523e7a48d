// components/profile-steps/ReviewStep.tsx
import React from 'react';
import { Button } from '@/components/ui/button';
import type { ProfileStep } from '../ProfileCompleteness';
import GraduationCap from '@/components/icons/GraduationCapIcon';
import useSettingsStore from '@/zustand/store/settingsStore';

interface ReviewStepProps {
  step: ProfileStep;
  onComplete: () => void;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ step, onComplete }) => {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="font-medium text-gray-700 mb-4">Up Next:</h3>

        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 p-2 bg-primary-50 rounded-lg">
            <GraduationCap stroke={brandColor} />
          </div>

          <div className="flex-1">
            <h4 className="font-semibold text-neutral-900 text-[18px]">
              {step.title}
            </h4>
            <p className="text-neutral-600 text-[16px] font-normal mt-1">
              {step.description}
            </p>
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <Button onClick={onComplete}>Complete Now!</Button>
      </div>
    </div>
  );
};

export default ReviewStep;
