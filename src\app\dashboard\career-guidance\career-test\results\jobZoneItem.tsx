// import React from 'react';

// import Text from '@/components/ui/text';

// interface JobZoneItemProps {
//   code: number;
//   description: string;
//   isSelected: boolean;
//   onClick: () => void;
// }

// const jobZoneItem: React.FC<JobZoneItemProps> = ({
//   code,
//   description,
//   onClick,
//   isSelected,
// }) => {
//   return (
//     <div
//       className={`
//                 p-4 flex justify-between border-t border-neutral-200
//                 md:py-3 md:px-6
//                 lg:border-none lg:cursor-pointer lg:rounded-xl
//                 ${isSelected ? 'bg-primary-500' : 'bg-transparent'}
//             `}
//       onClick={onClick}
//     >
//       <div className={`mr-4`}>
//         <Text
//           className={`font-semibold text-neutral-700 ${isSelected ? 'lg:text-white' : 'text-neutral-700'}`}
//           variant="large"
//         >
//           Job Zone {code}
//         </Text>
//         <Text className={isSelected ? 'text-white ' : 'text-neutral-600'}>
//           {description}
//         </Text>
//       </div>
//       <div className={`flex justify-center items-center`}>
//         <svg
//           width="24"
//           height="24"
//           viewBox="0 0 24 24"
//           fill="none"
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path
//             className={
//               isSelected ? 'fill-neutral-500 lg:fill-white' : 'fill-neutral-500'
//             }
//             fillRule="evenodd"
//             clipRule="evenodd"
//             d="M8.51192 4.43057C8.82641 4.161 9.29989 4.19743 9.56946 4.51192L15.5695 11.5119C15.8102 11.7928 15.8102 12.2072 15.5695 12.4881L9.56946 19.4881C9.29989 19.8026 8.82641 19.839 8.51192 19.5695C8.19743 19.2999 8.161 18.8264 8.43057 18.5119L14.0122 12L8.43057 5.48811C8.161 5.17361 8.19743 4.70014 8.51192 4.43057Z"
//           />
//         </svg>
//       </div>
//     </div>
//   );
// };

// export default jobZoneItem;
