'use client';

import { cn } from '@/lib/utils';
import type { ReactNode } from 'react';
import React from 'react';

interface TextProps {
  children: ReactNode;
  variant?: 'small' | 'medium' | 'large';
  className?: string;
}

const Text: React.FC<TextProps> = ({
  children,
  variant = 'medium',
  className = '',
}) => {
  const baseClass = 'font-normal leading-[160%]';

  const variantClass = {
    small: 'text-[0.875rem]',
    medium: 'text-[1rem]',
    large: 'text-[1.125rem]',
  };

  const combinedClasses = cn(baseClass, variantClass[variant], className);

  return <p className={combinedClasses}>{children}</p>;
};

export default Text;
