import StageIcon from '@/components/icons/StageSvg';
import useSettingsStore from '@/zustand/store/settingsStore';

// app/components/TopSkillsDemand.tsx
export default function TopSkillsDemand() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  return (
    <div className="mb-6 bg-black-50 rounded-lg p-4 space-y-4">
      <h2 className="text-lg font-semibold text-black-100">
        Top Skills in Demand
      </h2>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <StageIcon stroke={brandColor} />
          <h3 className="text-[16px] font-normal text-blue-400">
            Software Engineering
          </h3>
        </div>
        <p className="text-[16px] text-gray-600">
          Software engineering involves the systematic application of
          engineering approaches to the development, operation, and maintenance
          of software systems. This includes coding, testing, and debugging.
        </p>
        <p className="text-[16px] text-gray-600">
          - 100% of employers are looking for this skill from Software
          Developers.
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <StageIcon stroke={brandColor} />
          <h3 className="text-[16px] font-normal text-blue-400">
            Infrastructure
          </h3>
        </div>
        <p className="text-[16px] text-gray-600">
          Infrastructure refers to the fundamental facilities and systems
          serving an area, including the services and facilities necessary for
          its economy to function. In an IT context, this includes physical
          hardware, networks, software, services, and data centers.
        </p>
        <p className="text-[16px] text-gray-600">
          - 90% of employers are looking for this skill from Software
          Developers.
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <StageIcon stroke={brandColor} />
          <h3 className="text-[16px] font-normal text-blue-400">Leadership</h3>
        </div>
        <p className="text-[16px] text-gray-600">
          SQL or Structured Query Language is a programming language used to
          manage, manipulate and retrieve data stored in relational databases.
          Proficiency in SQL includes understanding syntax, implementing
          queries, and managing database instances.
        </p>
        <p className="text-[16px] text-gray-600">
          - 85% of employers are looking for this skill from Software
          Developers.
        </p>
      </div>

      <h3 className="text-md font-semibold mt-4">Other Skills in Demand</h3>
      <ul className="list-disc pl-6 text-[16px] text-gray-700 mt-2 grid grid-cols-1 md:grid-cols-2 gap-3 font-normal">
        <li>SQL</li>
        <li>Docker</li>
        <li>Angular</li>
        <li>Node.js</li>
        <li>Python</li>
        <li>TypeScript</li>
        <li>Management</li>
        <li>Integration</li>
        <li>Java</li>
        <li>Writing</li>
        <li>Planning</li>
        <li>Research</li>
        <li>Collaboration</li>
        <li>Sales</li>
        <li>Written English</li>
      </ul>
    </div>
  );
}
