'use client';

import type { Route } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import CareerNavigatorLogo from '@/assets/CareerNavigatorLogo.svg';
import { GovernmentEmblem } from '../../GovermentEmblems';
import { partnerBaseUrl } from '@/constants/GLOBAL_BASE_URL';
import useSettingsStore from '@/zustand/store/settingsStore';
import InputField from '@/components/common/InputField';
import { validationMessages } from '@/constants/VALIDATION_MESSAGE';

const { emailInvalid, emailRequired, passwordRequired } = validationMessages;

const validationSchema = Yup.object().shape({
  email: Yup.string().email(emailInvalid).required(emailRequired),
  password: Yup.string().required(passwordRequired),
  rememberMe: Yup.boolean(),
});

function LoginFormTemplate() {
  const { appearanceSettings } = useSettingsStore();
  const { push } = useRouter();
  const { login, isloading } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);

  const redirectToPath = (path: Route) => {
    push(path);
  };

  return (
    <>
      <div className="flex flex-col space-y-8">
        <div className="flex flex-col space-y-8 items-start font-semibold text-neutral-900 dark:text-neutral-100">
          <Image
            src={appearanceSettings?.platformLogoDark || CareerNavigatorLogo}
            alt="Logo"
            width={300}
            height={40}
            className="h-[40px] w-auto cursor-pointer"
            onClick={() => redirectToPath('/')}
          />
          <h2 className="text-neutral-900 md:text-[28px] text-[28px] font-semibold leading-[36px]">
            Login as a Citizen
          </h2>
        </div>
        <div className="w-full mx-auto">
          <Formik
            initialValues={{ email: '', password: '', rememberMe: false }}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                await login({
                  email: values.email,
                  password: values.password,
                  role: 'User',
                });
                redirectToPath('/dashboard');
              } catch (error) {
                console.error('login failed:', error);
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ handleChange, handleBlur, values, isSubmitting, errors }) => (
              <Form className="grid grid-cols-1 gap-4">
                <div className="space-y-1">
                  <InputField
                    label="Email Address"
                    error={errors.email}
                    name={'email'}
                    type={'email'}
                    placeholder="<EMAIL>"
                    value={values.email}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                {/* Password Field */}
                <div className="relative space-y-1">
                  <button
                    type="button"
                    className="absolute right-3 top-[48px] transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-primary-500" />
                    ) : (
                      <Eye className="h-5 w-5 text-primary-500" />
                    )}
                  </button>
                  <InputField
                    label="Password"
                    error={errors.password}
                    name={'password'}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    value={values.password}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                <p className="text-primary-500 text-[16px] font-semibold leading-[16px] cursor-pointer">
                  Forgot Password?
                </p>

                <Button
                  type="submit"
                  disabled={isloading || isSubmitting}
                  className="justify-center my-4 bg-[--buttonColor] flex items-center gap-2 text-[16px] py-[14px] px-[28px] h-[48px]"
                >
                  {isloading || isSubmitting ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    'Log in as a Citizen'
                  )}
                </Button>

                <Link
                  href={'/signup' as Route}
                  className="text-[16px] font-normal text-center text-[--bodyTextColor]"
                >
                  <p className="">
                    Don’t have an account?{' '}
                    <span className="font-semibold ml-1 text-primary-500 hover:underline cursor-pointer">
                      Sign Up
                    </span>
                  </p>
                </Link>
              </Form>
            )}
          </Formik>
        </div>

        <div className="flex items-center w-full gap-10">
          <div className="flex-1 h-px bg-neutral-200" />
          <p className="text-[16px] text-neutral-500 font-normal leading-[27px]">
            Or
          </p>
          <div className="flex-1 h-px bg-neutral-200" />
        </div>

        <Button
          variant="outline"
          onClick={() => redirectToPath(partnerBaseUrl)}
          className="justify-center flex items-center gap-2 text-[18px] py-[14px] px-[28px] h-[48px] font-medium"
        >
          Continue as a Partner Company
        </Button>
      </div>

      <GovernmentEmblem />
    </>
  );
}

export default LoginFormTemplate;
