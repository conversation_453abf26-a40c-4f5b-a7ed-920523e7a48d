'use client';

import type React from 'react';

import { Button } from '@/components/ui/button';
import GraduationCap from '@/components/icons/GraduationCapIcon';
import useSettingsStore from '@/zustand/store/settingsStore';
import type { ProfileStep } from '../ProfileCompleteness';
interface EducationProgramStepProps {
  step: ProfileStep;
  onComplete: () => void;
  onSkip: () => void;
}

const EducationProgramStep: React.FC<EducationProgramStepProps> = ({
  step,
  onComplete,
  onSkip,
}) => {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  return (
    <div>
      <div className="mb-4">
        <h3 className="font-medium text-gray-700 mb-4">Up Next:</h3>

        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 p-2 bg-primary-50 rounded-lg">
            <GraduationCap stroke={brandColor} />
          </div>

          <div className="flex-1">
            <h4 className="font-semibold text-neutral-900 text-[18px]">
              {step.title}
            </h4>
            <p className="text-neutral-600 text-[16px] font-normal mt-1">
              {step.description}
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 mt-6">
        {step.canSkip && (
          <Button variant="outline" onClick={onSkip}>
            Skip This Step
          </Button>
        )}
        <Button onClick={onComplete}>Complete Now!</Button>
      </div>
    </div>
  );
};

export default EducationProgramStep;
