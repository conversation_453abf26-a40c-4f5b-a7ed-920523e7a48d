import Image from 'next/image';
import React from 'react';

interface AssistantTooltipProps {
  imgUrl?: string;
  avatarPosition?:
    | 'top-left'
    | 'top-center'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-center'
    | 'bottom-right';
  arrowPosition?:
    | 'top-left'
    | 'top-center'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-center'
    | 'bottom-right';
  children?: React.ReactNode;
}

// Helper for avatar positioning
const avatarClasses = {
  'top-left':
    'absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 ml-14',
  'top-center':
    'absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
  'top-right':
    'absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2',
  'bottom-left':
    'absolute bottom-0 left-0 transform -translate-x-1/2 translate-y-1/2',
  'bottom-center':
    'absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2',
  'bottom-right':
    'absolute bottom-0 right-0 transform translate-x-1/2 translate-y-1/2',
};

// Helper for arrow positioning
const arrowClasses = {
  'top-left':
    'absolute bottom-full left-0 transform -translate-x-1/2 -mb-3 ml-14',
  'top-center':
    'absolute bottom-full left-1/2 transform -translate-x-1/2 -mb-3',
  'top-right': 'absolute bottom-full right-0 transform translate-x-1/2 -mb-3',
  'bottom-left': 'absolute top-full left-0 transform -translate-x-1/2 mt-3',
  'bottom-center': 'absolute top-full left-1/2 transform -translate-x-1/2 mt-3',
  'bottom-right': 'absolute top-full right-0 transform translate-x-1/2 mt-3',
};

const AssistantTooltip: React.FC<AssistantTooltipProps> = ({
  imgUrl = '/images/ws/assistant.svg',
  avatarPosition = 'top-center',
  arrowPosition = 'top-center',
  children,
}) => {
  return (
    <div className={`relative flex items-center justify-center  w-full`}>
      <Image
        height={100}
        width={100}
        src={imgUrl}
        alt="Avatar"
        className={` object-cover w-[200px] h-[200px]  ${avatarClasses[avatarPosition]}`}
      />

      <div className="relative bg-white rounded-xl text-center w-full mt-20">
        <div
          className={`w-5 h-5 bg-white rotate-45  ${arrowClasses[arrowPosition]}`}
        ></div>

        {children}
      </div>
    </div>
  );
};

export default AssistantTooltip;
