
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import AssistantTooltip from '@/components/common/AssistantTooltip';
import { useRouter } from 'next/navigation';

interface SuccessModalProps {
  open: boolean;
  setOpen: (_open: boolean) => void;
}

const SuccessModal: React.FC<SuccessModalProps> = ({ open }) => {
  const router = useRouter();

  const handleHome = () => {
    router.push('/dashboard/career-opportunities/applied-jobs');
  };

  return (
    <Dialog open={open} onOpenChange={() => { }}>
      {/* {open && (
        <>
          <div className="absolute top-0 left-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={150}
              gravity={0.1}
              confettiSource={{ x: 0, y: 0, w: 0, h: window.innerHeight }}
            />
          </div>

          <div className="absolute top-0 right-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={150}
              gravity={0.1}
              confettiSource={{
                x: window.innerWidth / 2,
                y: 0,
                w: 0,
                h: window.innerHeight,
              }}
            />
          </div>
        </>
      )} */}

      <DialogContent
        className="sm:max-w-[600px] shadow-none"
        onInteractOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-left">
            <AssistantTooltip
              avatarPosition="top-left"
              arrowPosition="top-left"
            >
              <div className="text-left px-4 flex flex-col space-y-4">
                <h2 className="text-[28px] font-semibold text-neutral-900 mt-4">
                  🎉 Success!
                </h2>

                <p className="text-neutral-500 mt-6 text-[18px] font-normal">
                  Your application has been submitted!
                </p>

                <p className="text-neutral-500 mt-2 text-[18px] font-normal leading-[28px]">
                  The employer will reach out if you&apos;re selected to move
                  forward to the next step.
                </p>

                <div className="mt-6 space-y-4">
                  <Button
                    onClick={() =>
                      router.push('/dashboard/career-opportunities/jobs')
                    }
                    className="w-full px-[28px] py-[14px] h-[48px]"
                  >
                    Apply for another job
                  </Button>

                  <Button
                    onClick={handleHome}
                    variant="outline"
                    className="w-full px-[28px] py-[14px] h-[48px]"
                  >
                    Track My applications
                  </Button>
                </div>
              </div>
            </AssistantTooltip>
          </DialogTitle>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessModal;
