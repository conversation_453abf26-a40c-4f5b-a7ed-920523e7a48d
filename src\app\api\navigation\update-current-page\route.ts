import { NextResponse } from 'next/server';

// This is a mock implementation of the update-current-page API endpoint
// In a real application, this would interact with a database or external service

// Store the current page ID in memory (this would be in a database in a real app)
let currentPage = 'dashboard';
let lastUpdateTime = Date.now();

export async function POST(request: Request) {
  try {
    const { pageId, priority } = await request.json();

    if (!pageId) {
      return NextResponse.json(
        { success: false, message: 'Page ID is required' },
        { status: 400 }
      );
    }

    console.log(`Received request to update current page to: ${pageId} with priority: ${priority || 'normal'}`);

    // If priority is high, always update regardless of timing
    // Otherwise, implement a cooldown period to prevent rapid changes
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateTime;
    
    // If priority is high or it's been more than 2 seconds since the last update
    if (priority === 'high' || timeSinceLastUpdate > 2000) {
      currentPage = pageId;
      lastUpdateTime = now;
      console.log(`Current page updated to: ${pageId}`);
    } else {
      console.log(`Update request for ${pageId} ignored due to recent update (${timeSinceLastUpdate}ms ago)`);
    }

    return NextResponse.json({
      success: true,
      message: 'Current page updated successfully',
      data: {
        pageId: currentPage,
        wasUpdated: currentPage === pageId
      }
    });
  } catch (error) {
    console.error('Error updating current page:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update current page', error: String(error) },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Current page retrieved successfully',
    data: {
      pageId: currentPage
    }
  });
}
