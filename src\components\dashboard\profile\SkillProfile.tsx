'use client';
import { useState } from 'react';
import { X, ChevronDown, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

const skillSuggestions = [
  'JavaScript',
  'React',
  'Node.js',
  'Python',
  'Data Analysis',
  'UI/UX Design',
  'Project Management',
];

const SkillsProfile = () => {
  const [inputValue, setInputValue] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);

  const [manuallyAddedSkills, setManuallyAddedSkills] = useState([
    'Wireframing',
    'Prototyping',
    'Information Architecture',
  ]);

  const [extractedSkills, setExtractedSkills] = useState([
    'Angular (Web Framework)',
    'Blockchain',
    'Bootstrap (Front-End Framework)',
    'Finance',
    'Market Research',
    'Ideation',
    'Cybersecurity',
    'Blockchain',
    'Artificial Intelligence & Machine Learning',
    'Project Management',
    'Blockchain Knowledge',
    'Financial Literacy',
    'Agile Methodologies',
  ]);

  const [courseSkills, setCourseSkills] = useState([
    'Ideation',
    'Market Research',
    'Ideation',
    'Ideation',
    'Ideation',
    'Market Research',
    'Market Research',
    'Research',
    'Market Research',
    'Research',
  ]);

  const filteredSuggestions = skillSuggestions.filter(
    (skill) =>
      skill.toLowerCase().includes(inputValue.toLowerCase()) &&
      !manuallyAddedSkills.includes(skill) &&
      !extractedSkills.includes(skill) &&
      !courseSkills.includes(skill)
  );

  const handleAddSkill = (skill: string) => {
    if (skill.trim() && !manuallyAddedSkills.includes(skill)) {
      setManuallyAddedSkills([...manuallyAddedSkills, skill.trim()]);
      setInputValue('');
      setShowDropdown(false);
    }
  };

  const handleRemoveManualSkill = (skillToRemove: string) => {
    setManuallyAddedSkills(
      manuallyAddedSkills.filter((skill) => skill !== skillToRemove)
    );
  };

  const handleRemoveExtractedSkill = (skillToRemove: string) => {
    setExtractedSkills(
      extractedSkills.filter((skill) => skill !== skillToRemove)
    );
  };

  const handleRemoveCourseSkill = (skillToRemove: string) => {
    setCourseSkills(courseSkills.filter((skill) => skill !== skillToRemove));
  };

  return (
    <div className="border border-neutral-200 rounded-md p-6 mt-4">
      <div className="mb-6">
        <h2 className="text-[20px] font-semibold text-neutral-900 leading-[28px] mb-2">
          Skills Profile
        </h2>
        <p className="text-[16px] font-normal text-neutral-500 leading-[24px]">
          Skills combined from your uploaded CV and Study Path to deliver
          smarter, more accurate job recommendations.
        </p>
      </div>

      <div className="mb-6 relative">
        <div className="relative w-full">
          <div className="relative flex items-center border border-neutral-200 rounded-lg h-12 max-w-[440px] ">
            <Search className="ml-3 text-neutral-400" />
            <Input
              placeholder="Add skill..."
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
                setShowDropdown(true);
              }}
              onFocus={() => setShowDropdown(true)}
              className="border-none  pl-4 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-neutral-50 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 md:text-[16px] max-w-[440px]"
            />
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
          </div>
          {showDropdown && filteredSuggestions.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-white border border-neutral-200 rounded-md shadow-lg">
              {filteredSuggestions.map((skill) => (
                <button
                  key={skill}
                  className="px-4 py-2 hover:bg-neutral-100 cursor-pointer flex flex-col"
                  onClick={() => handleAddSkill(skill)}
                >
                  {skill}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="font-semibold text-neutral-900 mb-3 text-[16px] leading-[24px]">
            Manually Added ({manuallyAddedSkills.length})
          </h3>
          <div className="flex flex-wrap gap-2">
            {manuallyAddedSkills.map((skill) => (
              <div
                key={skill}
                className="flex items-center px-3 py-1 bg-neutral-100 text-neutral-800 rounded-md text-[14px] font-medium"
              >
                {skill}
                <button
                  onClick={() => handleRemoveManualSkill(skill)}
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-neutral-900 mb-3 text-[16px] leading-[24px]">
            Extracted from CV ({extractedSkills.length})
          </h3>
          <div className="flex flex-wrap gap-2">
            {extractedSkills.map((skill) => (
              <div
                key={skill}
                className="flex items-center px-3 py-1 bg-neutral-100 text-neutral-800 rounded-md text-[14px] font-medium"
              >
                {skill}
                <button
                  onClick={() => handleRemoveExtractedSkill(skill)}
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-neutral-900 mb-3 text-[16px] leading-[24px]">
            Added from your completed courses ({courseSkills.length})
          </h3>
          <div className="flex flex-wrap gap-2">
            {courseSkills.map((skill) => (
              <div
                key={skill}
                className="flex items-center px-3 py-1 bg-neutral-100 text-neutral-800 rounded-md text-[14px] font-medium"
              >
                {skill}
                <button
                  onClick={() => handleRemoveCourseSkill(skill)}
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillsProfile;
