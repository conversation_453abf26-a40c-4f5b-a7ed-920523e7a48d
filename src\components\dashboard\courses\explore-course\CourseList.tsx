'use client';

import { Card } from '@/components/ui/card';
import { useSearchParams } from 'next/navigation';
import { AdminValuesCategories } from '@/constants';
import { useAdminValues, useCourses } from '@/queries';
import type { Cities, Topic } from '@/types';
import type { CourseFilters } from '@/types/courseType';
import { CourseFilter } from './CourseFilter';
import { CourseListContent } from './CourseListContent';
import { CourseListSkeleton } from './CourseListSkeleton';
import { useState } from 'react';

interface CourseListProps {
  initialMainTopic?: string;
}

export default function CourseList({ initialMainTopic }: CourseListProps) {
  const { data: topicsData } = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });
  const mainTopics: Topic[] = topicsData?.data?.data?.customValues || [];

  const { data: citiesData } = useAdminValues({
    category: AdminValuesCategories?.cities?.category,
    subcategory: AdminValuesCategories?.cities?.subcategories?.Level_2,
  });
  const cities: Cities[] = citiesData?.data?.data?.customValues || [];

  const searchParams = useSearchParams();
  const searchParam = searchParams.get('search');
  const mainTopicParam = searchParams.get('mainTopic');
  const cityParam = searchParams.get('city');

  const [filters, setFilters] = useState<CourseFilters>({
    page: 1,
    pageSize: 6,
    mainTopic: initialMainTopic || mainTopicParam || undefined,
    search: searchParam || undefined,
    deliveryMode: undefined,
    experienceLevel: undefined,
    city: cityParam ?? undefined,
    startDate: undefined,
    endDate: undefined,
    duration: undefined,
    courseFee: undefined,
  });

  const { data, isLoading } = useCourses(filters);
  const courses = data?.data ?? [];

  const applyFilters = async (filters: CourseFilters) => {
    setFilters((prev) => ({ ...prev, ...filters }));
  };

  const handleResetFilters = () => {
    setFilters({
      page: 1,
      pageSize: 6,
      mainTopic: initialMainTopic || '',
      search: '',
      deliveryMode: undefined,
      experienceLevel: undefined,
      city: undefined,
      startDate: undefined,
      endDate: undefined,
      duration: undefined,
      courseFee: undefined,
    });

    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('search');
      url.searchParams.delete('mainTopic');
      url.searchParams.delete('city');
      window.history.replaceState({}, '', url.toString());
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= (data?.totalPages ?? 0)) {
      setFilters((prev) => ({ ...prev, page: newPage }));
    }
  };

  return (
    <div className="w-full">
      <h1 className="font-semibold mb-8 text-neutral-900 text-[40px] leading-[24px] my-10">
        Search Courses
      </h1>
      <div className="grid grid-cols-1 lg:grid-cols-[300px_1fr] gap-8">
        <Card className="h-fit rounded-lg">
          <CourseFilter
            filters={filters}
            mainTopics={mainTopics}
            cities={cities}
            isLoading={isLoading}
            ApplyFilters={applyFilters}
            onResetFilters={handleResetFilters}
          />
        </Card>

        {isLoading ? (
          <CourseListSkeleton />
        ) : (
          <CourseListContent
            courses={courses}
            isLoading={isLoading}
            page={data?.page ?? 0}
            totalPages={data?.totalPages ?? 0}
            totalCourses={data?.total ?? 0}
            pageSize={filters.pageSize ?? 6}
            handlePageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
}
