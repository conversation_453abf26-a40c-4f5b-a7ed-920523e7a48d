'use client';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import Trends from '../components/Trends';
import Education from '../components/education';
import { ExploreJobsTrendTab } from '@/constants/NAV_CONFIG';
import Occupations from '../components/Occupations';

export default function ExplorerTab() {
  return (
    <div className="w-full space-y-4">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'Career Guidance', href: '/dashboard' },
          { label: 'Explore Job Trends', href: '/' },
        ]}
      />
      <Tabs defaultValue="Trends" className="w-full">
        <TabsList className="bg-transparent mb-3 gap-6">
          {ExploreJobsTrendTab.map((tab) => (
            <TabsTrigger
              key={tab}
              value={tab}
              className={`flex-1 text-center font-semibold text-lg leading-[24.38px] text-gray-20 transition-all px-0`}
            >
              {tab}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="Trends" className="">
          <Trends />
        </TabsContent>
        <TabsContent value="Occupations" className="">
          <Occupations />
        </TabsContent>
        <TabsContent value="Education" className="">
          <Education />
        </TabsContent>
      </Tabs>
    </div>
  );
}
