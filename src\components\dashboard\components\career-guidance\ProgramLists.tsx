'use client';

import { useEffect, useState } from 'react';
import { Loader2, Trash2 } from 'lucide-react';
import { useGetUserPrograms, useDeleteUserProgramCourses } from '@/queries';
import { MyProgramsGraduateCapIcon } from '@/components/icons/MyProgramCapSvg';
import EditIcon from '@/components/icons/EditSvg';
import type { Program } from '@/types';
import { useRouter } from 'next/navigation';
import { useNotificationStore } from '@/zustand/store/notificationStore';
import { DeleteProgramDialog } from './DeleteProgramDialog';

export function ProgramsList() {
  const { data: userProgramsResponse, isLoading: isProgramsLoading } =
    useGetUserPrograms();
  const [localPrograms, setLocalPrograms] = useState<Program[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [programToDelete, setProgramToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const router = useRouter();
  const { showNotification } = useNotificationStore();
  const { mutate: deleteProgramsProgramCourses, isPending: isDeleting } =
    useDeleteUserProgramCourses();

  const programData = userProgramsResponse?.data;

  console.log(programData);

  useEffect(() => {
    if (programData) {
      setLocalPrograms(programData);
    }
  }, [programData]);

  const handleEdit = (id: string) => {
    router.push(`/dashboard/career-guidance/program-builder/${id}`);
  };

  const openDeleteDialog = (id: string, name: string) => {
    setProgramToDelete({ id, name });
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = () => {
    if (!programToDelete) return;

    try {
      const program = localPrograms.find((p) => p.id === programToDelete.id);

      if (!program) {
        showNotification('Program not found!', 'error');
        return;
      }

      const courseIds = program.courses.map((course) => course.id);

      deleteProgramsProgramCourses(
        {
          programId: programToDelete.id,
          courseIds,
        },
        {
          onSuccess: () => {
            setLocalPrograms((prev) =>
              prev.filter((p) => p.id !== programToDelete.id)
            );
            showNotification('Program deleted successfully!', 'success');
          },
          onError: () => {
            showNotification(
              'Failed to delete program. Please try again.',
              'error'
            );
          },
          onSettled: () => {
            setIsDeleteDialogOpen(false);
            setProgramToDelete(null);
          },
        }
      );
    } catch (error) {
      console.error('Error deleting program:', error);
      showNotification('An unexpected error occurred', 'error');
      setIsDeleteDialogOpen(false);
      setProgramToDelete(null);
    }
  };

  if (isProgramsLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            My Programs
          </h2>
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div
                // eslint-disable-next-line react/no-array-index-key
                key={i}
                className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
              >
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 mt-0.5 bg-gray-100 rounded-full animate-pulse"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-100 rounded w-48 animate-pulse"></div>
                    <div className="h-3 bg-gray-100 rounded w-32 animate-pulse"></div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 bg-gray-100 rounded animate-pulse"></div>
                  <div className="h-8 w-8 bg-gray-100 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6">
          <h2 className="text-[24px] font-semibold text-neutral-900 mb-4 leading-[24px]">
            My Programs
          </h2>
          <div className="space-y-4">
            {localPrograms.length > 0 ? (
              localPrograms.map((program) => (
                <div
                  key={program.id}
                  className="flex items-center justify-between py-3 px-4 border border-neutral-200 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <MyProgramsGraduateCapIcon />
                    <div>
                      <h3 className="font-semibold text-neutral-700 text-[18px] leading-[28px]">
                        {program.name}
                      </h3>
                      <p className="text-[16px] text-neutral-500 font-normal leading-[24px]">
                        {program.code || 'PROGRAMCODE'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => handleEdit(program.id)}
                      className="text-primary-500 hover:text-primary-600"
                    >
                      <EditIcon />
                    </button>

                    <button
                      onClick={() => openDeleteDialog(program.id, program.name)}
                      className="text-destructive-500 hover:text-destructive-600"
                      disabled={isDeleting}
                    >
                      {isDeleting && programToDelete?.id === program.id ? (
                        <Loader2 className="h-7 w-7 animate-spin" />
                      ) : (
                        <Trash2 className="h-7 w-7" />
                      )}
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-neutral-500 text-[16px] leading-[24px] font-medium">
                  No programs found
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <DeleteProgramDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        programToDelete={programToDelete}
        onDelete={handleDelete}
        isDeleting={isDeleting}
      />
    </>
  );
}
