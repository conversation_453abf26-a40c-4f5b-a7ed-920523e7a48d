'use client';

import type React from 'react';
import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { CustomValues } from '@/types/customValues';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const yearsOptions = [
  'Less than 1 year',
  '1 to less than 3 years',
  '3 to less than 5 years',
  '5 to less than 10 years',
  '10+ years',
] as const;

const ProfessionalInfoCard = () => {
  /* ---------------- Admin values (sectors) ------------------- */
  const getSectorClass = useAdminValues({
    category: AdminValuesCategories?.sector?.category,
  });
  const sectorClass: CustomValues[] =
    getSectorClass.data?.data?.data?.customValues || [];

  /* ---------------- Local state ------------------------------ */
  const [selectedSectors, setSelectedSectors] = useState<CustomValues[]>([]);
  const [experience, setExperience] = useState<Record<string, string>>({});
  const [open, setOpen] = useState(false);

  /* ---------------- Handlers --------------------------------- */
  const handleSelect = (value: string) => {
    const selectedItem = sectorClass.find((item) => item.value === value);

    if (selectedItem) {
      if (selectedSectors.some((item) => item.value === value)) {
        setSelectedSectors((prev) =>
          prev.filter((item) => item.value !== value)
        );
      } else if (selectedSectors.length < 3) {
        setSelectedSectors((prev) => [...prev, selectedItem]);
      }
    }
  };

  const handleRemove = (value: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setSelectedSectors((prev) => prev.filter((item) => item.value !== value));
  };

  const handleExperienceChange = (sector: string, val: string) => {
    setExperience((prev) => ({ ...prev, [sector]: val }));
  };

  /* ---------------- Render ----------------------------------- */
  return (
    <div className="bg-white text-left">
      <form className="mt-8 space-y-6">
        <div className="space-y-1">
          <Label className="text-neutral-900 text-[18px] font-semibold leading-[28px]">
            Preferred company sectors (up to 3):
          </Label>

          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <button
                className="flex flex-wrap items-center gap-1 p-2 border rounded-md min-h-10 cursor-pointer"
                onClick={() => setOpen(true)}
              >
                {selectedSectors.length > 0 ? (
                  selectedSectors.map((item) => (
                    <Badge
                      key={item.value}
                      variant="secondary"
                      className="text-[16px] py-1 px-3 bg-[#E2E4E9] text-[#111827] font-normal rounded-md"
                    >
                      {item.label}
                      <button
                        type="button"
                        className="ml-2 text-black hover:bg-transparent"
                        onClick={(e) => handleRemove(item.value, e)}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </Badge>
                  ))
                ) : (
                  <span className="text-muted-foreground">Please Select</span>
                )}
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Command>
                <CommandInput placeholder="Search sectors..." />
                <CommandList>
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup>
                    {sectorClass.map((item) => {
                      const isSelected = selectedSectors.some(
                        (selected) => selected.value === item.value
                      );
                      return (
                        <CommandItem
                          key={item.value}
                          value={item.value}
                          onSelect={handleSelect}
                          disabled={selectedSectors.length >= 3 && !isSelected}
                          className={cn(
                            'flex items-center gap-2',
                            selectedSectors.length >= 3 &&
                              !isSelected &&
                              'opacity-50 cursor-not-allowed'
                          )}
                        >
                          <div
                            className={cn(
                              'flex-shrink-0 mr-2 h-4 w-4 rounded-sm border flex items-center justify-center',
                              isSelected
                                ? 'bg-primary border-primary'
                                : 'border-input'
                            )}
                          >
                            {isSelected && (
                              <Check className="h-3 w-3 text-primary-foreground" />
                            )}
                          </div>
                          {item.label}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>

          {selectedSectors.length >= 3 && (
            <p className="text-[16px] text-destructive-500 mt-1">
              You&apos;ve reached the maximum of 3 sectors. Remove one to add
              another.
            </p>
          )}
        </div>

        {selectedSectors.map((sector) => (
          <div key={sector.value} className="space-y-1">
            <Label className="text-neutral-900 text-[18px] font-semibold leading-[28px]">
              Years of work experience in {sector.label}:
            </Label>
            <select
              value={experience[sector.value] ?? ''}
              onChange={(e) =>
                handleExperienceChange(sector.value, e.target.value)
              }
              className="border rounded p-2 w-full"
            >
              <option value="" disabled>
                Please Select
              </option>
              {yearsOptions.map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
          </div>
        ))}
      </form>
    </div>
  );
};

export default ProfessionalInfoCard;
