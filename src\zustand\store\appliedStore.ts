import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { AppliedJobState } from '@/types/jobTypes';
import appliedJobService from '../services/appliedJob.service';

const initialState: AppliedJobState = {
  jobs: [],
  currentJob: null,
  isLoading: false,
  page: 1,
  size: 10,
  total: 0,
  searchTerm: '',
  location: '',
  jobType: '',
  monthlyFrom: '',
};

type AppliedJobStore = AppliedJobState & {
  // eslint-disable-next-line no-unused-vars
  fetchJobs: (params?: {
    page?: number;
    size?: number;
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: string;
    userId?: string;
    ascending?: boolean;
  }) => Promise<void>;
  setSearchTerm: (_searchTerm: string) => void;
  setLocation: (_location: string) => void;
  setJobType: (_jobType: string) => void;
  setMonthlyFrom: (_monthlyFrom: string) => void;
  resetSearch: () => void;
};

export const useAppliedJobStore = create<AppliedJobStore>()(
  persist(
    (set) => ({
      ...initialState,

      fetchJobs: async ({
        page = 1,
        size = 10,
        search = '',
        location = '',
        jobType = '',
        monthlyFrom = '',
        userId = '',
        ascending = false,
      } = {}) => {
        try {
          await appliedJobService.getAppliedJobs({
            page,
            size,
            search,
            location,
            jobType,
            monthlyFrom,
            userId,
            ascending,
          });
        } catch (error) {
          console.error('Failed to fetch jobs:', error);
        }
      },

      setSearchTerm: (searchTerm: string) => {
        set({ searchTerm });
      },

      setLocation: (location: string) => {
        set({ location });
      },

      setJobType: (jobType: string) => {
        set({ jobType });
      },

      setMonthlyFrom: (monthlyFrom: string) => {
        set({ monthlyFrom });
      },

      resetSearch: () => {
        set({
          searchTerm: '',
          location: '',
          jobType: '',
          monthlyFrom: '',
        });
      },
    }),
    {
      name: 'applied-job-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
