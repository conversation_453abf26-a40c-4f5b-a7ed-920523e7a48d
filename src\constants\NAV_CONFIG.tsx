export const navConfig = [
  {
    id: 'benefits',
    name: 'Benefits',
    path: '/benefits',
    children: [
      {
        id: 'resume-builder',
        name: 'Resume Builder',
        path: '/services/resume-builder',
      },
      {
        id: 'career-advice',
        name: 'Career Advice',
        path: '/services/career-advice',
      },
    ],
  },
  {
    id: 'partners',
    name: 'Our Partners',
    path: '/our-partners',
    children: [
      {
        id: 'resume-builder',
        name: 'Resume Builder',
        path: '/services/resume-builder',
      },
      {
        id: 'career-advice',
        name: 'Career Advice',
        path: '/services/career-advice',
      },
    ],
  },
  {
    id: 'about',
    name: 'About',
    path: '/about',
  },
  {
    id: 'faqs',
    name: 'FAQS',
    path: '/faqs',
  },
  {
    id: 'contact',
    name: 'Contact Us',
    path: '/contact',
  },
];

export const dashboardSideBarRoutes = [
  {
    label: 'Home',
    route: '/dashboard',
  },
  {
    label: 'Career Opportunities',
    children: [
      {
        label: 'Jobs',
        route: '/dashboard/career-opportunities/jobs',
      },
      {
        label: 'Applied Jobs',
        route: '/dashboard/career-opportunities/applied-jobs',
      },
    ],
  },
  {
    label: 'Upskilling',
    children: [
      {
        label: 'My Upskilling Journey',
        route: '/dashboard/upskilling/my-upskilling-journey',
      },
      {
        label: 'All Courses',
        route: '/dashboard/upskilling/all-courses',
      },
    ],
  },
  {
    label: 'Financial Benefits',
    children: [
      {
        label: 'Eligible Benefits',
        route: '/dashboard/financial-benefits/eligible-benefits',
      },
      {
        label: 'Benefits Payments',
        route: '/dashboard/financial-benefits/benefit-payments',
      },
    ],
  },
  {
    label: 'Career Guidance',
    children: [
      {
        label: 'Career Test',
        route: '/dashboard/career-guidance/career-test',
      },
      {
        label: 'Program Builder',
        route: '/dashboard/career-guidance/program-builder',
      },
      {
        label: 'Explore Job Trends',
        route: '/dashboard/career-guidance/job-trends',
      },
    ],
  },
];

export const ExploreJobsTrendTab = ['Trends', 'Occupations', 'Education'];

export const STUDENTS_ROUTES_NAMES: Record<string, string> = {
  STUDENTDASHBOARD: '/student-dashboard',
  TRENDINGOCCUPATIONS: '/trending-occupations',
  TOPSKILLS: '/top-skills',
  ASSESSMENTS: '/student-assessments',
  REDIRECTURL: '/student-explorer',
  EXPLORE: '/student-explorer',
  PROGRAMBUILDER: '/std-program-builder',
  CVBUILDER: '/cv-builder',
  STUDENTSPROFILE: '/student-profile',
  PROGRAM: '/student-program',
};
