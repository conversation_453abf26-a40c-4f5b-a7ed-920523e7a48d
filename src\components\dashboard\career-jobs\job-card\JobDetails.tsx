'use client';

import type React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Briefcase, CheckIcon } from 'lucide-react';
import type { Job } from '@/types/jobTypes';
import { useRouter } from 'next/navigation';
import useFormatSalary from '@/hooks/useFormatSalary';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { formatPostedDate, normalizeText, sanitizeHtml } from '@/utils';
import FallbackImage from '@/assets/fallback_image.svg';
import { useAppliedJobs } from '@/queries';

interface JobDetailsProps {
  selectedJob: Job | null;
}

const JobDetails: React.FC<JobDetailsProps> = ({ selectedJob }) => {
  const router = useRouter();
  const formatSalary = useFormatSalary();
  const { user } = useAuthStore();
  const { data } = useAppliedJobs({
    userId: user?.id,
  });

  const applicationStatusMap = new Map<string, string>();
  data?.data?.forEach((application) => {
    applicationStatusMap.set(application.jobId, application.status);
  });

  const hasApplied = (jobId: string) => {
    return applicationStatusMap.has(jobId);
  };

  const handleApplyForJob = (jobId: string) => {
    if (!user?.id) return;
    router.push(
      `/dashboard/career-opportunities/jobs/apply-for-a-job/${jobId}`
    );
  };

  if (!selectedJob) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-10">
        <Briefcase className="h-12 w-12 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-neutral-900 mb-1">
          No job selected
        </h3>
        <p className="text-neutral-500 max-w-md">
          Select a job from the list to view its details
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="border-b border-neutral-50 pb-6">
        <div className="items-start gap-4">
          <div className="h-[48px] w-[84px] rounded-md flex items-center justify-center overflow-hidden mb-4">
            {selectedJob?.company?.logo ? (
              <Image
                src={selectedJob?.company?.logo || '/placeholder.svg'}
                alt={`${selectedJob.companyName || 'Company'} logo`}
                width={84}
                height={48}
              />
            ) : (
              <Image
                src={FallbackImage || '/placeholder.svg'}
                alt=""
                width={84}
                height={48}
              />
            )}
          </div>
          <div>
            <h2 className="text-[20px] font-semibold text-neutral-900 capitalize leading-[28px]">
              {selectedJob.title}
            </h2>
            <div className="flex items-center gap-2 text-neutral-500 mt-1">
              <span className="flex items-center">
                {selectedJob.companyName || 'N/A'}
                <div className="mx-1"> | </div>
                {selectedJob.location || 'N/A'}
              </span>
            </div>
            <div className="flex items-center text-neutral-500 text-[16px] mt-1">
              {formatPostedDate(selectedJob?.postedDate)}
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Button
            onClick={() => handleApplyForJob(selectedJob.id)}
            className={`md:w-auto h-[48px] px-[28px] py-[14px]`}
            disabled={hasApplied(selectedJob.id)}
          >
            {hasApplied(selectedJob.id) ? (
              <>
                <CheckIcon className="h-4 w-4 mr-2" />
                Applied
              </>
            ) : (
              'Apply Now'
            )}
          </Button>
        </div>
      </div>

      <div className="py-6 border-b border-neutral-50 ">
        <div className="space-y-4 gap-4">
          <div>
            <p className="text-[16px] font-semibold leading-[24px] text-neutral-900">
              Job Type
            </p>
            <div className="flex gap-2 mt-1">
              <Badge className="bg-neutral-100 rounded-md text-neutral-900 py-1 capitalize text-[14px]">
                {normalizeText(selectedJob.jobType || 'Not Specified')}
              </Badge>
              {selectedJob.jobMode && (
                <Badge className="bg-neutral-100 rounded-md text-neutral-900 py-1 capitalize text-[14px]">
                  {selectedJob.jobMode}
                </Badge>
              )}
            </div>
          </div>
          <div>
            <p className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-1">
              Minimum Salary
            </p>
            <Badge className="bg-neutral-100 rounded-md text-neutral-900 py-1 capitalize text-[14px]">
              {formatSalary(selectedJob.monthlyFrom)}
            </Badge>
          </div>
          <div>
            <p className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-1">
              Starting Date
            </p>
            <p className="text-[16px] font-medium mt-1">
              {selectedJob.postedDate
                ? new Date(selectedJob.postedDate).toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'long',
                    year: 'numeric',
                  })
                : 'N/A'}
            </p>
          </div>

          {selectedJob.jobPosition && (
            <div>
              <p className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-1">
                Job Position (ISCO Code)
              </p>
              <p className="text-[16px] font-medium mt-1 capitalize">
                {normalizeText(selectedJob.jobPosition || 'Not Specified')}
              </p>
            </div>
          )}
        </div>
      </div>

      <div className="py-6">
        <h3 className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-4">
          Details
        </h3>
        <div className="prose prose-sm max-w-none text-neutral-500">
          {selectedJob.description ? (
            <div
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(selectedJob.description),
              }}
            />
          ) : (
            <p className="text-[16px] font-normal leading-[24px] text-neutral-900">
              No details responsibilities provided
            </p>
          )}
        </div>
      </div>
    </>
  );
};

export default JobDetails;
