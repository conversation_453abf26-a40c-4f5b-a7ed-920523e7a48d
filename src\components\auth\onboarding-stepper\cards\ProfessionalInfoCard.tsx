/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { CustomValues } from '@/types/customValues';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const yearsOptions = [
  'Less than 1 year',
  '1 to less than 3 years',
  '3 to less than 5 years',
  '5 to less than 10 years',
  '10+ years',
] as const;

interface ProfessionalInfoCardProps {
  data: {
    skills: any[];
    certifications: any[];
    yearsOfExperience: Record<string, string>;
  };
  updateData: (_data: Partial<ProfessionalInfoCardProps['data']>) => void;
  errors: Record<string, string>;
}

const ProfessionalInfoCard = ({
  data,
  updateData,
  errors,
}: ProfessionalInfoCardProps) => {
  /* ---------------- Admin values (sectors) ------------------- */
  const getSectorClass = useAdminValues({
    category: AdminValuesCategories?.sector?.category,
  });
  const sectorClass: CustomValues[] =
    getSectorClass.data?.data?.data?.customValues || [];

  /* ---------------- Local state ------------------------------ */
  const [selectedSectors, setSelectedSectors] = useState<CustomValues[]>([]);
  const [open, setOpen] = useState(false);

  /* ---------------- Initialize from props -------------------- */
  useEffect(() => {
    if (data.skills && data.skills.length > 0) {
      const sectors = sectorClass.filter((sector) =>
        data.skills.some((skill) => skill.value === sector.value)
      );
      setSelectedSectors(sectors);
    }

    if (typeof data.yearsOfExperience !== 'object') {
      updateData({
        yearsOfExperience: {},
      });
    }
  }, [data.skills, sectorClass, data.yearsOfExperience, updateData]);

  /* ---------------- Handlers --------------------------------- */
  const handleSelect = (value: string) => {
    const selectedItem = sectorClass.find((item) => item.value === value);

    if (selectedItem) {
      let newSectors;
      if (selectedSectors.some((item) => item.value === value)) {
        newSectors = selectedSectors.filter((item) => item.value !== value);

        const newExperiences = { ...data.yearsOfExperience };
        delete newExperiences[value];
        updateData({
          yearsOfExperience: newExperiences,
        });
      } else if (selectedSectors.length < 3) {
        newSectors = [...selectedSectors, selectedItem];
      } else {
        return;
      }

      setSelectedSectors(newSectors);
      updateData({
        skills: newSectors,
      });
    }
  };

  const handleRemove = (value: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    const newSectors = selectedSectors.filter((item) => item.value !== value);
    setSelectedSectors(newSectors);

    const newExperiences = { ...data.yearsOfExperience };
    delete newExperiences[value];

    updateData({
      skills: newSectors,
      yearsOfExperience: newExperiences,
    });
  };

  const handleExperienceChange = (sector: string, val: string) => {
    updateData({
      yearsOfExperience: {
        ...data.yearsOfExperience,
        [sector]: val,
      },
    });
  };

  /* ---------------- Render ----------------------------------- */
  return (
    <div className="bg-white text-left px-7 pb-4 rounded-lg">
      <form className="mt-8 space-y-6">
        <div className="space-y-1">
          <Label className="text-neutral-900 text-[18px] font-semibold leading-[28px]">
            Preferred company sectors (up to 3):
          </Label>

          {errors.skills && (
            <p className="text-destructive-500 text-[16px]">{errors.skills}</p>
          )}

          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <button
                className="flex flex-wrap items-center gap-1 p-2 border rounded-md min-h-10 cursor-pointer w-full"
                onClick={() => setOpen(true)}
              >
                {selectedSectors.length > 0 ? (
                  selectedSectors.map((item) => (
                    <Badge
                      key={item.value}
                      variant="secondary"
                      className="text-[16px] py-1 px-3 bg-[#E2E4E9] text-[#111827] font-normal rounded-md"
                    >
                      {item.label}
                      <button
                        type="button"
                        className="ml-2 text-black hover:bg-transparent"
                        onClick={(e) => handleRemove(item.value, e)}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </Badge>
                  ))
                ) : (
                  <span className="text-muted-foreground">Please Select</span>
                )}
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Command>
                <CommandInput placeholder="Search sectors..." />
                <CommandList>
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup>
                    {sectorClass.map((item) => {
                      const isSelected = selectedSectors.some(
                        (selected) => selected.value === item.value
                      );
                      return (
                        <CommandItem
                          key={item.value}
                          value={item.value}
                          onSelect={handleSelect}
                          disabled={selectedSectors.length >= 3 && !isSelected}
                          className={cn(
                            'flex items-center gap-2',
                            selectedSectors.length >= 3 &&
                              !isSelected &&
                              'opacity-50 cursor-not-allowed'
                          )}
                        >
                          <div
                            className={cn(
                              'flex-shrink-0 mr-2 h-4 w-4 rounded-sm border flex items-center justify-center',
                              isSelected
                                ? 'bg-primary border-primary'
                                : 'border-input'
                            )}
                          >
                            {isSelected && (
                              <Check className="h-3 w-3 text-primary-foreground" />
                            )}
                          </div>
                          {item.label}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        {selectedSectors.map((sector) => (
          <div key={sector.value} className="space-y-1">
            <Label className="text-neutral-900 text-[18px] font-semibold leading-[28px]">
              Years of work experience in {sector.label}:
            </Label>
            <select
              value={data.yearsOfExperience?.[sector.value] || ''}
              onChange={(e) =>
                handleExperienceChange(sector.value, e.target.value)
              }
              className="border rounded p-2 w-full"
            >
              <option value="" disabled>
                Please Select
              </option>
              {yearsOptions.map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
            {errors.yearsOfExperience && (
              <p className="text-destructive-500 text-[16px]">
                {errors.yearsOfExperience}
              </p>
            )}
          </div>
        ))}
      </form>
    </div>
  );
};

export default ProfessionalInfoCard;
