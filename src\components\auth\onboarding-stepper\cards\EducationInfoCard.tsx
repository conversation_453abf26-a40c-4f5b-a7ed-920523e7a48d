/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { AdminValue } from '@/types';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { normalizeText } from '@/utils';

interface EducationInfoCardProps {
  data: {
    highestDegree: string;
    institution: string;
    fieldOfStudy: string;
    subSpecialization: string;
  };
  updateData: (_data: Partial<EducationInfoCardProps['data']>) => void;
  errors: Record<string, string>;
}

const EducationInfoCard: React.FC<EducationInfoCardProps> = ({
  data,
  updateData,
  errors,
}) => {
  const [specialisationPath, setSpecialisationPath] = useState('');

  const { data: eduLevelRes, isLoading: loadingLevels } = useAdminValues({
    category: AdminValuesCategories?.educationLevel?.category,
  });
  const educationLevels = eduLevelRes?.data?.data?.customValues || [];

  const { data: eduSpecRes, isLoading: loadingSpecs } = useAdminValues({
    category: AdminValuesCategories?.educationSpecialisation?.category,
    subcategory:
      AdminValuesCategories?.educationSpecialisation?.subcategories?.ISCED_2,
  });
  const educationSpecialisation = eduSpecRes?.data?.data?.customValues || [];

  const { data: eduSubSpecRes } = useAdminValues({ path: specialisationPath });
  const allEducationSubSpecialisations =
    eduSubSpecRes?.data?.data?.customValues || [];

  const [selectedSubSpecializations, setSelectedSubSpecializations] = useState<
    AdminValue[]
  >([]);
  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    const selectedItem = allEducationSubSpecialisations.find(
      (item: any) => item.value === value
    );
    if (!selectedItem) return;

    const alreadySelected = selectedSubSpecializations.some(
      (item) => item.value === value
    );
    const newSelection = alreadySelected
      ? selectedSubSpecializations.filter((item) => item.value !== value)
      : selectedSubSpecializations.length < 3
        ? [...selectedSubSpecializations, selectedItem]
        : selectedSubSpecializations;

    setSelectedSubSpecializations(newSelection);
    updateData({
      subSpecialization: newSelection.map((item) => item.value).join(', '),
    });
  };

  const handleRemove = (value: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    const newSelection = selectedSubSpecializations.filter(
      (item) => item.value !== value
    );
    setSelectedSubSpecializations(newSelection);
    updateData({
      subSpecialization: newSelection.map((item) => item.value).join(', '),
    });
  };

  return (
    <div className="bg-white text-left px-7 pb-4 rounded-lg">
      <form>
        {/* Education Level */}
        <div className="mb-3 space-y-1 col-span-2 mt-5">
          <Label
            htmlFor="highestDegree"
            className="text-neutral-900 text-[18px] font-semibold leading-[28px]"
          >
            Education Level:
          </Label>
          <Select
            onValueChange={(value) => updateData({ highestDegree: value })}
          >
            <SelectTrigger className="text-[16px] font-normal text-neutral-900">
              <SelectValue
                placeholder={
                  loadingLevels ? 'Loading...' : 'Select education level'
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {educationLevels.map((item: any) => (
                  <SelectItem
                    key={`${item.id}-${item.value}-`}
                    value={item.value}
                  >
                    {item.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {errors.highestDegree && (
            <p className="text-destructive-500 text-[16px]">
              {errors.highestDegree}
            </p>
          )}
        </div>

        {/* Institution */}
        <div className="mb-3 space-y-1 col-span-2 mt-3">
          <Label
            htmlFor="institution"
            className="text-neutral-900 text-[18px] font-semibold leading-[28px]"
          >
            University you attended for your most recent degree:
          </Label>
          <Select
            value={data.institution}
            onValueChange={(value) => updateData({ institution: value })}
          >
            <SelectTrigger className="text-[16px]">
              <SelectValue placeholder="Please Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {[
                  ...new Set([
                    'Al Ain University of Science and Technology',
                    'Abu Dhabi University',
                    'Al Falah University',
                    'Emirates College for Advanced Education',
                    'Emirates College of Technology',
                    'European International College',
                    'INSEAD – Abu Dhabi',
                    'Masdar Institute of Science and Technology',
                    'New York Institute of Technology',
                    'New York University Abu Dhabi',
                  ]),
                ].map((name) => (
                  <SelectItem key={name} value={name}>
                    {name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {errors.institution && (
            <p className="text-destructive-500 text-[16px]">
              {errors.institution}
            </p>
          )}
        </div>

        {/* Specialisation */}
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="fieldOfStudy"
            className="text-neutral-900 text-[18px] font-semibold leading-[28px]"
          >
            Specialisation:
          </Label>
          <Select
            onValueChange={(value) => {
              const selected = educationSpecialisation.find(
                (item: AdminValue) => item.value === value
              );
              if (selected) {
                setSpecialisationPath(selected.path ?? '');
              }
              updateData({ fieldOfStudy: value });
            }}
          >
            <SelectTrigger className="text-[16px] font-normal">
              <SelectValue
                placeholder={loadingSpecs ? 'Loading...' : 'Please Select'}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {educationSpecialisation.map((item: any, i: number) => (
                  <SelectItem
                    // eslint-disable-next-line react/no-array-index-key
                    key={`${item.label}-${item.value}-${i}`}
                    value={item.value}
                  >
                    {item.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>

          {errors.fieldOfStudy && (
            <p className="text-destructive-500 text-[16px]">
              {errors.fieldOfStudy}
            </p>
          )}
        </div>

        {specialisationPath && (
          <>
            {/* Sub-specialisation */}
            <div className="mb-3 space-y-1 col-span-2">
              <Label
                htmlFor="sub-specialisation"
                className="text-neutral-900 text-[18px] font-semibold leading-[28px]"
              >
                Sub-specialisation(s) (up to 3):
              </Label>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <button
                    className="flex flex-wrap items-center gap-1 p-2 border rounded-md min-h-10 cursor-pointer w-full"
                    onClick={() => setOpen(true)}
                  >
                    {selectedSubSpecializations.length > 0 ? (
                      selectedSubSpecializations.map((item) => (
                        <Badge
                          key={`${item.id}-${item.label}-`}
                          variant="secondary"
                          className="text-[16px] py-1 px-3 bg-neutral-200 text-neutral-900 font-normal rounded-md capitalize"
                        >
                          {normalizeText(item.label || '')}
                          <button
                            type="button"
                            className="ml-2 text-black hover:bg-transparent"
                            onClick={(e) => handleRemove(item.value, e)}
                          >
                            <X className="h-3 w-3" />
                            <span className="sr-only">Remove</span>
                          </button>
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground">
                        Please Select
                      </span>
                    )}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search sub-specialisations..." />
                    <CommandList>
                      <CommandEmpty>No results found.</CommandEmpty>
                      <CommandGroup>
                        {allEducationSubSpecialisations.map((item: any) => {
                          const isSelected = selectedSubSpecializations.some(
                            (selected) => selected.value === item.value
                          );
                          return (
                            <CommandItem
                              key={`${item.value}-${item.value}-`}
                              value={item.value}
                              onSelect={handleSelect}
                              disabled={
                                selectedSubSpecializations.length >= 3 &&
                                !isSelected
                              }
                              className={cn(
                                'flex items-center gap-2',
                                selectedSubSpecializations.length >= 3 &&
                                  !isSelected &&
                                  'opacity-50 cursor-not-allowed'
                              )}
                            >
                              <div
                                className={cn(
                                  'flex-shrink-0 mr-2 h-4 w-4 rounded-sm border flex items-center justify-center',
                                  isSelected
                                    ? 'bg-accent text-accent-foreground'
                                    : 'bg-background'
                                )}
                              >
                                {isSelected && <Check className="h-2 w-2" />}
                              </div>
                              {item.label}
                            </CommandItem>
                          );
                        })}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              {errors.subSpecialization && (
                <p className="text-destructive-500 text-[16px]">
                  {errors.subSpecialization}
                </p>
              )}
            </div>
          </>
        )}
      </form>
    </div>
  );
};

export default EducationInfoCard;
