'use client';

import { useAuthStore } from '@/zustand/store/useAuthStore';
import React from 'react';

interface UserInfo {
  label: string
  value: string
}

function InfoSection({
  title,
  data,
  isLast = false,
}: {
  title: string
  data: UserInfo[]
  isLast?: boolean
}) {
  return (
    <div
      className={`my-4 pb-4 ${!isLast ? 'border-b border-neutral-200' : ''}`}
    >
      <h3 className="text-neutral-900 text-[18px] font-semibold leading-[28px] mb-2">
        {title}
      </h3>
      {data.map((item, index) => (
        <div key={index} className="flex justify-between mb-2">
          <p className="text-[16px] font-semibold leading-[24px] text-neutral-500">
            {item.label}
          </p>
          <p className="text-[16px] font-normal leading-[24px] text-neutral-500">
            {item.value || 'Not provided'}
          </p>
        </div>
      ))}
    </div>
  );
}

function PersonalInformation() {
  const user = useAuthStore((state) => state.user);

  if (!user) {
    return (
      <div className="py-4 px-4 mt-4 border border-neutral-200 rounded-md">
        <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-neutral-900">
          Personal Information
        </h4>
        <p className="text-neutral-500 mt-2">
          Please log in to view your personal information
        </p>
      </div>
    );
  }

  // Split fullname into first and last names
  const [firstName, ...lastNameParts] = user.fullName?.split(' ') || [];
  const lastName = lastNameParts.join(' ') || 'Not provided';

  // Extract user data with fallbacks for missing fields
  const userData = {
    firstName: firstName || 'Not provided',
    lastName,
    nationalId: user.nationalId || 'Not provided',
    nationality: user.nationality || 'Not provided',
    phoneNumber: user.phoneNumber || 'Not provided',
    email: user.email || 'Not provided',
  };

  return (
    <div className="py-4 px-4 mt-4 border border-neutral-200 rounded-md">
      <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-neutral-900">
        Personal Information
      </h4>

      <InfoSection
        title="Name"
        data={[
          { label: 'First Name', value: userData.firstName },
          { label: 'Last Name', value: userData.lastName },
        ]}
      />
      <InfoSection
        title="Nationality"
        data={[
          { label: 'National ID', value: userData.nationalId },
          { label: 'Nationality', value: userData.nationality },
        ]}
      />
      <InfoSection
        title="Contact"
        data={[
          { label: 'Mobile', value: userData.phoneNumber },
          { label: 'Email', value: userData.email },
        ]}
        isLast={true}
      />
    </div>
  );
}

export default PersonalInformation;
