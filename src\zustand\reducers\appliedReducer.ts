import type { AppliedJob, AppliedJobState } from '@/types/jobTypes';

type AppliedJobAction =
  | {
      type: 'SET_JOBS';
      payload: {
        jobs: AppliedJob[];
        page: number;
        size: number;
        total: number;
      };
    }
  | { type: 'SET_USER_JOBS'; payload: AppliedJob[] }
  | { type: 'SET_CURRENT_JOB'; payload: AppliedJob | null }
  | { type: 'ADD_JOB'; payload: AppliedJob }
  | { type: 'UPDATE_JOB'; payload: AppliedJob }
  | { type: 'DELETE_JOB'; payload: string }
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' };

export const appliedjobReducer = (
  state: AppliedJobState,
  action: AppliedJobAction
): AppliedJobState => {
  switch (action.type) {
    case 'SET_JOBS':
      return {
        ...state,
        jobs: action.payload.jobs,
        page: action.payload.page,
        size: action.payload.size,
        total: action.payload.total,
        isLoading: false,
      };

    case 'SET_USER_JOBS':
      return {
        ...state,
        jobs: action.payload,
        isLoading: false,
      };

    case 'SET_CURRENT_JOB':
      return {
        ...state,
        currentJob: action.payload,
        isLoading: false,
      };

    case 'ADD_JOB':
      return {
        ...state,
        jobs: [action.payload, ...state.jobs],
      };

    case 'UPDATE_JOB':
      return {
        ...state,
        jobs: state.jobs.map((job) =>
          job.id === action.payload.id ? action.payload : job
        ),
        currentJob:
          state.currentJob?.id === action.payload.id
            ? action.payload
            : state.currentJob,
      };

    case 'DELETE_JOB':
      return {
        ...state,
        jobs: state.jobs.filter((job) => job.id !== action.payload),
        currentJob:
          state.currentJob?.id === action.payload ? null : state.currentJob,
      };

    case 'START_LOADING':
      return { ...state, isLoading: true };

    case 'STOP_LOADING':
      return { ...state, isLoading: false };

    default:
      return state;
  }
};
