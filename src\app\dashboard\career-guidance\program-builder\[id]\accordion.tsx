/* eslint-disable react-hooks/exhaustive-deps */
'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import type { Course, ProgramDetailsResponse } from '@/types';
import { ArrowDown, ArrowUp, ChevronDown, ChevronRight } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import CourseDetailsDialog from './dialog';

interface ProgramData {
  id: string;
  code: string | null;
  name: string;
  department: string;
  courses: Course[];
  // other properties...
}

interface ApiResponse {
  data: ProgramData;
}

interface AccordionProps {
  data: ProgramDetailsResponse | ApiResponse;
  label: string;
  type: string;
}

type Order = 'asc' | 'desc';

function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator<Key extends keyof any>(
  order: Order,
  orderBy: Key
): (
  // eslint-disable-next-line no-unused-vars
  a: { [key in Key]: number | string },
  // eslint-disable-next-line no-unused-vars
  b: { [key in Key]: number | string }
) => number {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function stableSort<T>(
  array: readonly T[],
  comparator: (_a: T, _b: T) => number
) {
  const stabilizedThis = (array || []).map(
    (el, index) => [el, index] as [T, number]
  );
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

const ProgramAccordion: React.FC<AccordionProps> = ({ data, label, type }) => {
  const [order, setOrder] = React.useState<Order>('asc');
  const [orderBy, setOrderBy] = React.useState<keyof { code: string }>('code');
  const [selected, setSelected] = React.useState<Course | null>(null);
  const [expanded, setExpanded] = React.useState(false);

  // Handle the new data structure
  const programData = 'data' in data ? data.data : data;

  const courses = Array.isArray(programData?.courses)
    ? programData.courses
    : [];

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: keyof { code: string }
  ) => {
    event.preventDefault();
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const createSortHandler =
    (property: keyof { code: string }) =>
      (event: React.MouseEvent<unknown>) => {
        handleRequestSort(event, property);
      };

  const visibleRows = React.useMemo(
    () =>
      stableSort<Course>(
        courses,
        getComparator<keyof { code: string }>(order, orderBy)
      ),
    [order, orderBy, courses]
  );

  const handleRowClick = (event: React.MouseEvent<unknown>, id: string) => {
    event.preventDefault();
    event.stopPropagation();

    const item = courses.find((course) => course.id === id);

    if (item) {
      setSelected(item);
    }
  };

  const filteredCourses = visibleRows.filter((item: Course) => {
    if (!item.type && type === 'Mandatory') return true;
    // Otherwise do a case-insensitive comparison
    return item.type?.toLowerCase() === type.toLowerCase();
  });

  return (
    <>
      <div className="mt-4 md:my-6 border-b border-neutral-300">
        <button
          className="flex items-center justify-between w-full py-2 text-left"
          onClick={() => setExpanded(!expanded)}
          aria-expanded={expanded}
          aria-controls={`${label}-content`}
        >
          <h3 className="text-lg font-medium text-neutral-700">{label}</h3>
          <ChevronDown
            className={`w-5 h-5 text-neutral-700 transition-transform duration-200 ${expanded ? 'rotate-180' : ''}`}
          />
        </button>

        {expanded && (
          <div id={`${label}-content`} className="pt-4 pb-4">
            {filteredCourses.length === 0 ? (
              <div className="py-4 text-center text-neutral-500">
                No {type.toLowerCase()} courses found.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="bg-neutral-300 rounded-lg">
                    <TableRow>
                      <TableHead className="w-[120px]">
                        <button
                          className="flex items-center gap-1"
                          onClick={createSortHandler('code')}
                        >
                          Code
                          {orderBy === 'code' ? (
                            order === 'asc' ? (
                              <ArrowUp className="w-4 h-4" />
                            ) : (
                              <ArrowDown className="w-4 h-4" />
                            )
                          ) : null}
                        </button>
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead className="hidden md:table-cell w-[200px]">
                        Credits
                      </TableHead>
                      <TableHead className="hidden md:table-cell w-[200px]">
                        Skills
                      </TableHead>
                      <TableHead className="w-[40px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCourses.map((row: Course, index: number) => (
                      <TableRow
                        key={row.id}
                        className={`cursor-pointer hover:bg-neutral-100 ${index % 2 === 0 ? '' : 'bg-neutral-50'}`}
                        onClick={(
                          event: React.MouseEvent<HTMLTableRowElement>
                        ) => handleRowClick(event, row.id)}
                      >
                        <TableCell className="whitespace-nowrap">
                          {row.code}
                        </TableCell>
                        <TableCell>{row.name}</TableCell>
                        <TableCell className="hidden md:table-cell">
                          {row.credits}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {
                            (row.escoSkills || '')
                              .split(';')
                              .filter((skill: string) => skill !== '').length
                          }
                        </TableCell>
                        <TableCell className="text-right">
                          <ChevronRight className="w-5 h-5 text-neutral-500 ml-auto" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        )}
      </div>
      <CourseDetailsDialog selected={selected} setSelected={setSelected} />
    </>
  );
};

export default ProgramAccordion;
