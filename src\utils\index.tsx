import DOMPurify from 'dompurify';

export function formatUSD(amount = 0) {
  const withDecimals = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
  return withDecimals.replace(/\.00$/, '');
}

export function normalizeText(input: string, separator = ' ') {
  return input.replace(/[-_]+/g, separator).replace(/\s+/g, ' ').trim();
}

export const formatDateDDMonthYYYY = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const day = date.getUTCDate();
  const month = date.toLocaleString('en-US', {
    month: 'short',
    timeZone: 'UTC',
  });
  const year = date.getUTCFullYear();
  return `${day} ${month} ${year}`;
};

export function sanitizeHtml(html: string) {
  return DOMPurify.sanitize(html, {
    USE_PROFILES: { html: true },
  });
}

export const formatDateAppliedJobs = (dateString?: string): string => {
  if (!dateString) return 'Posted recently';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Posted recently';

  const now = new Date();
  const diffDays = Math.ceil(
    Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffDays === 0) return 'Posted today';
  if (diffDays === 1) return 'Posted yesterday';
  if (diffDays < 7) return `Posted ${diffDays} days ago`;
  if (diffDays < 30) return `Posted ${Math.floor(diffDays / 7)} weeks ago`;
  return `Posted on ${date.toLocaleDateString()}`;
};

export const formatSalary = (
  from?: number,
  to?: number,
  currency = '$'
): string => {
  const formatNumber = (num: number) => num.toLocaleString();

  if (!from && !to) return 'Salary not specified';
  if (from && to)
    return `${currency} ${formatNumber(from)} - ${formatNumber(to)} per month`;
  return from
    ? `${currency}${formatNumber(from)} per month`
    : `Up to ${currency} ${formatNumber(to || 0)} per month`;
};

export const transformToCamelCase = (text: string) => {
  const textSplitted: string[] = text.split(' ');
  const camelCasedTextArr = textSplitted.map((word: string) => {
    if (word.length > 1) {
      return word[0].toUpperCase() + word.substring(1, word.length);
    } else {
      return word.toUpperCase();
    }
  });
  return camelCasedTextArr.join(' ');
};

export const DROPDOWN_NOT_APPLICABLE = {
  id: 'na',
  title: 'Not Applicable',
};

export const DROPDOWN_OTHER = {
  id: 'other',
  title: 'Other',
};

export const formatDate = (dateString?: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

export const formatPostedDate = (postedDate?: string) => {
  if (!postedDate) return 'Posted date not available';

  const postedTime = new Date(postedDate).getTime();
  const now = Date.now();

  const diffInMs = now - postedTime;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays >= 1) {
    return `Posted ${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(
    (diffInMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );

  if (diffInHours >= 1) {
    return `Posted ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  return 'Posted today';
};
