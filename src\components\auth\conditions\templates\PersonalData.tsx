import { BulletPoints, PersonalData } from '@/constants/PRIVACY_DEFINITION';
import React from 'react';

function UsePersonalData() {
  return (
    <div>
      <h5 className="text-blue-300 text-[16px] font-medium mt-4">
        Use of Your Personal Data
      </h5>
      <p> The Company may use Personal Data for the following purposes: </p>

      <ul className="ml-8 flex flex-col gap-3 mt-4">
        {PersonalData.map((item) => (
          <li
            key={item.definition}
            className="text-[16px] font-normal leading-6 text-neutral-900"
          >
            <span className="font-semibold">{item.term}:</span>{' '}
            {item.definition}
          </li>
        ))}
      </ul>

      <p className="text-neutral-900 text-[16px] font-normal mt-4">
        We may share Your personal information in the following situations:
      </p>

      <ul className="mt-2 list-disc list-inside text-[16px] flex flex-col gap-3">
        {BulletPoints.map((point) => (
          <li key={point.title} className="text-neutral-800">
            <strong>{point.title}: </strong> {point.def}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default UsePersonalData;
