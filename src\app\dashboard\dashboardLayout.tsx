/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { ActionsAlert } from '@/components/dashboard/components/ActionsAlert';
import DashboardFooter from '@/components/dashboard/components/DashboardFooter';
import TopBar from '@/components/dashboard/components/TopBar';
import TopNavigation from '@/components/dashboard/components/TopNavigation';
import useSettingsStore from '@/zustand/store/settingsStore';
import { CareerAssistant } from '@/components/ai/AssistantDrawer';
import { LoadingScreen } from '@/components/common/LoadingVersionScreen';

const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const [showActionsAlert, setShowActionsAlert] = useState(false);
  const [isVersionFlag, setIsVersionFlag] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const { appearanceSettings, getSettings } = useSettingsStore();
  const mainRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  useEffect(() => {
    const isV3Path = pathname.startsWith('/V03');
    setIsVersionFlag(isV3Path);
  }, [pathname]);

  const toggleActionsAlert = () => {
    setShowActionsAlert((prev) => !prev);
  };
  const toggleVersionFlag = (checked: boolean) => {
    setIsVersionFlag(checked);
    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
    }, 1200);
  };

  const {
    isBgStyleColor,
    bgStyleColor,
    bgStyleImg,
    overlayColor,
    overlayPercentage,
  } = appearanceSettings;

  const bgStyle = isBgStyleColor
    ? {
        backgroundColor: bgStyleColor,
      }
    : {
        backgroundImage: `url(${bgStyleImg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      };

  useEffect(() => {
    if (mainRef.current) {
      mainRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [pathname]);

  return (
    <>
      <LoadingScreen
        isVisible={isLoading}
        message="Switching to Version0.3..."
      />
      <div className="overflow-y-scroll">
        <div className="flex flex-col h-screen overflow-x-hidden">
          <TopBar
            isVersionFlag={isVersionFlag}
            onToggleVersionFlag={toggleVersionFlag}
            toggleActionsAlert={toggleActionsAlert}
          />
          <div>
            <TopNavigation toggleActionsAlert={toggleActionsAlert} />
          </div>

          <div className="flex flex-1 overflow-hidden relative" style={bgStyle}>
            {!isBgStyleColor && (
              <div
                className="fixed top-0 left-0 w-full h-full z-0"
                style={{
                  backgroundColor: overlayColor,
                  opacity: Number(overlayPercentage) / 100,
                }}
              />
            )}

            <div className="flex flex-col flex-1 relative z-10">
              <main
                ref={mainRef}
                className="flex-1 relative overflow-y-auto scrollbar-hide"
              >
                {showActionsAlert && <ActionsAlert />}
                <section className="p-8 min-h-full z-1 relative max-w-[1200px] mx-auto">
                  {children}
                </section>
              </main>
            </div>

            <div className="bg-transparent overflow-hidden relative z-10 ">
              <CareerAssistant />
            </div>
          </div>

          <DashboardFooter />
        </div>
      </div>
    </>
  );
};

export default DashboardLayout;
