import Image from 'next/image';
import PaperPlan from '@/assets/images/dashboard/paperplan.svg';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';

const JobApplied = ({
  handleNextJob,
  appliedJobs,
}: {
  handleNextJob: () => void;
  appliedJobs: number;
}) => {
  return (
    <div className="flex flex-col gap-8 justify-center items-center h-full p-8 bg-white max-w-[544px] rounded-[20px] border-[1px] border-neutral-200">
      <Image src={PaperPlan} alt="checklist" height={120} width={120} />
      <div className="flex flex-col gap-2 items-center">
        <h3 className="font-[600] text-[28px] tracking-[-0.56px]">Success!</h3>
        <p className="font-medium text-[18px] !text-neutral-500">
          Your application has been submitted!
        </p>
        <p className="font-medium text-[18px] !text-neutral-500 text-center">{`You have ${3 - appliedJobs} more application available today. Choose the role that fit you best.`}</p>
      </div>

      <div className="self-stretch inline-flex justify-center items-center">
        {[1, 2, 3]?.map((item, index) => {
          if (index + 1 <= appliedJobs) {
            return (
              <div
                key={`appliedJob_${item}`}
                data-1st-progress-line="false"
                data-2nd-progress-line="true"
                data-direction="Horizontal"
                data-show-icon="true"
                data-show-nodes="true"
                data-state="Completed"
                data-style="Lined"
                data-type="Dot"
                className="flex-1 py-4 bg-Generic-White inline-flex flex-col justify-center items-center gap-3"
              >
                <div className="self-stretch inline-flex justify-start items-center">
                  {index < 2 && index !== 1 && (
                    <div className="flex-1 flex justify-center items-center" />
                  )}
                  {(index === 2 || index === 1) && (
                    <div className="flex-1 h-0.5 flex justify-start items-start">
                      <div className="flex-1 h-0.5 bg-primary-500" />
                    </div>
                  )}
                  <div
                    data-indicator="Dot"
                    data-size="Medium (32px)"
                    data-state="Completed"
                    className="inline-flex flex-col justify-center items-end"
                  >
                    <div className="w-8 h-8 relative bg-primary-500 rounded-3xl">
                      <div className="w-6 h-6 left-[4px] top-[4px] absolute overflow-hidden">
                        <Check className="text-white" />
                      </div>
                    </div>
                  </div>
                  {index === 2 && (
                    <div className="flex-1 flex justify-center items-center" />
                  )}
                  {index < 2 && (
                    <div className="flex-1 h-0.5 flex justify-start items-start">
                      <div className="flex-1 h-0.5 bg-primary-500" />
                    </div>
                  )}
                </div>
              </div>
            );
          } else {
            return (
              <div
                key={`appliedJob_${item}`}
                data-1st-progress-line="true"
                data-2nd-progress-line="false"
                data-direction="Horizontal"
                data-show-icon="true"
                data-show-nodes="true"
                data-state="Active"
                data-style="Lined"
                data-type="Dot"
                className="flex-1 py-4 bg-Generic-White inline-flex flex-col justify-start items-start gap-3"
              >
                <div className="self-stretch inline-flex justify-start items-center">
                  {index < 2 && index !== 1 && (
                    <div className="flex-1 flex justify-center items-center" />
                  )}
                  {(index === 2 || index === 1) && (
                    <div className="flex-1 h-0.5 flex justify-start items-start">
                      <div className="flex-1 h-0.5 bg-primary-500" />
                    </div>
                  )}
                  <div
                    data-indicator="Dot"
                    data-size="Medium (32px)"
                    data-state="Active"
                    className="inline-flex flex-col justify-center items-end"
                  >
                    <div className="w-8 h-8 relative bg-Generic-White rounded-3xl shadow-[0px_0px_0px_4px_rgba(200,208,217,0.70)] outline outline-2 outline-offset-[-2px] outline-primary-500 overflow-hidden">
                      <div className="w-3 h-3 left-[10px] top-[10px] absolute bg-primary-500 rounded-full" />
                    </div>
                  </div>
                  {index === 2 && (
                    <div className="flex-1 flex justify-center items-center" />
                  )}
                  {index < 2 && (
                    <div className="flex-1 h-0.5 flex justify-start items-start">
                      <div className="flex-1 h-0.5 bg-primary-500" />
                    </div>
                  )}
                </div>
              </div>
            );
          }
        })}
      </div>

      <Button className="w-full" onClick={handleNextJob}>
        Next job match
      </Button>
    </div>
  );
};

export default JobApplied;
