'use client';
import { TrashIcon } from '@/assets/images/dashboard';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useDeleteCvCustomMutation } from '@/mutations';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';
import React, { useState } from 'react';

const DeleteCvModal = ({ id }: { id: string }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const userId = useAuthStore((state) => state.user?.id);
  const queries = useQueryClient();

  const { deleteCv, isLoading } = useDeleteCvCustomMutation(() => {
    queries.invalidateQueries({ queryKey: ['usercvs'] });
  });
  const handleDelete = (id: string) => {
    deleteCv({ id, userId: userId as string });
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="text-destructive-500 bg-transparent hover:text-destructive-700 rounded  px-0 w-[40px] h-[40px] hover:bg-inherit">
            {isLoading ? (
              <Loader2 className=" animate-spin" />
            ) : (
              <Image
                src={TrashIcon}
                width={10}
                height={10}
                alt="trashIcon"
                className="w-7 h-7"
              />
            )}
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this CV? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="bg-destructive-500"
              onClick={() => {
                handleDelete(id);
                setIsDialogOpen(false);
              }}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteCvModal;
