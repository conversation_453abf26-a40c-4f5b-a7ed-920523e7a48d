import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import axiosClient from '@/utils/axiosClient';
import { useNotificationStore } from '@/zustand/store/notificationStore';
import { useAuthStore } from '@/zustand/store/useAuthStore';

interface ApplicationState {
  selectedDocumentId: string | null;
  contactInfo: {
    email: string;
    mobile: string;
    doNotShare: boolean;
  };
  isLoading: boolean;
  showLoader: boolean;

  setSelectedDocumentId: (_id: string) => void;
  setContactInfo: (_info: {
    email: string;
    mobile: string;
    doNotShare: boolean;
  }) => void;
  applyForJob: (_jobId: string) => Promise<void>;
  checkApplicationStatus: (_jobId: string) => Promise<string | null>;
}

const initialState = {
  selectedDocumentId: null,
  contactInfo: {
    email: '',
    mobile: '',
    doNotShare: false,
  },
  isLoading: false,
  showLoader: false,
};

export const useApplicationStore = create<ApplicationState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setSelectedDocumentId: (id) => set({ selectedDocumentId: id }),

      setContactInfo: (info) => set({ contactInfo: info }),

      applyForJob: async (jobId) => {
        const { selectedDocumentId, contactInfo } = get();
        const { user } = useAuthStore.getState(); // Get user from auth store
        const { showNotification } = useNotificationStore.getState();

        if (!user || !user.id) {
          showNotification(
            'User not authenticated. Please log in again.',
            'error'
          );
          throw new Error('User not authenticated');
        }

        if (!selectedDocumentId) {
          showNotification(
            'Please select a document before applying.',
            'error'
          );
          throw new Error('No document selected');
        }

        try {
          set({ isLoading: true, showLoader: true });

          const response = await axiosClient.post(`/Jobs/${jobId}/apply`, {
            userId: user.id,
            documentId: selectedDocumentId,
            ...contactInfo,
          });

          if (!response.status === true) {
            throw new Error('Failed to apply for job');
          }

          showNotification('Application submitted successfully! 🎉', 'success');

          setTimeout(() => {
            set({
              selectedDocumentId: null,
              contactInfo: { email: '', mobile: '', doNotShare: false },
            });
          }, 2000);
        } catch (error) {
          console.error('Error applying for job:', error);
          showNotification('Failed to apply. Please try again.', 'error');
          throw error;
        } finally {
          set({ isLoading: false, showLoader: false });
        }
      },

      checkApplicationStatus: async (jobId) => {
        try {
          const response = await axiosClient.get(`/Jobs/${jobId}/status`);

          if (response.status === 200 && response.data) {
            return response.data.status;
          } else {
            throw new Error('Invalid response format');
          }
        } catch (error) {
          console.error('Error checking application status:', error);
          return null;
        }
      },
    }),
    {
      name: 'application-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
