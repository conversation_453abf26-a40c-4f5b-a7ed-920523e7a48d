import React from 'react';

function CustomTabPanelSvg5() {
  return (
    <div>
      <svg
        width="323"
        height="240"
        viewBox="0 0 323 240"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1500_5904)">
          <path
            d="M175.268 24.4773C179.469 24.4773 182.874 21.0721 182.874 16.8715C182.874 12.6709 179.469 9.26562 175.268 9.26562C171.068 9.26562 167.663 12.6709 167.663 16.8715C167.663 21.0721 171.068 24.4773 175.268 24.4773Z"
            fill="#1D506B"
          />
          <path
            d="M174.515 19.773C174.344 19.7732 174.177 19.7179 174.04 19.6152L174.032 19.6089L172.244 18.2415C171.896 17.9746 171.83 17.476 172.097 17.1278C172.364 16.7796 172.863 16.7138 173.211 16.9807L174.369 17.8685L177.104 14.2992C177.371 13.9512 177.87 13.8854 178.218 14.1522L178.201 14.1759L178.218 14.1524C178.566 14.4195 178.632 14.9177 178.365 15.2658L175.147 19.4622C174.996 19.6582 174.763 19.7727 174.515 19.772L174.515 19.773Z"
            fill="white"
          />
          <path
            d="M175.268 75.8953C179.469 75.8953 182.874 72.49 182.874 68.2894C182.874 64.0888 179.469 60.6836 175.268 60.6836C171.068 60.6836 167.663 64.0888 167.663 68.2894C167.663 72.49 171.068 75.8953 175.268 75.8953Z"
            fill="#1D506B"
          />
          <path
            d="M174.515 71.1909C174.344 71.1912 174.177 71.1358 174.04 71.0332L174.032 71.0268L172.244 69.6595C171.896 69.3925 171.83 68.8939 172.097 68.5457C172.364 68.1976 172.863 68.1317 173.211 68.3986L174.369 69.2865L177.104 65.7172C177.371 65.3692 177.87 65.3034 178.218 65.5702L178.201 65.5939L178.218 65.5703C178.566 65.8375 178.632 66.3357 178.365 66.6838L175.147 70.8802C174.996 71.0762 174.763 71.1907 174.515 71.19L174.515 71.1909Z"
            fill="white"
          />
          <path
            d="M194.37 148.212C194.276 145.83 196.132 143.823 198.514 143.73C198.768 143.72 199.022 143.732 199.274 143.767L206.24 130.1L210.176 137.032L202.958 148.947C202.473 151.295 200.177 152.805 197.83 152.32C195.866 151.915 194.435 150.216 194.37 148.212Z"
            fill="#FFB7B7"
          />
          <path
            d="M226.845 234.27L221.378 234.27L218.778 213.184L226.846 213.184L226.845 234.27Z"
            fill="#FFB7B7"
          />
          <path
            d="M228.239 239.569L210.612 239.569V239.346C210.613 235.557 213.684 232.485 217.473 232.485H217.474L228.24 232.485L228.239 239.569Z"
            fill="#2F2E41"
          />
          <path
            d="M256.48 234.27L251.013 234.27L248.413 213.184L256.481 213.184L256.48 234.27Z"
            fill="#FFB7B7"
          />
          <path
            d="M257.874 239.569L240.247 239.569V239.346C240.247 235.557 243.319 232.485 247.108 232.485H247.108L257.874 232.485L257.874 239.569Z"
            fill="#2F2E41"
          />
          <path
            d="M202.766 136.317L200.379 140.295C200.379 140.295 204.357 140.295 205.152 146.261L208.334 142.284L202.766 136.317Z"
            fill="#E5E5E5"
          />
          <path
            d="M244.529 127.964C244.529 127.964 250.893 144.67 252.086 155.011C253.279 165.352 255.268 192.797 255.268 192.797L259.246 228.196L248.109 229.787L243.336 193.99L232.597 158.591L230.21 193.99L228.222 226.207L217.88 226.605L217.085 193.195L212.709 150.636L215.096 130.351L244.529 127.964Z"
            fill="#2F2E41"
          />
          <path
            d="M236.773 77.252L240.751 80.0358L245.921 132.141L213.704 132.936L218.079 96.7413L222.057 86.3999L224.928 82.6042L236.773 77.252Z"
            fill="#E5E5E5"
          />
          <path
            d="M237.569 114.64L238.786 78.6606C238.786 78.6606 241.149 78.8428 241.944 80.0361C242.74 81.2293 255.865 83.6158 255.865 83.6158L253.479 120.606C253.479 120.606 263.422 150.039 259.843 151.63C256.263 153.221 249.899 152.823 249.899 152.823L237.569 114.64Z"
            fill="#2F2E41"
          />
          <path
            d="M218.278 110.066L222.498 85.8174L213.108 92.1674L215.494 109.668L213.505 121.601C213.505 121.601 205.948 154.613 208.335 155.807C210.721 157 213.505 155.807 213.505 155.807L218.278 122.396V110.066Z"
            fill="#2F2E41"
          />
          <path
            d="M215.891 91.3721L213.107 92.1676L211.914 108.077L209.925 121.998L200.379 139.101C200.379 139.101 205.152 135.92 207.141 145.068L217.654 126.765L215.891 91.3721Z"
            fill="#2F2E41"
          />
          <path
            d="M237.526 144.95C237.897 142.595 240.107 140.986 242.462 141.357C242.713 141.397 242.96 141.458 243.2 141.541L252.687 129.487L255.202 137.051L245.808 147.338C244.877 149.547 242.332 150.583 240.123 149.652C238.275 148.873 237.202 146.929 237.526 144.95Z"
            fill="#FFB7B7"
          />
          <path
            d="M253.677 140.692L250.098 145.067L245.325 136.715L247.313 133.135L253.677 140.692Z"
            fill="#E5E5E5"
          />
          <path
            d="M252.882 85.008L256.064 83.417C256.064 83.417 258.45 86.599 258.848 89.3832C259.246 92.1674 261.235 95.7471 261.235 97.7358C261.235 99.7245 266.803 118.419 266.803 118.419L254.075 144.272C254.075 144.272 251.291 134.726 245.325 133.931L255.268 120.01L249.7 110.066L252.882 85.008Z"
            fill="#2F2E41"
          />
          <path
            d="M321.732 239.815H168.683C168.462 239.815 168.282 239.635 168.282 239.413C168.282 239.191 168.462 239.011 168.683 239.011H321.732C321.954 239.011 322.134 239.191 322.134 239.413C322.134 239.635 321.954 239.815 321.732 239.815Z"
            fill="#CBCBCB"
          />
          <path d="M146.621 85.161H0V51.418H146.621V85.161Z" fill="white" />
          <path
            d="M21.161 62.5747C20.372 62.5747 19.7324 63.2143 19.7324 64.0033C19.7324 64.7923 20.372 65.4319 21.161 65.4319H125.467C126.256 65.4319 126.895 64.7923 126.895 64.0033C126.895 63.2143 126.256 62.5747 125.467 62.5747H21.161Z"
            fill="#1D506B"
          />
          <path
            d="M21.1606 71.1465C20.3716 71.1448 19.7307 71.7831 19.729 72.5721C19.7273 73.3611 20.3656 74.0021 21.1546 74.0037H87.0978C87.8868 74.0037 88.5264 73.3641 88.5264 72.5751C88.5264 71.7861 87.8868 71.1465 87.0978 71.1465L21.1606 71.1465Z"
            fill="#1D506B"
          />
          <path
            d="M146.621 85.161H0V51.418H146.621V85.161ZM2.41021 82.7507H144.211V53.8282H2.41021V82.7507Z"
            fill="#E5E5E5"
          />
          <path d="M146.621 136.579H0V102.836H146.621V136.579Z" fill="white" />
          <path
            d="M21.161 113.993C20.372 113.993 19.7324 114.632 19.7324 115.421C19.7324 116.21 20.372 116.85 21.161 116.85H125.467C126.256 116.85 126.895 116.21 126.895 115.421C126.895 114.632 126.256 113.993 125.467 113.993H21.161Z"
            fill="#E6E6E6"
          />
          <path
            d="M21.1606 122.564C20.3716 122.563 19.7307 123.201 19.729 123.99C19.7273 124.779 20.3656 125.42 21.1546 125.422H87.0978C87.8868 125.422 88.5264 124.782 88.5264 123.993C88.5264 123.204 87.8868 122.564 87.0978 122.564H21.1606Z"
            fill="#E6E6E6"
          />
          <path
            d="M146.621 136.579H0V102.836H146.621V136.579ZM2.41021 134.169H144.211V105.246H2.41021V134.169Z"
            fill="#E5E5E5"
          />
          <path d="M147.826 33.743H1.20508V0H147.826V33.743Z" fill="white" />
          <path
            d="M22.366 11.1572C21.577 11.1587 20.9385 11.7994 20.9399 12.5884C20.9414 13.3754 21.579 14.013 22.366 14.0145H126.672C127.461 14.013 128.099 13.3722 128.098 12.5832C128.096 11.7963 127.459 11.1587 126.672 11.1572H22.366Z"
            fill="#1D506B"
          />
          <path
            d="M22.3657 19.7285C21.5767 19.7269 20.9357 20.3651 20.9341 21.1541C20.9324 21.9431 21.5707 22.5841 22.3597 22.5858H88.3029C89.0919 22.5872 89.7326 21.9487 89.7341 21.1597C89.7355 20.3707 89.0971 19.73 88.3081 19.7285C88.3063 19.7285 88.3046 19.7285 88.3029 19.7285L22.3657 19.7285Z"
            fill="#1D506B"
          />
          <path
            d="M147.826 33.743H1.20508V0H147.826V33.743ZM3.61529 31.3328H145.416V2.41021H3.61529V31.3328Z"
            fill="#E5E5E5"
          />
          <path
            d="M296.958 240V210.941C296.958 210.941 308.282 231.543 296.958 240Z"
            fill="#F1F1F1"
          />
          <path
            d="M297.657 239.995L276.25 220.343C276.25 220.343 299.085 225.933 297.657 239.995Z"
            fill="#F1F1F1"
          />
          <path
            d="M194.55 147.336C198.751 147.336 202.156 143.931 202.156 139.73C202.156 135.53 198.751 132.125 194.55 132.125C190.35 132.125 186.944 135.53 186.944 139.73C186.944 143.931 190.35 147.336 194.55 147.336Z"
            fill="#1D506B"
          />
          <path
            d="M193.796 142.632C193.625 142.632 193.459 142.577 193.322 142.474L193.313 142.468L191.526 141.1C191.178 140.833 191.112 140.335 191.379 139.987C191.646 139.638 192.144 139.573 192.493 139.84L193.65 140.727L196.386 137.158C196.653 136.81 197.151 136.744 197.499 137.011L197.483 137.035L197.5 137.011C197.848 137.278 197.913 137.777 197.647 138.125L194.429 142.321C194.278 142.517 194.044 142.632 193.797 142.631L193.796 142.632Z"
            fill="white"
          />
          <path
            d="M225.59 77.9114C231.603 77.9114 236.478 73.037 236.478 67.0241C236.478 61.0111 231.603 56.1367 225.59 56.1367C219.578 56.1367 214.703 61.0111 214.703 67.0241C214.703 73.037 219.578 77.9114 225.59 77.9114Z"
            fill="#FFB6B6"
          />
          <path
            d="M235.101 75.3891C235.101 75.3891 238.326 68.8584 238.268 66.3652C238.211 63.872 236.449 57.4562 235.705 57.0331C234.962 56.61 226.944 52.5393 224.772 53.7632C222.599 54.9871 218.775 59.1734 214.651 60.306C213.484 60.6263 214.503 66.6191 214.503 66.6191L219.329 65.921C219.329 65.921 224.361 61.403 224.578 64.4795C224.796 67.5559 229.462 66.2746 229.462 66.2746L229.519 68.7678L231.429 68.8705L231.955 66.2172C231.955 66.2172 232.813 71.6267 231.704 74.4402C230.595 77.2538 235.101 75.3891 235.101 75.3891Z"
            fill="#2F2E41"
          />
        </g>
        <defs>
          <clipPath id="clip0_1500_5904">
            <rect width="322.134" height="240" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

export default CustomTabPanelSvg5;
