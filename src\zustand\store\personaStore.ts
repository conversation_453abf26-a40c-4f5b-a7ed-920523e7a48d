/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface PersonaState {
  selectedPersonaId: string | null;
  selectedPersonaData: any | null;
  setSelectedPersona: (personaId: string) => void;
  clearSelectedPersona: () => void;
  getSelectedPersona: () => any | null;
}

export const usePersonaStore = create<PersonaState>()(
  persist(
    (set, get) => ({
      selectedPersonaId: null,
      selectedPersonaData: null,

      setSelectedPersona: (personaId: string) => {
        set({
          selectedPersonaId: personaId,
          selectedPersonaData: null,
        });
      },

      clearSelectedPersona: () => {
        set({
          selectedPersonaId: null,
          selectedPersonaData: null,
        });
      },

      getSelectedPersona: () => {
        const state = get();
        return {
          id: state.selectedPersonaId,
          data: state.selectedPersonaData,
        };
      },
    }),
    {
      name: 'persona-storage',
      partialize: (state) => ({
        selectedPersonaId: state.selectedPersonaId,
        selectedPersonaData: state.selectedPersonaData,
      }),
    }
  )
);
