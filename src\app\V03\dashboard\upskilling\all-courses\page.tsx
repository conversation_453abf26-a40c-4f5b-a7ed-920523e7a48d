/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  getCourses,
  getPopularCourses,
  getRecommendedCourses,
  getRecentSearch,
} from '@/zustand/services/course.services';
import type { Course, CourseFilters } from '@/types/courseType';
import CourseSearch from '@/components/dashboard/courses/CoursesSearch';
import CourseGrid from '@/components/dashboard/courses/CourseGrid';
import PopularCoursesSection from '@/components/dashboard/courses/MostPopular';
import MainTopicTabs from '@/components/dashboard/courses/MainTopicTab';
import SubTopicTabs from '@/components/dashboard/courses/SubTopicTabs';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import {
  CourseCardSkeleton,
  PopularCourseSkeleton,
} from '@/components/common/skeleton/CoursesSkeletons';

export default function CoursesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [activeSubTopic, setActiveSubTopic] = useState<string>('all');
  const [featuredCourses, setFeaturedCourses] = useState<Course[]>([]);
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([]);
  const [recentSearches, setRecentSearches] = useState<
    { searchQuery: string }[]
  >([]);
  const [recentSearchCourses, setRecentSearchCourses] = useState<Course[]>([]);
  const [allPopularCourses, setAllPopularCourses] = useState<Course[]>([]);
  const [popularDisplayCount, setPopularDisplayCount] = useState(4);
  const [isLoading, setIsLoading] = useState(true);

  const fetchInitialData = async () => {
    setIsLoading(true);
    try {
      const featuredFilters: CourseFilters = {
        status: 'Active',
        sortField: 'createdOn',
        ascending: true,
        pageSize: 3,
      };
      const featuredResponse = await getCourses(featuredFilters);
      setFeaturedCourses(featuredResponse.data);

      const recommendedResponse = await getRecommendedCourses();
      const mappedRecommendedCourses = recommendedResponse.data.map(
        (item: any) => ({
          ...item.courseDetails,
          id: item.courseId,
        })
      );
      setRecommendedCourses(mappedRecommendedCourses);

      const popularResponse = await getPopularCourses();
      const mappedPopularCourses = popularResponse.data.map((item: any) => ({
        ...item.courseDetails,
        id: item.courseId,
        enrollmentCount: item.enrollmentCount,
      }));
      setAllPopularCourses(mappedPopularCourses);

      const searchResponse = await getRecentSearch();
      if (searchResponse?.data?.recentSearches?.length > 0) {
        setRecentSearches(searchResponse.data?.recentSearches ?? []);
        const mostRecentSearchTerm =
          searchResponse.data?.recentSearches?.[0]?.searchQuery ?? '';
        const recentFilters: CourseFilters = {
          status: 'Active',
          search: mostRecentSearchTerm,
          pageSize: 3,
        };
        const recentCourses = await getCourses(recentFilters);
        setRecentSearchCourses(recentCourses.data);
      } else {
        setRecentSearches([]);
        setRecentSearchCourses([]);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchFilteredCourses = async () => {
    if (activeCategory === 'all' && activeSubTopic === 'all' && !searchQuery)
      return;

    setIsLoading(true);
    try {
      const filters: CourseFilters = {
        status: 'Active',
        sortField: 'modifiedOn',
        ascending: true,
        pageSize: 12,
      };

      if (activeCategory !== 'all') filters.mainTopic = activeCategory;
      if (searchQuery) filters.search = searchQuery;

      const response = await getCourses(filters);
      setFeaturedCourses(response.data);
    } catch (error) {
      console.error('Error fetching filtered courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFilteredCourses();
  }, [activeCategory, activeSubTopic, searchQuery]);

  const handleSearch = (term: string) => {
    if (!term.trim()) {
      setSearchQuery('');
      return;
    }

    setSearchQuery(term);
    router.push(
      `/dashboard/upskilling/all-courses/courseList?search=${encodeURIComponent(term)}${
        activeCategory !== 'all'
          ? `&mainTopic=${encodeURIComponent(activeCategory)}`
          : ''
      }${activeCategory !== 'all' ? `&city=${encodeURIComponent(activeCategory)}` : ''}`
    );
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setActiveSubTopic('all');
  };

  const handleSubTopicChange = (subTopic: string) => {
    setActiveSubTopic(subTopic);
  };

  const handleShowMore = () => {
    router.push('/dashboard/upskilling/all-courses/courseList');
  };

  const handleShowMorePopular = () => {
    setPopularDisplayCount((prev) => prev + 6);
  };

  const displayedPopularCourses = allPopularCourses.slice(
    0,
    popularDisplayCount
  );
  const hasMorePopularCourses = allPopularCourses.length > popularDisplayCount;

  return (
    <div>
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'UpSkilling', href: '/dashboard/upskilling/all-courses' },
          { label: 'All Courses', href: 'dashboard/upskilling' },
        ]}
      />
      <div className="mx-auto py-8">
        <h1 className="text-[40px] font-semibold leading-[48px] text-neutral-900 mb-2">
          Your Pathway to Professional Growth
        </h1>
        <div className="mb-8 max-w-[54rem]">
          <p className="text-neutral-700 text-[20px] mb-6">
            Explore courses from top-vetted training providers. Build essential
            soft skills and advanced technical expertise—all in one place. Start
            upskilling today!
          </p>
          <CourseSearch
            onSearch={handleSearch}
            initialSearchTerm={searchQuery}
          />
        </div>

        <Card className="py-8 px-2">
          <MainTopicTabs
            activeCategory={activeCategory}
            onCategoryChange={handleCategoryChange}
          />
          <SubTopicTabs
            activeCategory={activeCategory}
            activeSubTopic={activeSubTopic}
            onSubTopicChange={handleSubTopicChange}
          />
        </Card>

        {isLoading ? (
          <>
            <Card className="pb-4 px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <CourseCardSkeleton key={i} />
                ))}
              </div>
              <Skeleton className="h-10 w-32 rounded-full mt-4" />
            </Card>
            <div className="mt-12">
              <Skeleton className="h-6 w-48 mb-4" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <CourseCardSkeleton key={i} />
                ))}
              </div>
              <Skeleton className="h-10 w-32 rounded-full mt-4" />
            </div>
            <div className="mt-12">
              <Skeleton className="h-6 w-64 mb-4" />
              <div className="space-y-4">
                {[1, 2, 3, 4].map((i) => (
                  <PopularCourseSkeleton key={i} />
                ))}
              </div>
              <Skeleton className="h-10 w-32 rounded-full mt-4" />
            </div>
          </>
        ) : (
          <>
            {!searchQuery && (
              <>
                <Card className="pb-4 px-4 rounded-lg">
                  <CourseGrid
                    title=""
                    courses={featuredCourses}
                    isLoading={isLoading}
                    category={activeCategory}
                  />
                  <Button
                    onClick={handleShowMore}
                    variant="outline"
                    className="px-6 mt-4"
                  >
                    Show more
                  </Button>
                </Card>
                <Card className="mt-12 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold mb-4">
                    Recommended for you
                  </h2>
                  <CourseGrid
                    courses={recommendedCourses}
                    isLoading={isLoading}
                    category={activeCategory}
                  />
                  <Button
                    onClick={handleShowMore}
                    variant="outline"
                    className="px-6 mt-4 bg-transparent"
                  >
                    Show more
                  </Button>
                </Card>
                {recentSearches.length > 0 && (
                  <div className="mt-12">
                    <h2 className="text-xl font-semibold mb-4">
                      Based on your recent search &quot;
                      {recentSearches[0].searchQuery}&quot;
                    </h2>
                    <CourseGrid
                      courses={recentSearchCourses}
                      isLoading={isLoading}
                      category={activeCategory}
                    />
                    <Button
                      onClick={() => {
                        router.push(
                          `/dashboard/upskilling/all-courses/courseList?search=${encodeURIComponent(recentSearches[0].searchQuery)}`
                        );
                      }}
                      variant="outline"
                      className="px-6 mt-4 bg-transparent"
                    >
                      Show more
                    </Button>
                  </div>
                )}
              </>
            )}
            <div className="mt-12">
              <PopularCoursesSection
                courses={displayedPopularCourses}
                isLoading={isLoading}
                hasMore={hasMorePopularCourses}
                onShowMore={handleShowMorePopular}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}
