export type CountryType = {
  airportCode: string;
  hubStatus: string;
  benefitsConfirmed: string;
  benefitsList: string;
  challengesRisk: string;
  countryCode: string;
  connectionImage: string;
  country: string;
  dashboardDescription: string;
  dateSubmitted: string;
  destinations: string;
  documentMaterial: string;
  duediligenceImage: string;
  flagIcon: string;
  flagIconFont: string;
  gatewayOption: string;
  hubCode: string;
  hubOrder: number;
  hubRegion: null | string;
  id: string;
  implementationStatus: string;
  investmentSummary: string;
  isFeatured: boolean;
  isPublished: boolean;
  keyUpdates: string;
  latLong: string;
  latitude: number;
  longitude: number;
  mapImage: string;
  members: string;
  membersList: string;
  numberPartners: string;
  oldId: number;
  partnersList: string;
  polygonSet: string;
  rate1: string;
  rate2: string;
  rationaleSelection: string;
  regionId: string;
  riskAssessmentImage: string;
  scale: number;
  status: string;
  swotAnalysisOpportunities: string;
  swotAnalysisStrength: string;
  swotAnalysisThreats: string;
  swotAnalysisWeakness: string;
  year1: string;
  year2: string;
};
