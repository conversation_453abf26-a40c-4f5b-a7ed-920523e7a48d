import { Search } from 'lucide-react';
import React from 'react';

interface SearchInputProps {
  type?: string;
  placeholder?: string;
  className?: string;
}

function SearchInput({
  type = 'search',
  placeholder = 'Search keyword...',
  className = ' text-[18px]',
  ...rest
}: SearchInputProps) {
  return (
    <div className="relative w-[60%] rounded-full border border-neutral-200 p-[2px]">
      <div className="flex items-center w-full rounded-full bg-white px-4 h-[48px]">
        <Search className="text-gray-400 w-6 h-6 mr-2" />
        <input
          type={type}
          className={`px-2 py-4 bg-transparent flex-1 focus:outline-none focus:ring-0 border-none ${className}`}
          placeholder={placeholder}
          {...rest}
        />
      </div>
    </div>
  );
}

export default SearchInput;
