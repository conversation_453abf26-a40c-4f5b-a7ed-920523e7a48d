/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useJobStore } from '@/zustand/store/jobStore';
import { useEffect, useRef, useState } from 'react';
import type { Job } from '@/types/jobTypes';
import CareerSearchInput from './career-profile-completeness/CareerSearchInput';
import ProfileCompletionProcess from './career-profile-completeness/ProfileCompleteness';
import JobSearchInput from './JobSearchInput';
import { LoadingState } from '@/components/common/LoaderState';
import JobList from './JobLists';
import { Briefcase } from 'lucide-react';
import { PaginationControls } from '@/components/common/PaginationController';
import { Card } from '@/components/ui/card';
import JobDetails from './JobDetails';
import { useFetchJobs } from '@/queries';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useAppliedJobStore } from '@/zustand/store/appliedStore';

const JobCard: React.FC = () => {
  const {
    page,
    size,
    searchTerm,
    location,
    jobType,
    monthlyFrom,
    status,
    total,
    setSearchTerm,
    setLocation,
    setJobType,
    setMonthlyFrom,
    setStatus,
  } = useJobStore();

  useEffect(() => {
    const storedData = localStorage.getItem('job-storage');
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      if (parsedData.state) {
        const { searchTerm, location, jobType, monthlyFrom, status } =
          parsedData.state;
        setSearchTerm(searchTerm);
        setLocation(location);
        setJobType(jobType);
        setMonthlyFrom(monthlyFrom);
        setStatus(status);
      }
    }
  }, [setSearchTerm, setLocation, setJobType, setMonthlyFrom, setStatus]);

  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const { fetchJobs: fetchAppliedJobs } = useAppliedJobStore();
  const { user } = useAuthStore();

  const totalPages = Math.max(1, Math.ceil(total / size));

  const { data, isLoading } = useFetchJobs({
    page,
    size,
    search: searchTerm,
    location,
    jobType,
    monthlyFrom,
    status,
  });

  useEffect(() => {
    if (user?.id) {
      fetchAppliedJobs({ userId: user.id });
    }
  }, [user, fetchAppliedJobs]);

  useEffect(() => {
    if (data) {
      const activeJobs = data.data.filter((job) => job.status === 'Active');
      setFilteredJobs(activeJobs);

      if (data.total !== undefined && data.total !== null) {
        useJobStore.setState({ total: data.total });
      }
    }
  }, [data]);

  const handleStorageChange = () => {
    if (user?.id) {
      fetchAppliedJobs({ userId: user.id });
    }
    setFilteredJobs((prev) => [...prev]);
  };
  useEffect(() => {
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [user, fetchAppliedJobs]);

  const handleJobSelect = (job: Job) => setSelectedJob(job);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      useJobStore.setState({ page: newPage });
    }
  };

  const handleStorageChange1 = (_e: StorageEvent) => {
    setFilteredJobs([...filteredJobs]);
  };

  useEffect(() => {
    window.addEventListener('storage', handleStorageChange1);
    return () => window.removeEventListener('storage', handleStorageChange1);
  }, [filteredJobs]);

  const [profileCompleted, _setProfileCompleted] = useState(false);
  const [searchTriggered, _setSearchTriggered] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);

  const modalTriggered = useRef(false);

  const handleCareerSearch = () => {
    _setSearchTriggered(true);
    if (!profileCompleted && !modalTriggered.current) {
      setShowWarningModal(true);
      modalTriggered.current = true;
    }
  };

  const handleProfileComplete = () => {
    _setProfileCompleted(true);
    setShowWarningModal(false);
  };

  const handleCloseModal = () => {
    setShowWarningModal(false);
  };

  return (
    <div className="w-full">
      <Dialog open={showWarningModal} onOpenChange={setShowWarningModal}>
        <DialogContent
          className="max-w-[600px]"
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold leading-[32px]">
              Complete Your Profile!
            </DialogTitle>
            <DialogDescription className="text-[18px]">
              Profile completion is required to access job listings.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex items-end justify-end w-1/2 gap-1">
            <Button
              variant={'outline'}
              onClick={handleCloseModal}
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              variant={'destructive'}
              onClick={handleCloseModal}
              className="w-full"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {!profileCompleted && !searchTriggered ? (
        <div>
          <CareerSearchInput
            setSelectedJob={setSelectedJob}
            onSearch={handleCareerSearch}
          />
          <ProfileCompletionProcess
            onComplete={handleProfileComplete}
            key={`profile-completion-${profileCompleted}`}
          />
        </div>
      ) : (
        <div>
          <JobSearchInput setSelectedJob={setSelectedJob} />
          {isLoading ? (
            <div className="w-full">
              <LoadingState />
            </div>
          ) : (
            <div className="flex flex-col md:flex-row h-full w-full gap-4 py-10">
              <div className="w-full md:w-[40%]">
                <div className="max-h-[920px] overflow-y-auto rounded-lg">
                  <div className="flex flex-col space-y-4">
                    {filteredJobs.length > 0 ? (
                      <JobList
                        jobs={filteredJobs}
                        selectedJobId={selectedJob?.id}
                        onJobSelect={handleJobSelect}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center h-40 text-center">
                        <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
                        <p className="text-neutral-500">No jobs found</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-10">
                  {filteredJobs.length > 0 && (
                    <PaginationControls
                      page={page}
                      totalPages={totalPages}
                      handlePageChange={handlePageChange}
                    />
                  )}
                </div>
              </div>

              <div className="w-full md:w-[60%]">
                <Card className="p-6 h-full rounded-lg">
                  <JobDetails selectedJob={selectedJob} />
                </Card>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default JobCard;
