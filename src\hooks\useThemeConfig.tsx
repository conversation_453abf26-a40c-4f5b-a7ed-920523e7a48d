import { useEffect, useCallback } from 'react';

export function useThemeConfig(apiUrl = '/api/theme-config') {
  const updateTheme = useCallback(
    (themeConfig: { [x: string]: string | null }) => {
      const root = document.documentElement;
      Object.keys(themeConfig).forEach((key) => {
        root.style.setProperty(`--${key}`, themeConfig[key]);
      });
    },
    []
  );

  const fetchTheme = useCallback(async () => {
    try {
      const res = await fetch(apiUrl);
      if (!res.ok) {
        throw new Error('Failed to fetch theme configuration');
      }
      const themeConfig = await res.json();
      updateTheme(themeConfig);
    } catch (error) {
      console.error('Error fetching theme configuration:', error);
    }
  }, [apiUrl, updateTheme]);

  useEffect(() => {
    fetchTheme();
  }, [fetchTheme]);

  return { refetchTheme: fetchTheme };
}
