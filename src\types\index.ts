/* eslint-disable @typescript-eslint/no-explicit-any */
import type { StaticImageData } from 'next/image';

export interface UserProfile {
  id: string;
  name: string;
  location: string;
  photo: StaticImageData;
  citizenDetails: {
    userId: string;
    registrationDate: string;
    age: number;
    gender: string;
    nationality: string;
    nationalId: string;
    email: string;
    mobile: string;
  };
  employmentDetails: {
    status: string;
    type: string;
    startDate: string;
    employer: string;
    position: string;
    category: string;
    monthlySalary: number;
    pensionableSalary: number;
  };
  benefits: {
    monthlyBenefits: number;
    lastProcessedDate: string;
    totalBenefits: number;
    eligibility: string;
    status: string;
  };
}

export interface CompanyDetails {
  id: number;
  name: string;
  location: string;
  companyId: string;
  registrationDate: string;
  industry: string;
  companySize: string;
  companyAddress: string;
  postCode: string;
  email: string;
}

export interface Representative {
  id: string;
  name: string;
  role: string;
  email: string;
}

export interface Benefit {
  monthlyBenefits: number;
  lastPaymentDate: string;
  TotalBenefitsReceived: string;
  CompanyEligibility: string;
}

export interface HiringInformation {
  hiringStatus: string;
  activeJobPosts: number;
  hiredOnPlatfom: number;
}

export interface IRegisterProps {
  email: string;
  password: string;
  role: 'User';
  fullName?: string;
}

export interface ILoginProps {
  email: string;
  password: string;
  role: 'User';
}

export interface IUser {
  id: string;
  email: string;
  username?: string;
  userName?: string;
  fullName?: string;
  phone?: string;
  phoneNumber?: string;
  mobile?: string;
  role?: string;
  emailverified: boolean;
  employmentStatus?: string;
  educationLevel?: string;
  experienceLevel?: string;
  industry?: string;
  specialization?: string;
  subSpecialization?: string;
  privacyPolicyAccepted?: boolean;
  termsAccepted?: boolean;
  userId?: string;
  nationalId?: string;
  nationality?: string;
  jobStartDate?: string;
  profession?: string;
  employmentCategory?: string;
  monthlySalary?: string;
  pensionableSalary?: string;
  highestDegree?: string;
  institution?: string;
  fieldOfStudy?: string;
  countryId?: string;
}

export interface CitizenState {
  citizen: IUser | null;
  isLoading: boolean;
  showLoader: boolean;
}

export interface AuthState {
  user: IUser | null;
  token: string | null;
  isloading: boolean;
  showLoader: boolean;
  register: (_credentials: IRegisterProps) => Promise<void>;
  login: (_credentials: ILoginProps) => Promise<void>;
  logout: () => void;
  setUser: (_user: IUser) => void;
  role: string;
}

export interface Applicant {
  id: string;
  userId: string;
  jobId: string;
  appliedDate: string;
  status: 'Pending' | 'Shortlisted' | 'Contacted' | 'Rejected' | 'Hired';
  attributes: any[];
  cv: {
    id: string;
    base64Content: string;
    fileName: string;
  };
  job: {
    id: string;
    title: string;
    description: string;
    location: string;
    monthlyFrom: number;
    monthlyTo: number;
    expiryDate: string;
    postedDate: string;
    jobType: string;
    jobMode: string;
    jobPosition: string;
    industry: string;
    experienceLevel: string;
    companyName: string;
    currency: string;
    createdById: string;
    lastModifiedById: string;
  };
  user: {
    employmentStatus: string | null;
    educationLevel: string | null;
    experienceLevel: string | null;
    industry: string | null;
    specialization: string;
    subSpecialization: string;
    attributes: any[];
    cVs: any[];
    id: string;
    userName: string;
    normalizedUserName: string;
    email: string;
    normalizedEmail: string;
    emailConfirmed: boolean;
    passwordHash: string;
    securityStamp: string;
    concurrencyStamp: string;
    phoneNumber: string | null;
    phoneNumberConfirmed: boolean;
    twoFactorEnabled: boolean;
    lockoutEnd: string | null;
    lockoutEnabled: boolean;
    accessFailedCount: number;
  };
}

export interface ApplicantResponse {
  data: Applicant[];
  page: number;
  size: number;
  total: number;
  success: boolean;
}

export interface AdminValue {
  id: string;
  category: string;
  subCategory: string | null;
  value: string;
  label: string;
  language: string;
  status: string;
  comment: string | null;
  path: string | null;
  createdById: string;
  createdBy: string | null;
  lastModifiedById: string;
  lastModifiedBy: string | null;
}

export interface getAdminValuesProps {
  page?: number;
  pageSize?: number;
  language?: string;
  status?: string;
  category?: string;
  path?: string;
  subcategory?: string;
}

export interface ApiUser {
  id: string | number;
  name?: string;
  username?: string;
  userName?: string;
  user_id?: string;
  employment_status?: string;
  email?: string;
  created_at?: string;
  avatar?: string;
}

export interface User {
  id: string | number;
  name: string;
  user_id: string;
  employment_status: string;
  email: string;
  registration_date: string;
  imageUrl: string;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
}

export interface Topic {
  id: string;
  value: string;
  label: string;
}

export interface Cities {
  id: string;
  value: string;
  label: string;
}

// settings types
export interface ISettings {
  id: string;
  platformLogo: string;
  platformLogoDark: string;
  governmentEmblem: string;
  governmentEmblemDark: string;
  brandColor: string;
  topBarBgColor: string;
  topBarButtonColor: string;
  navMenuColor: string;
  navMenuPositionTop: boolean;
  headingFont: string;
  headingTextColor: string;
  bodyTextFont: string;
  bodyTextColor: string;
  buttonStyle: string;
  buttonColor: string;
  bgStyleColor: string;
  bgStyleImg: string;
  isBgStyleColor: boolean;
  overlayColor: string;
  overlayPercentage: string;
  isDefault: boolean;
}

export interface ISCO {
  iscoCode: string;
  iscoLabel: string;
  iscoDecription: string;
  fitCategory: 'Best Fit' | 'Great Fit' | 'Good Fit';
}

export interface JobZone {
  code: number;
  name: string;
  description: string;
  experience: string;
  education: string;
  training: string;
  examples: string;
  iscos: ISCO[];
}

export type CourseType = 'Mandatory' | 'Elective';

export interface Course {
  id: string;
  code: string;
  name: string;
  description: string;
  emsiSkills: string;
  escoSkills: string | null;
  universityId: string;
  credits: number | null;
  type: CourseType;
  score: number;
  underMarketDemand: boolean;
}

export interface University {
  id: string;
  name: string;
  hubId: string;
}

export interface Program {
  id: string;
  code: string | null;
  name: string;
  department: string;
  duration: number | null;
  durationAmount: number;
  durationPeriod: string;
  durationOriginalDuration: number | null;
  durationOriginalPeriod: string;
  isFullTime: boolean;
  isOnCampus: boolean;
  universityId: string;
  summary: string;
  totalCredits: number | null;
  university: University;
  courses: Course[];
}

export interface UserProgram {
  data: Program[];
}

export interface GetProgramsListProps {
  universityId: string;
  programNameOrCode?: string;
  faculties?: string[];
  educationLevels?: string[];
  page: number;
  pageSize?: number;
}

export interface ProgramItem {
  id: string;
  code: string | null;
  name: string;
  department: string;
  summary: string;
  duration: string;
  isFullTime: boolean;
  isOnCampus: boolean;
  totalCredits: number;
}

export interface ProgramsListResponse {
  result: ProgramItem[];
  page: number;
  pageSize: number;
  totalCount: number;
}

export interface ProgramDetailsResponse {
  id: string;
  code: string | number | null;
  name: string;
  department: string;
  duration: string;
  isFullTime: boolean;
  isOnCampus: boolean;
  universityId: string;
  summary: string;
  totalCredits: number | null;
  university: {
    id: string;
    name: string;
    hubId: string;
  };
  courses: Course[];
}

export type SaveUserProgramCoursesResponse = any;
export type SavedCourses = {
  programId: string;
  courseIds: string[];
};

export interface UniversityItem {
  id: string;
  name: string;
  location?: string;
}

export interface DepartmentWithPrograms {
  department: string;
  programs: ProgramItem[];
}

export interface ApiResponse {
  totalCount: number;
  result: DepartmentWithPrograms[];
}

export interface AssessmentScore {
  id: string;
  name: string;
  description: string;
  interests: string[];
  score: number;
}

export interface Question {
  id: string;
  question: string;
  order: number;
  answerValue: number | string | null;
  length: number | string;
  result: string;
}

export interface FetchJobParams {
  page?: number;
  size?: number;
  search?: string;
  location?: string;
  jobType?: string;
  monthlyFrom?: string;
  status?: 'Active' | 'Closed';
}

export interface Params {
  page?: number;
  size?: number;
  search?: string;
  location?: string;
  jobType?: string;
  monthlyFrom?: string;
  userId?: string;
  ascending?: boolean;
}

export interface EnrollmentDetails {
  id: string;
  userName: string;
  email: string;
  enrollmentDate: string;
  isCompleted: boolean;
}

export interface RecentSearch {
  searchQuery: string;
}

export interface RecentSearchResponse {
  recentSearches: RecentSearch[];
}
