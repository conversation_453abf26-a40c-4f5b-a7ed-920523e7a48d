'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { Topic } from '@/types';

interface MainTopicTabsProps {
  activeCategory: string;
  onCategoryChange: (_category: string) => void;
}

export default function MainTopicTabs({
  activeCategory,
  onCategoryChange,
}: MainTopicTabsProps) {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { data: topicsData, isLoading } = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const mainTopics: Topic[] = topicsData?.data?.data?.customValues || [];

  const checkScroll = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft <
        container.scrollWidth - container.clientWidth - 10
      );
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScroll);
      checkScroll();
      setTimeout(checkScroll, 100);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
      }
    };
  }, [mainTopics]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  if (isLoading) {
    return (
      <div className="h-12 bg-transparent animate-pulse rounded-md my-4"></div>
    );
  }

  return (
    <div className="relative mb-6">
      <div className="flex items-center">
        {showLeftArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 z-10 text-white bg-primary-500 shadow-md rounded-full h-12 w-12  hover:bg-primary-500 hover:text-white"
            onClick={scrollLeft}
          >
            <ChevronLeft className="h-6 w-6 text-white" />
            <span className="sr-only">Scroll left</span>
          </Button>
        )}

        <div
          ref={scrollContainerRef}
          className="flex overflow-x-auto pb-2 scrollbar-hide mx-4 scroll-smooth"
          style={{ msOverflowStyle: 'none', scrollbarWidth: 'none' }}
        >
          {mainTopics.map((topic) => (
            <button
              key={topic.id}
              onClick={() => onCategoryChange(topic.label)}
              className={`px-4 py-2 whitespace-nowrap mr-2 rounded-md ${activeCategory === topic.label
                  ? 'bg-primary-400 text-primary-500 border-2 border-primary-500'
                  : 'bg-transparent text-primary-500 hover:bg-primary-400'
                }`}
            >
              {topic.label}
            </button>
          ))}
        </div>

        {showRightArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 z-10 text-white bg-primary-500 shadow-md rounded-full h-12 w-12  hover:bg-primary-500 hover:text-white"
            onClick={scrollRight}
          >
            <ChevronRight className="h-6 w-6 text-white" />
            <span className="sr-only">Scroll right</span>
          </Button>
        )}
      </div>
    </div>
  );
}
