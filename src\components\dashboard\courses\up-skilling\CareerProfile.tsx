import { Button } from '@/components/ui/button';
import { EyeIcon, Upload } from 'lucide-react';

export default function CareerProfile() {
  return (
    <div className="bg-white p-7  rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-semibold text-[20px] leading-[28px] text-neutral-900">
          Is your CV up to date?
        </h2>
      </div>

      <p className="text-[16px] text-neutral-500 mb-4">
        Help us create a personalized upskilling roadmap tailored to your career
        goals by ensuring your CV is up to date.
      </p>

      <div className="flex justify-between items-center mb-4 border border-neutral-200 rounded-lg p-3">
        <div className="">
          <h3 className="text-[16px] font-medium mb-2">John.smith-cv.pdf</h3>
          <div className="text-[16px] text-neutral-500">
            525KB • 12 Jan 2025
          </div>
        </div>
        <div className="items-center">
          <EyeIcon className="text-primary-500 cursor-pointer" />
        </div>
      </div>

      <Button
        variant="outline"
        className="w-full flex items-center justify-center gap-2 mb-2"
      >
        <Upload className="h-4 w-4" />
        Upload a new CV
      </Button>
    </div>
  );
}
