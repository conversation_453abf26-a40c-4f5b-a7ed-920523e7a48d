'use client';

import React, { useState } from 'react';
import { ChevronRight, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import JobIconbb from '@/components/icons/JobIcon';
import StatsCard from '../../job-details/components/StatsCard';
import EducationLevelsPie from '../../components/charts/EducationPieChart';
import FieldsEducation from '../../job-details/components/FieldsEducation';
import SalaryBrackets from '../../job-details/components/SalaryBrackets';
import TopSkillsDemand from '../../job-details/components/TopSkillsDemand';
import useSettingsStore from '@/zustand/store/settingsStore';

export const mockJobs = [
  {
    id: 1,
    title: 'Senior AI Scientist II',
    jobCategories: 'Software Developer',
    company: 'TechCorp Solutions',
    location: 'Careem | Kuwait City, Kuwait',
    description:
      'Software developers research, analyse and evaluate requirements for existing or new software applications and operating systems, and design, develop, test and maintain software solutions to meet these requirements.',
    salaryRange: 'KWD 380-400 per month',
    jobType: 'Full-time',
    EmploymentType: 'Remote',
    postedWhen: 'Posted 4 days ago',
  },
  {
    id: 2,
    title: 'Product Designer',
    jobCategories: 'UX/UI Designer',
    company: 'Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 3,
    title: 'Project Owner',
    jobCategories: 'Project Director',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 4,
    title: 'Machine Learning Engineer',
    jobCategories: 'Cloud Architect',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 5,
    title: 'Machine Learning Engineer',
    jobCategories: 'Cyber Security Engineer',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 6,
    title: 'Data Analyst Engineer',
    jobCategories: 'Data Analyst',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
];

const OccupationCard: React.FC = () => {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  const [selectedJob, setSelectedJob] = useState(mockJobs[0]);

  return (
    <div className="flex h-auto w-full gap-4 py-10">
      <div className="w-[35%]">
        <Card className="h-[39%] rounded-lg overflow-y-auto px-6 py-6">
          <h2 className="font-semibold text-[20px] text-primary-400">
            Top Occupations
          </h2>
          <Tabs defaultValue="categories" className="mt-14 text-left">
            <TabsList className="flex space-x-4 px-0 bg-transparent justify-start text-primary-500">
              <TabsTrigger
                className="leading-[25.6px] text-[14px] !text-primary-500"
                value="categories"
              >
                Job Categories
              </TabsTrigger>
              <TabsTrigger value="titles" className="!text-primary-500">
                Job Titles
              </TabsTrigger>
            </TabsList>
            <TabsContent value="categories">
              <div className="flex flex-col space-y-6 mt-6">
                {mockJobs.map((job) => (
                  <button
                    key={job.id}
                    className={`cursor-pointer px-6 py-3 rounded-full flex justify-between ${
                      selectedJob?.id === job.id
                        ? 'bg-primary-500 text-white'
                        : ''
                    }`}
                    onClick={() => setSelectedJob(job)}
                  >
                    <h3 className="text-[16px] font-semibold">
                      {job.jobCategories}
                    </h3>
                    <ChevronRight className="text-[--headingTextColor]" />
                  </button>
                ))}
              </div>
            </TabsContent>
            <TabsContent value="titles">
              <div className="flex flex-col space-y-6 mt-6">
                {mockJobs.map((job) => (
                  <button
                    key={job.id}
                    className={`cursor-pointer px-6 py-3 rounded-full flex justify-between ${
                      selectedJob?.id === job.id
                        ? 'bg-primary-500 text-white'
                        : ''
                    }`}
                    onClick={() => setSelectedJob(job)}
                  >
                    <h3 className="text-[16px] font-semibold">{job.title}</h3>
                    <ChevronRight className="text-[--headingTextColor]" />
                  </button>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
      <div className="w-[65%] bg-white rounded-md p-6">
        {selectedJob ? (
          <>
            <div className="flex justify-between">
              <h3 className="text-[32px] font-semibold  text-blue-400">
                {selectedJob.jobCategories}
              </h3>
              <Button variant="outline">
                <Download />
                Download Job Card
              </Button>
            </div>

            <div className="mt-4 w-full">
              <div className="flex items-center gap-2">
                <JobIconbb stroke={brandColor} />
                <h4 className="font-semibold text-[18px] text-blue-400">
                  Job Description
                </h4>
              </div>
              <p className="text-[13px] w-[98%] text-[#646A77] mt-4">
                {selectedJob.description}
              </p>
              <div className="mt-6 flex gap-4">
                <StatsCard
                  title="Software Developer job posts in the quarter"
                  value="741"
                />
                <StatsCard
                  title="Average years of experience listed in job posts"
                  value="1–3 years"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-32 mt-10">
                <EducationLevelsPie />
                <FieldsEducation />
              </div>

              <div className="mt-8">
                <SalaryBrackets />
                <div className="mt-8">
                  <TopSkillsDemand />
                </div>
              </div>
            </div>
          </>
        ) : (
          <p> No content </p>
        )}
      </div>
    </div>
  );
};

export default OccupationCard;
