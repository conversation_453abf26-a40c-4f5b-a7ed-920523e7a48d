'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Title from '@/components/ui/title';
import Text from '@/components/ui/text';
import CustomPanelSvg from '@/components/icons/svg/CustomTabPanelSvgIcon';
import CustomTabPanelSvg1 from '@/components/icons/svg/CustomTabPanelSvgIcon1';
import CustomTabPanelSvg2 from '@/components/icons/svg/CustomTabPanelSvgIcon2';
import CustomTabPanelSvg3 from '@/components/icons/svg/CustomTabPanelSvgIcon3';
import CustomTabPanelSvg4 from '@/components/icons/svg/CustomTabPanelSvgIcon4';
import CustomTabPanelSvg5 from '@/components/icons/svg/CustomTabPanelIconSvg5';
import type { AssessmentScore } from '@/types';

interface AssessmentsTabsProps {
  assessmentScores: AssessmentScore[];
}

const AssessmentsTabs: React.FC<AssessmentsTabsProps> = ({
  assessmentScores,
}) => {
  const [activeTab, setActiveTab] = useState('realistic');
  const [realistic, setRealistic] = useState<AssessmentScore | null>(null);
  const [investigative, setInvestigative] = useState<AssessmentScore | null>(
    null
  );
  const [artistic, setArtistic] = useState<AssessmentScore | null>(null);
  const [social, setSocial] = useState<AssessmentScore | null>(null);
  const [enterprising, setEnterprising] = useState<AssessmentScore | null>(
    null
  );
  const [conventional, setConventional] = useState<AssessmentScore | null>(
    null
  );

  useEffect(() => {
    if (!assessmentScores || assessmentScores.length === 0) return;

    assessmentScores.forEach((item) => {
      if (item.name === 'Artistic') {
        setArtistic(item);
        if (item === assessmentScores[0]) {
          setActiveTab('artistic');
        }
      }
      if (item.name === 'Investigative') {
        setInvestigative(item);
        if (item === assessmentScores[0]) {
          setActiveTab('investigative');
        }
      }
      if (item.name === 'Realistic') {
        setRealistic(item);
        if (item === assessmentScores[0]) {
          setActiveTab('realistic');
        }
      }
      if (item.name === 'Enterprising') {
        setEnterprising(item);
        if (item === assessmentScores[0]) {
          setActiveTab('enterprising');
        }
      }
      if (item.name === 'Conventional') {
        setConventional(item);
        if (item === assessmentScores[0]) {
          setActiveTab('conventional');
        }
      }
      if (item.name === 'Social') {
        setSocial(item);
        if (item === assessmentScores[0]) {
          setActiveTab('social');
        }
      }
    });
  }, [assessmentScores]);

  return (
    <div className="max-w-[calc(100vw_-_80px)]">
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="bg-transparent gap-4"
      >
        <TabsList className="h-auto flex flex-wrap scrollbar-hide bg-transparent justify-start w-full gap-y-4 gap-x-5">
          <TabsTrigger
            value="realistic"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Realistic
          </TabsTrigger>
          <TabsTrigger
            value="investigative"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Investigative
          </TabsTrigger>
          <TabsTrigger
            value="artistic"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Artistic
          </TabsTrigger>
          <TabsTrigger
            value="social"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Social
          </TabsTrigger>
          <TabsTrigger
            value="enterprising"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Enterprising
          </TabsTrigger>
          <TabsTrigger
            value="conventional"
            className="font-medium data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 rounded-none"
          >
            Conventional
          </TabsTrigger>
        </TabsList>
        <TabsContent value="realistic">
          <div className="mb-[20px]">
            <CustomPanelSvg />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {realistic?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {realistic?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">
            {realistic?.description}
          </Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(realistic?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="investigative">
          <div className="mb-[20px]">
            <CustomTabPanelSvg1 />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {investigative?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {investigative?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">
            {investigative?.description}
          </Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(investigative?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="artistic">
          <div className="mb-[20px]">
            <CustomTabPanelSvg2 />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {artistic?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {artistic?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">{artistic?.description}</Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(artistic?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="social">
          <div className="mb-[20px]">
            <CustomTabPanelSvg3 />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {social?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {social?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">{social?.description}</Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(social?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="enterprising">
          <div className="mb-[20px]">
            <CustomTabPanelSvg4 />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {enterprising?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {enterprising?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">
            {enterprising?.description}
          </Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(enterprising?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="conventional">
          <div className="mb-[20px]">
            <CustomTabPanelSvg5 />
          </div>
          <Title variant="h2" className="text-blue-400 mb-3">
            {conventional?.name}
          </Title>
          <Text variant="large" className="text-neutral-700 mb-3 font-semibold">
            Your score: {conventional?.score}
          </Text>
          <Text className="mb-3 text-neutral-700">
            {conventional?.description}
          </Text>
          <Text className="text-neutral-700">They like:</Text>
          <ul className="list-disc pl-5">
            {(conventional?.interests || []).map((text) => (
              <li key={text}>
                <Text className="text-neutral-700">{text}</Text>
              </li>
            ))}
          </ul>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AssessmentsTabs;
