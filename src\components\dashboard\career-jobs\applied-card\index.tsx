/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React from 'react';
import { useState } from 'react';
import { Briefcase } from 'lucide-react';
import type { AppliedJob } from '@/types/jobTypes';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { LoadingState } from '@/components/common/LoaderState';
import { PaginationControls } from '@/components/common/PaginationController';
import AppliedJobList from './AppliedLists';
import AppliedJobDetails from './AppliedDetails';
import { useAppliedJobs } from '@/queries';
import AppliedSearchInput from './AppliedJobsSearch';

const AppliedJobs: React.FC = () => {
  const [selectedJob, setSelectedJob] = useState<AppliedJob | null>(null);
  const [filteredJobs, setFilteredJobs] = useState<AppliedJob[]>([]);
  const { user } = useAuthStore();

  const [page, setPage] = useState(1);
  const size = 10;

  const { data, isLoading, refetch } = useAppliedJobs({
    userId: user?.id,
    page,
    size,
  });

  const jobs = data?.data || [];
  const total = data?.total || 0;
  const totalPages = Math.max(1, Math.ceil(total / size));

  React.useEffect(() => {
    if (user?.id && jobs.length > 0) {
      const userJobs = jobs.filter((job) => job.userId === user.id);
      setFilteredJobs(userJobs);

      if (userJobs.length > 0 && !selectedJob) {
        setSelectedJob(userJobs[0]);
      }
    } else {
      setFilteredJobs([]);
    }
  }, [jobs, user?.id, selectedJob]);

  const handleJobSelect = (job: AppliedJob) => setSelectedJob(job);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleSearch = (filtered: AppliedJob[]) => {
    setFilteredJobs(filtered);
    if (
      filtered.length > 0 &&
      !filtered.some((job) => job.id === selectedJob?.id)
    ) {
      setSelectedJob(filtered[0]);
    } else if (filtered.length === 0) {
      setSelectedJob(null);
    }
  };

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <Briefcase className="h-16 w-16 text-neutral-300 mb-4" />
        <h3 className="text-xl font-medium text-neutral-900 mb-2">
          No User Logged In
        </h3>
        <p className="text-neutral-500 max-w-md">
          Please log in to view your applied jobs
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="max-w-[1050px] mx-auto">
        <AppliedSearchInput onSearch={handleSearch} />
      </div>

      {isLoading ? (
        <div className="w-full">
          <LoadingState />
        </div>
      ) : (
        <div className="flex flex-col md:flex-row h-full w-full gap-4 py-10">
          <div className="w-full md:w-[40%]">
            <div className="max-h-[920px] overflow-y-auto rounded-lg">
              {filteredJobs.length > 0 ? (
                <AppliedJobList
                  jobs={filteredJobs}
                  selectedJobId={selectedJob?.id}
                  onJobSelect={handleJobSelect}
                  user={user}
                  onRefresh={handleRefresh}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
                  <p className="text-neutral-500">No jobs found</p>
                </div>
              )}
            </div>

            <div className="mt-10">
              {filteredJobs.length > 0 && (
                <PaginationControls
                  page={page}
                  totalPages={totalPages}
                  handlePageChange={handlePageChange}
                />
              )}
            </div>
          </div>

          <div className="w-full md:w-[60%]">
            <AppliedJobDetails selectedJob={selectedJob} />
          </div>
        </div>
      )}
    </div>
  );
};

export default AppliedJobs;
