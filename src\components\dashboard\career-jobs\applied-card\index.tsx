'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Briefcase } from 'lucide-react';
import type { AppliedJob } from '@/types/jobTypes';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { LoadingState } from '@/components/common/LoaderState';
import { PaginationControls } from '@/components/common/PaginationController';
import AppliedJobList from './AppliedLists';
import AppliedJobDetails from './AppliedDetails';
import { useAppliedJobs } from '@/queries';
import AppliedSearchInput from './AppliedJobsSearch';

const AppliedJobs: React.FC = () => {
  const [selectedJob, setSelectedJob] = useState<AppliedJob | null>(null);
  const { user } = useAuthStore();

  const [page, setPage] = useState(1);
  const [searchParams, setSearchParams] = useState<{
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: number;
  }>({});

  const size = 10;

  const { data, isLoading, refetch } = useAppliedJobs({
    userId: user?.id,
    page,
    size,
    ...searchParams,
    monthlyFrom: searchParams.monthlyFrom?.toString(),
  });

  const jobs = data?.data || [];
  const total = data?.total || 0;
  const totalPages = Math.max(1, Math.ceil(total / size));

  const userJobs = jobs.filter((job) => job.userId === user?.id);

  useEffect(() => {
    if (userJobs.length > 0) {
      if (!selectedJob || !userJobs.some((job) => job.id === selectedJob.id)) {
        setSelectedJob(userJobs[0]);
      }
    } else {
      setSelectedJob(null);
    }
  }, [userJobs, selectedJob]);

  const handleJobSelect = (job: AppliedJob) => setSelectedJob(job);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleSearch = (params: {
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: number;
  }) => {
    setSearchParams(params);
    setPage(1);
  };

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <Briefcase className="h-16 w-16 text-neutral-300 mb-4" />
        <h3 className="text-xl font-medium text-neutral-900 mb-2">
          No User Logged In
        </h3>
        <p className="text-neutral-500 max-w-md">
          Please log in to view your applied jobs
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="max-w-[1050px] mx-auto">
        <AppliedSearchInput onSearch={handleSearch} isLoading={isLoading} />
      </div>

      {isLoading ? (
        <div className="w-full">
          <LoadingState />
        </div>
      ) : (
        <div className="flex flex-col md:flex-row h-full w-full gap-4 py-10">
          <div className="w-full md:w-[40%]">
            <div className="max-h-[920px] overflow-y-auto rounded-lg">
              {userJobs.length > 0 ? (
                <AppliedJobList
                  jobs={userJobs}
                  selectedJobId={selectedJob?.id}
                  onJobSelect={handleJobSelect}
                  user={user}
                  onRefresh={handleRefresh}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
                  <p className="text-neutral-500">
                    {Object.keys(searchParams).length > 0
                      ? 'No jobs match your search criteria'
                      : 'No applied jobs found'}
                  </p>
                  {Object.keys(searchParams).length > 0 && (
                    <p className="text-sm text-neutral-400 mt-1">
                      Try adjusting your search terms
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="mt-10">
              {userJobs.length > 0 && (
                <PaginationControls
                  page={page}
                  totalPages={totalPages}
                  handlePageChange={handlePageChange}
                />
              )}
            </div>
          </div>

          <div className="w-full md:w-[60%]">
            <AppliedJobDetails selectedJob={selectedJob} />
          </div>
        </div>
      )}
    </div>
  );
};

export default AppliedJobs;
