import type { IUser } from '@/types';

export interface CitizenState {
  citizen: IUser | null;
  isLoading: boolean;
  showLoader: boolean;
}

export type citizenAction =
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' }
  | { type: 'SHOW_LOADER' }
  | { type: 'HIDE_LOADER' }
  | { type: 'SET_CITIZEN'; payload: IUser };

export const citizenReducer = (
  state: CitizenState,
  action: citizenAction
): CitizenState => {
  switch (action.type) {
    case 'START_LOADING':
      return { ...state, isLoading: true };
    case 'STOP_LOADING':
      return { ...state, isLoading: false };
    case 'SHOW_LOADER':
      return { ...state, showLoader: true };
    case 'HIDE_LOADER':
      return { ...state, showLoader: false };
    case 'SET_CITIZEN':
      return {
        ...state,
        citizen: action.payload,
        isLoading: false,
        showLoader: false,
      };
    default:
      return state;
  }
};
