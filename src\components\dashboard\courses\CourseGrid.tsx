'use client';
import { Button } from '@/components/ui/button';
import type { Course } from '@/types/courseType';
import CourseCard from './CourseCard';

interface CourseGridProps {
  title?: string;
  courses: Course[];
  isLoading: boolean;
  showMoreButton?: boolean;
  onShowMore?: () => void;
  category?: string;
}

export default function CourseGrid({
  title,
  courses,
  isLoading,
  showMoreButton = false,
  onShowMore,
}: CourseGridProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 h-48 rounded-t-lg"></div>
            <div className="p-4 border border-gray-200 rounded-b-lg">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (courses.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">No courses found</div>
    );
  }

  return (
    <div>
      {title && <h2 className="text-xl font-semibold mb-4">{title}</h2>}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {courses.map((course) => (
          <CourseCard key={course.id} course={course} />
        ))}
      </div>

      {showMoreButton && (
        <div className="text-center mt-6">
          <Button variant="outline" onClick={onShowMore} className="px-6">
            Show more
          </Button>
        </div>
      )}
    </div>
  );
}
