import React from 'react';

interface UserInfo {
  label: string;
  value: string;
}

export function InfoSection({
  title,
  data,
  isLast = false,
}: {
  title: string;
  data: UserInfo[];
  isLast?: boolean;
}) {
  return (
    <div className={`my-4 py-4 ${!isLast ? 'border-b border-[#646A77]' : ''}`}>
      <h3 className="text-[#111827] text-[18px] font-semibold leading-[28px] mb-2">
        {title}
      </h3>
      {data.map((item) => (
        <div key={item.label} className="flex justify-between mb-2">
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.label}
          </p>
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.value}
          </p>
        </div>
      ))}
    </div>
  );
}

function ProfessionalInformation() {
  return (
    <div className="py-4 px-4 mt-4 border border-[#D7DAED] rounded-md">
      <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-[#111827]">
        Professional Information
      </h4>
    </div>
  );
}

export default function ProfessionalInformationWrapper() {
  return <ProfessionalInformation />;
}
