# Career Navigator Citizen
## Description
Career Navigator Citizen is a modern web application built with **Next.js 15** using the **App Router**, with full **TypeScript** support for type safety and maintainability. The project leverages **Turbopack** for faster development builds and includes essential scripts for development, production, and linting.

## Requirements
- **Node.js**: `>=22.15.1`
- **npm**: `>=10.9.2`

## Installation
Before running any scripts, install dependencies using:
```sh
npm install
```
## Available Scripts
The following scripts are available in the `package.json` file:
### 1. Start Development Server
```sh
npm run dev
```
Runs the development server using Turbopack for faster builds. Access the application at `http://localhost:3000` by default.
### 2. Build the Application
```sh
npm run build
```
Generates an optimized production build of the application.
### 3. Start Production Server
```sh
npm run start
```
Starts the built application in production mode.
### 4. Lint the Codebase
```sh
npm run lint
```
Runs ESLint to check for and fix code style and syntax issues, ensuring adherence to TypeScript best practices.
## Folder Structure
```
/CareerNavigatorProBackendUI
│-- public/                  # Static assets (images, fonts, etc.)
│
│-- src/                     # Main source code for the application
│   ├── app/                 # Next.js App Router pages, layouts, and routing logic
│   ├── assets/              # Project assets like images, SVGs, icons, etc.
│   ├── components/          # Reusable UI components (e.g., buttons, modals)
│   ├── constants/           # Static values and configuration constants
│   ├── hooks/               # Custom React hooks for shared behavior
│   ├── interface/           # Global TypeScript interfaces and type definitions
│   ├── lib/                 # Utility libraries
│   ├── mutations/           # Functions for performing  REST mutations
│   ├── queries/             # Data-fetching functions (API calls)
│   ├── services/            # Reusable business logic, feature-specific logic
│   ├── types/               # Centralized TypeScript types for entities, DTOs, etc.
│   ├── zustand/             # Global state management store using Zustand
│   └── utils/               # Helper functions
│
│-- tailwind.config.ts      # Tailwind CSS configuration
│-- .prettierrc             # Prettier config for code formatting
│-- .eslint.config.mjs      # ESLint config with project-specific rules
│-- next.config.ts          # Next.js project configuration
│-- package.json            # Project dependencies and scripts
│-- README.md               # Project documentation
```
## Technologies Used
- **Next.js 15** (App Router)
- **Turbopack** (for fast builds)
- **ESLint** (for code linting)
- **TypeScript** (for type safety and maintainability)
## Getting Startedgit 
1. Clone the repository
2. Run `npm install`
3. Start the development server with `npm run dev`
## License
This project is licensed under [MIT License](LICENSE).