'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PaginationProps {
  pageCount: number;
  currentPage: number;
  onPageChange: (_page: number) => void;
  maxDisplayedPages?: number;
}

export function Pagination({
  pageCount,
  currentPage,
  onPageChange,
  maxDisplayedPages = 5,
}: PaginationProps) {
  if (pageCount <= 1) return null;

  let startPage = Math.max(1, currentPage - Math.floor(maxDisplayedPages / 2));
  const endPage = Math.min(pageCount, startPage + maxDisplayedPages - 1);

  if (endPage - startPage + 1 < maxDisplayedPages) {
    startPage = Math.max(1, endPage - maxDisplayedPages + 1);
  }

  const pageNumbers = Array.from(
    { length: endPage - startPage + 1 },
    (_, i) => startPage + i
  );

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        aria-label="Previous page"
        className="rounded-full"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {startPage > 1 && (
        <>
          <Button
            variant={currentPage === 1 ? 'default' : 'outline'}
            size="icon"
            onClick={() => onPageChange(1)}
            aria-label="Page 1"
            className="rounded-full"
          >
            1
          </Button>
          {startPage > 2 && <span className="mx-1">...</span>}
        </>
      )}

      {pageNumbers.map((page) => (
        <Button
          key={page}
          variant={currentPage === page ? 'default' : 'outline'}
          size="icon"
          onClick={() => onPageChange(page)}
          aria-label={`Page ${page}`}
          className="rounded-full"
        >
          {page}
        </Button>
      ))}

      {endPage < pageCount && (
        <>
          {endPage < pageCount - 1 && <span className="mx-1">...</span>}
          <Button
            variant={currentPage === pageCount ? 'default' : 'outline'}
            size="icon"
            onClick={() => onPageChange(pageCount)}
            aria-label={`Page ${pageCount}`}
            className="rounded-full"
          >
            {pageCount}
          </Button>
        </>
      )}

      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === pageCount}
        aria-label="Next page"
        className="rounded-full"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
