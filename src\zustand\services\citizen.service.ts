// services/partnerService.ts
import axiosClient from '@/utils/axiosClient';
import type { IUser } from '@/types';

const API_URL = '/users';

const citizenService = {
  registerCitizen: async (
    citizenData: Omit<IUser, 'userId'>
  ): Promise<IUser> => {
    try {
      const response = await axiosClient.put<IUser>(API_URL, citizenData);
      return response.data;
    } catch (error) {
      console.error('Error registering citizen', error);
      throw new Error('Failed to register citizen');
    }
  },

  getCitizenById: async (id: string): Promise<IUser> => {
    try {
      const response = await axiosClient.get<IUser>(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching citzen with id ${id}:`, error);
      throw new Error('Failed to fetch citizen');
    }
  },

  updateCitizen: async (
    id: string,
    updatedPartner: Partial<IUser>
  ): Promise<IUser> => {
    try {
      const response = await axiosClient.put<IUser>(
        `${API_URL}/${id}`,
        updatedPartner
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating citzen with id ${id}:`, error);
      throw new Error('Failed to update citzen');
    }
  },

  deleteCitzen: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`${API_URL}/${id}`);
    } catch (error) {
      console.error(`Error deleting citizen with id ${id}:`, error);
      throw new Error('Failed to delete citizen');
    }
  },

  getAllCitzens: async (): Promise<IUser[]> => {
    try {
      const response = await axiosClient.get<IUser[]>(API_URL);
      return response.data;
    } catch (error) {
      console.error('Error fetching all citzen', error);
      throw new Error('Failed to fetch citizen');
    }
  },
};

export default citizenService;
