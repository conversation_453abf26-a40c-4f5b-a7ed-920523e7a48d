import type { EnrollmentDetails } from '@/types';
import type {
  Course,
  CourseFilters,
  CourseListResponse,
  CourseRespones,
  EnrollmentFilters,
  EnrollmentListResponse,
} from '@/types/courseType';
import axiosClient from '@/utils/axiosClient';

export const getCourses = async (
  filters: CourseFilters = {}
): Promise<CourseListResponse> => {
  const params = new URLSearchParams();
  params.append('page', (filters.page || 1).toString());
  params.append('pageSize', (filters.pageSize || 10).toString());

  if (filters.search) params.append('search', filters.search);
  if (filters.mainTopic) params.append('mainTopic', filters.mainTopic);
  if (filters.deliveryMode) params.append('deliveryMode', filters.deliveryMode);
  if (filters.city) params.append('city', filters.city);
  if (filters.experienceLevel)
    params.append('experienceLevel', filters.experienceLevel);
  if (filters.startDate) params.append('startDate', filters.startDate);
  if (filters.endDate) params.append('endDate', filters.endDate);
  if (filters.status) params.append('status', filters.status);
  if (filters.sortField) params.append('sortField', filters.sortField);

  if (filters.courseFee !== undefined)
    params.append('courseFee', filters.courseFee.toString());

  if (filters.duration) params.append('duration', filters.duration);

  const response = await axiosClient.get<CourseListResponse>(
    `/courses?${params.toString()}`
  );
  return response.data;
};

export const updateCourse = async (
  id: string,
  courseData: Partial<Course>
): Promise<Course> => {
  const response = await axiosClient.put<Course>(`/courses/${id}`, courseData);
  return response.data;
};

export const deleteCourse = async (id: string): Promise<void> => {
  await axiosClient.delete(`/courses/${id}`);
};

export const getCourseById = async (id: string): Promise<CourseRespones> => {
  const response = await axiosClient.get<CourseRespones>(`/courses/${id}`);
  return response.data;
};

export const getCourseEnrollments = async (
  courseId: string,
  filters: EnrollmentFilters = {},
  page = 1,
  pageSize = 10
): Promise<EnrollmentListResponse> => {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  if (filters.search) params.append('search', filters.search);
  if (filters.status && filters.status !== 'All')
    params.append('status', filters.status);
  if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
  if (filters.dateTo) params.append('dateTo', filters.dateTo);
  if (filters.sortField) params.append('sortField', filters.sortField);

  const response = await axiosClient.get<EnrollmentListResponse>(
    `/courses/${courseId}/enrollments?${params.toString()}`
  );
  return response.data;
};

export const applyForCourse = async (courseId: string): Promise<void> => {
  await axiosClient.post(`/courses/${courseId}/apply`);
};

export const enrollInCourse = async (
  courseId: string,
  userId: string
): Promise<{ id: string }> => {
  const response = await axiosClient.post(`/courses/${courseId}/enroll`, {
    userId,
  });
  return response.data;
};

export const completeCourse = async (
  enrollmentId: string,
  userId: string
): Promise<void> => {
  await axiosClient.put(`/courses/${enrollmentId}/complete`, { userId });
};

export const getUserEnrollment = async (
  courseId: string
): Promise<{ id: string } | null> => {
  try {
    const response = await axiosClient.get(
      `/courses/${courseId}/enrollments?page=1&pageSize=1`
    );
    return response.data.items?.[0] || null;
  } catch (error) {
    console.error('Error fetching user enrollment:', error);
    return null;
  }
};

export const getRecommendedCourses = async (): Promise<CourseListResponse> => {
  const params = new URLSearchParams();

  const response = await axiosClient.get<CourseListResponse>(
    `/courses/recommended-for-you?${params.toString()}`
  );
  return response.data;
};

export const getRecentSearch = async (): Promise<{
  data: {
    recentSearches: { searchQuery: string }[];
    courses: Course[];
  };
}> => {
  const response = await axiosClient.get(`/courses/recent-search`);
  return response.data;
};

export const getPopularCourses = async (): Promise<CourseListResponse> => {
  const params = new URLSearchParams();

  const response = await axiosClient.get<CourseListResponse>(
    `/courses/most-popular?${params.toString()}`
  );
  return response.data;
};

export const checkEnrollment = async (
  courseId: string,
  userId: string
): Promise<EnrollmentDetails | null> => {
  try {
    const response = await axiosClient.get<{
      data: { enrollmentDetails: EnrollmentDetails };
    }>(`/courses/${courseId}/check-enrollment/${userId}`);
    return response.data.data.enrollmentDetails;
  } catch (error) {
    console.error('Error checking enrollment status:', error);
    return null;
  }
};
