import React from 'react';

function LitBulb() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 2C6.68629 2 4 4.68629 4 8C4 10.2187 5.20459 12.1555 6.99284 13.1931C7.49136 13.4823 7.80353 13.9261 7.81766 14.3728C7.82545 14.6191 7.9975 14.8297 8.23738 14.8864C8.49293 14.9468 8.75335 14.9943 9.01788 15.0283C9.25323 15.0585 9.45468 14.87 9.45468 14.6327V11.2429C9.22247 11.2172 8.99493 11.1753 8.77326 11.1182C8.48153 11.0431 8.3059 10.7458 8.38098 10.454C8.45607 10.1623 8.75344 9.98665 9.04518 10.0617C9.34977 10.1401 9.66964 10.182 10.0001 10.182C10.3306 10.182 10.6505 10.1401 10.9551 10.0617C11.2468 9.98665 11.5442 10.1623 11.6193 10.454C11.6944 10.7458 11.5187 11.0431 11.227 11.1182C11.0053 11.1753 10.7778 11.2172 10.5456 11.2429V14.6327C10.5456 14.87 10.747 15.0585 10.9824 15.0282C11.2468 14.9943 11.5072 14.9467 11.7626 14.8864C12.0025 14.8297 12.1746 14.6191 12.1823 14.3728C12.1965 13.9261 12.5086 13.4823 13.0072 13.1931C14.7954 12.1555 16 10.2187 16 8C16 4.68629 13.3137 2 10 2Z"
        fill="url(#paint0_linear_22646_44964)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.82774 15.927C7.88388 15.6311 8.16932 15.4366 8.46529 15.4928C8.962 15.587 9.47498 15.6364 10 15.6364C10.525 15.6364 11.038 15.587 11.5347 15.4928C11.8307 15.4366 12.1161 15.6311 12.1723 15.927C12.2284 16.223 12.034 16.5084 11.738 16.5646C11.1746 16.6714 10.5936 16.7273 10 16.7273C9.40642 16.7273 8.82537 16.6714 8.26198 16.5646C7.96601 16.5084 7.7716 16.223 7.82774 15.927Z"
        fill="url(#paint1_linear_22646_44964)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.3666 17.7047C8.39794 17.4051 8.66623 17.1876 8.96584 17.219C9.30554 17.2545 9.65055 17.2727 10 17.2727C10.3495 17.2727 10.6945 17.2545 11.0342 17.219C11.3338 17.1876 11.6021 17.4051 11.6334 17.7047C11.6647 18.0043 11.4473 18.2726 11.1477 18.3039C10.7703 18.3434 10.3874 18.3636 10 18.3636C9.61257 18.3636 9.22967 18.3434 8.85234 18.3039C8.55273 18.2726 8.33525 18.0043 8.3666 17.7047Z"
        fill="url(#paint2_linear_22646_44964)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_22646_44964"
          x1="4"
          y1="2"
          x2="19.6069"
          y2="13.4451"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF0016" />
          <stop offset="1" stop-color="#E68500" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_22646_44964"
          x1="4"
          y1="2"
          x2="19.6069"
          y2="13.4451"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF0016" />
          <stop offset="1" stop-color="#E68500" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_22646_44964"
          x1="4"
          y1="2"
          x2="19.6069"
          y2="13.4451"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF0016" />
          <stop offset="1" stop-color="#E68500" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default LitBulb;
