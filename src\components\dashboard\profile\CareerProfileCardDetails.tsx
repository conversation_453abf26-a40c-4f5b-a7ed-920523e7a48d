'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useCvs, useGetEnrollmentById } from '@/queries';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useUploadCvCustomMutation } from '@/mutations';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import DeleteCvModal from '../career-jobs/job-application/DeleteCvModal';

import { formatDateDDMonthYYYY } from '@/utils';
import UploadIcon from '@/components/icons/UploadSvg';
import SkillsProfile from './SkillProfile';
import BounceLoader from '@/components/common/BounceLoader';
import Image from 'next/image';
import { EyeIcon } from '@/assets/images/dashboard';
import CourseProfile from './CourseProfileDetails';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';

export function CareerProfileCard() {
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const userId = useAuthStore((state) => state.user?.id);
  const { data: myCvs, isLoading, refetch } = useCvs(userId as string);
  const { data: myCourses } = useGetEnrollmentById(userId as string);
  const { uploadCv } = useUploadCvCustomMutation();
  const { downloadFile } = useDownloadFile();

  React.useEffect(() => {
    const hasCvs = myCvs?.data?.length > 0;
    const hasCourses = myCourses?.length > 0;

    if (!hasCvs || !hasCourses) {
      setShowModal(true);
    } else {
      setShowModal(false);
    }
  }, [myCvs, myCourses]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setIsUploading(true);

    if (e.target.files && e.target.files.length > 0) {
      if (myCvs.data?.length >= 4) {
        setError(
          'You can only upload up to 4 files. Please delete an existing file first.'
        );
        setIsUploading(false);
        return;
      }

      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ];
      const selectedFiles = Array.from(e.target.files);
      const validFiles = selectedFiles.filter((file) =>
        allowedTypes.includes(file.type)
      );
      if (validFiles.length !== selectedFiles.length) {
        setError('Only .pdf and .docx files are allowed.');
        setIsUploading(false);
        return;
      }

      try {
        const newFiles = await Promise.all(
          validFiles.map(async (file) => ({
            id: crypto.randomUUID(),
            name: file.name,
            size: file.size,
            date: new Date(),
            content: await readFileAsBase64(file),
          }))
        );
        uploadCv({
          cv: {
            FileName: newFiles[0].name,
            Base64Content: newFiles[0].content,
          },
          userId: userId as string,
        });
        await refetch();
      } catch (error) {
        console.error('Error uploading file:', error);
        setError(
          'An error occurred while uploading the file. Please try again.'
        );
      }
    }

    setIsUploading(false);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024)
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const getBase64FileSize = (base64String: string): number => {
    const base64Data = base64String.split(',')[1] || base64String;
    return (base64Data.length * 3) / 4;
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  return (
    <>
      <Dialog open={showModal}>
        <DialogContent
          className="max-w-[600px]"
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold leading-[32px]">
              Complete Your Career Profile!
            </DialogTitle>
            <DialogDescription className="text-[18px]">
              Upload your CV to easily update your profile and receive smarter
              and more accurate job recommendations.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex items-end justify-end w-1/2 gap-1">
            <Button
              variant={'outline'}
              onClick={handleCloseModal}
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              variant={'destructive'}
              onClick={handleCloseModal}
              className="w-full"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <section className="bg-white mx-auto rounded-lg p-6 mt-10 h-full w-[824px]">
        <DynamicBreadcrumb
          customBreadcrumbs={[{ label: 'Profile', href: '/dashboard' }]}
        />
        <h6 className=" text-[24px] font-semibold leading-[32px] text-neutral-900">
          Career Profile
        </h6>
        <div className="flex flex-col">
          <div className="py-4 mt-4">
            <h2 className=" text-[20px] font-semibold leading-[20px] text-neutral-900 mb-4">
              My CV
            </h2>
            {!myCvs?.data?.length && (
              <p className="text-[16px] font-normal leading-[24px] text-neutral-500">
                Upload your CV to easily update your profile, extract key skills
                and receive smarter and more accurate job recommendations.
              </p>
            )}
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-destructive-500 rounded-md text-[16px]">
                {error}
              </div>
            )}

            <div className="space-y-3 mb-3">
              {isLoading && <BounceLoader />}
              {myCvs?.data &&
                myCvs?.data?.map(
                  (file: {
                    fileName: string;
                    base64Content: string;
                    createdOn: string;
                    id: string;
                  }) => {
                    const fileSizeBytes = getBase64FileSize(file.base64Content);
                    const formattedSize = formatFileSize(fileSizeBytes);
                    return (
                      <div
                        key={file.base64Content}
                        className="border rounded-lg p-3 flex items-center border-neutral-200"
                      >
                        <div className="flex-1">
                          <p className="text-[14px] text-neutral-900 font-semibold leading-[24px] ">
                            {file.fileName}
                          </p>

                          <div className="flex space-x-1 items-center text-neutral-500 text-[14px] leading-[28px]">
                            <p className="">{formattedSize}</p>
                            <div className="h-[3px] w-[3px] bg-neutral-500 rounded-full" />
                            <p className="">
                              {formatDateDDMonthYYYY(file.createdOn)}
                            </p>
                          </div>
                        </div>
                        <div></div>
                        <div className="flex space-x-1 items-center">
                          <Image
                            src={EyeIcon}
                            width={10}
                            height={10}
                            alt="trashIcon"
                            className="w-7 h-7"
                            onClick={(e) => {
                              e.stopPropagation();
                              downloadFile(file.base64Content, file.fileName);
                            }}
                          />

                          <DeleteCvModal id={file?.id} />
                        </div>
                      </div>
                    );
                  }
                )}
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              className="hidden"
              multiple
              accept=".pdf,.docx"
            />

            <Button
              className="flex items-center gap-2 px-[28px] py-[14px] h-[48px]"
              onClick={() => fileInputRef.current?.click()}
              disabled={myCvs?.data?.length >= 4 || isUploading}
            >
              {isUploading ? (
                'Uploading...'
              ) : (
                <>
                  <UploadIcon /> Upload a new CV
                </>
              )}
            </Button>

            {myCvs?.data?.length >= 4 && (
              <p className="mt-2 text-[16px] text-amber-600">
                Maximum file limit reached (4/4). Delete a file to upload a new
                one.
              </p>
            )}
          </div>

          <CourseProfile />
          <div className="mt-2">
            <SkillsProfile />
          </div>
        </div>
      </section>
    </>
  );
}
