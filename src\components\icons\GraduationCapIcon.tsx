import React from 'react';

interface GraduationCapProps {
  stroke?: string;
}

function GraduationCap({ stroke = '#4F778C' }: GraduationCapProps) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.9987 4L1.33203 12L6.66536 14.9067V22.9067L15.9987 28L25.332 22.9067V14.9067L27.9987 13.4533V22.6667H30.6654V12L15.9987 4ZM25.092 12L15.9987 16.96L6.90536 12L15.9987 7.04L25.092 12ZM22.6654 21.3333L15.9987 24.96L9.33203 21.3333V16.36L15.9987 20L22.6654 16.36V21.3333Z"
        fill={stroke}
      />
    </svg>
  );
}

export default GraduationCap;
