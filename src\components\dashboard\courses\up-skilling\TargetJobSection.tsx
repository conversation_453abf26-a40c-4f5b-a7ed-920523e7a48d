'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const jobOptions = [
  'Product Manager',
  'Software Engineer',
  'Data Scientist',
  'UX Designer',
  'Marketing Specialist',
];

export default function TargetJobSection() {
  const [selectedJob, setSelectedJob] = useState('Product Manager');

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedJob(e.target.value);
  };

  return (
    <div className="mb-6">
      <h2 className="text-xl font-semibold mb-6">My Upskilling Journey</h2>
      <h3 className="font-semibold text-[18px] leading-[28px] text-neutral-900 mb-2">
        What is your target job title?
      </h3>

      <div className="relative w-full">
        <select
          value={selectedJob}
          onChange={handleChange}
          className="appearance-none w-full border border-neutral-300 rounded-md px-3 py-2 pr-10 text-[18px] font-normal focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          {jobOptions.map((job) => (
            <option key={job} value={job}>
              {job}
            </option>
          ))}
        </select>
        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500 pointer-events-none" />
      </div>

      <p className="text-[18px] text-neutral-500 font-medium leading-[28px] mt-4">
        Become an exceptional <span className="font-bold">{selectedJob}</span>{' '}
        with our curated path of{' '}
        <span className="font-semibold">5 courses</span> across{' '}
        <span className="font-semibold">4 parts</span>. This tailored upskilling
        journey is designed to bridge the gap between the skills in your current
        profile and those required for success as a{' '}
        <span className="font-semibold">{selectedJob}</span>. By focusing on key
        areas such as user experience design, agile methodologies, and
        data-driven decision making, you&apos;ll gain the expertise needed to
        excel in this role.
      </p>
    </div>
  );
}
