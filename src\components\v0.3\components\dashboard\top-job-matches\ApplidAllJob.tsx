import Image from 'next/image';
import AppliedCheckList from '@/assets/images/dashboard/checklist.svg';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

const ApplidAllJob = () => {
  const router = useRouter();

  const handleBrowsMore = () => {
    router.push('/V03/dashboard/career-opportunities/all-jobs');
  };

  const handleBackToHome = () => {
    router.push('/V03/dashboard');
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white">
      <div className="flex flex-col gap-8 justify-center items-center p-8 bg-white max-w-[544px] rounded-[20px] border-[1px] border-neutral-200">
        <Image src={AppliedCheckList} alt="checklist" height={120} width={120} />
        <h3 className="font-[600] text-[24px] tracking-[-0.48px] text-center">
          That’s all for today’s smart suggestions!
        </h3>
        <Button className="w-full" onClick={handleBrowsMore}>
          Browse more job listings
        </Button>
        <Button variant={'outline'} className="w-full" onClick={handleBackToHome}>
          Go back home
        </Button>
      </div>
    </div>
  );
};

export default ApplidAllJob;
