'use client';

import Image from 'next/image';
import SignUpForm from '@/components/auth/Register';
import { Card, CardContent } from '@/components/ui/card';

const PageLogin = () => {
  return (
    <div className="relative h-screen w-full flex flex-row overflow-y-auto py-0">
      <Image
        src="/images/ws/login-bg.png"
        alt="Signup Background"
        fill
        priority
        className="object-cover object-center -z-10"
      />

      <div className="w-full lg:w-1/2 z-10 mx-auto flex justify-center items-center">
        <Card className="max-w-[600px] flex-1 rounded-2xl p-8 md:p-12 min-h-screen md:min-h-0">
          <CardContent className="p-0 pt-6">
            <SignUpForm />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PageLogin;
