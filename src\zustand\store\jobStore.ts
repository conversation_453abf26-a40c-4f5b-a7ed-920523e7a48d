import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { jobReducer } from '@/zustand/reducers/jobReducer';
import type { JobState } from '@/types/jobTypes';
import jobService from '../services/job.services';

const initialState: JobState = {
  jobs: [],
  currentJob: null,
  isLoading: false,
  page: 1,
  size: 10,
  total: 0,
  searchTerm: '',
  location: '',
  jobType: '',
  monthlyFrom: '',
  status: 'Active',
};

type JobStore = JobState & {
  // eslint-disable-next-line no-unused-vars
  fetchJobs: (params?: {
    page?: number;
    size?: number;
    search?: string;
    location?: string;
    jobType?: string;
    monthlyFrom?: string;
    status?: 'Active' | 'Closed';
  }) => Promise<void>;
  fetchJobsByUserId: (_userId: string) => Promise<void>;
  fetchJobById: (_id: string) => Promise<void>;
  setSearchTerm: (_searchTerm: string) => void;
  setLocation: (_location: string) => void;
  setJobType: (_jobType: string) => void;
  setMonthlyFrom: (_monthlyFrom: string) => void;
  setStatus: (_status: 'Active' | 'Closed') => void;
  resetSearch: () => void;
};

export const useJobStore = create<JobStore>()(
  persist(
    (set) => ({
      ...initialState,

      fetchJobs: async ({
        page = 1,
        size = 10,
        search = '',
        location = '',
        jobType = '',
        monthlyFrom = '',
        status = 'Active',
      } = {}) => {
        try {
          await jobService.getJobs({
            page,
            size,
            search,
            location,
            jobType,
            monthlyFrom,
            status,
          });
        } catch (error) {
          console.error('Failed to fetch jobs', error);
        }
      },

      fetchJobsByUserId: async (userId) => {
        try {
          set((state) => jobReducer(state, { type: 'START_LOADING' }));
          const jobs = await jobService.getJobsByUserId(userId);
          set((state) =>
            jobReducer(state, {
              type: 'SET_USER_JOBS',
              payload: jobs,
            })
          );
        } catch (error) {
          console.error('Failed to fetch jobs for user', error);
          set((state) => jobReducer(state, { type: 'STOP_LOADING' }));
        }
      },

      fetchJobById: async (id: string) => {
        try {
          set((state) => jobReducer(state, { type: 'START_LOADING' }));
          const job = await jobService.getJobById(id);
          set((state) =>
            jobReducer(state, { type: 'SET_CURRENT_JOB', payload: job })
          );
        } catch (error) {
          console.error('Failed to fetch job', error);
          set((state) => jobReducer(state, { type: 'STOP_LOADING' }));
        }
      },

      setSearchTerm: (searchTerm: string) => {
        set({ searchTerm });
      },

      setLocation: (location: string) => {
        set({ location });
      },

      setJobType: (jobType: string) => {
        set({ jobType });
      },

      setMonthlyFrom: (monthlyFrom: string) => {
        set({ monthlyFrom });
      },

      setStatus: (status: 'Active' | 'Closed') => {
        set({ status });
      },

      resetSearch: () => {
        set({
          searchTerm: '',
          location: '',
          jobType: '',
          monthlyFrom: '',
          status: 'Active',
        });
      },
    }),
    {
      name: 'job-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
