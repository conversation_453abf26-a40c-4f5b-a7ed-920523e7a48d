import React from 'react';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
}) => {
  return (
    <div className={`mb-4`}>
      <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
        {title}
      </h2>
      {subtitle && (
        <p className="text-neutral-700 text-[16px] font-normal">{subtitle}</p>
      )}
    </div>
  );
};
