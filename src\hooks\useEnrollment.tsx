import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  applyForCourse,
  enrollInCourse,
  completeCourse,
  getUserEnrollment,
  checkEnrollment,
} from '@/zustand/services/course.services';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import toast from 'react-hot-toast';
import { useEnrollmentStore } from '@/zustand/store/enrollmentStore';

export function useEnrollmentActions(courseId: string) {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { addEnrollment, getEnrollment, updateEnrollmentStatus } =
    useEnrollmentStore();

  const { data: enrollment, isLoading: isLoadingEnrollment } = useQuery({
    queryKey: ['enrollment', courseId],
    queryFn: async () => {
      const storedEnrollment = getEnrollment(courseId);
      if (storedEnrollment) return storedEnrollment;

      try {
        if (user?.id) {
          const enrollmentDetails = await checkEnrollment(courseId, user.id);

          if (enrollmentDetails) {
            addEnrollment({
              id: enrollmentDetails.id,
              courseId,
              userId: user.id,
              userName: enrollmentDetails.userName,
              email: enrollmentDetails.email,
              enrollmentDate: enrollmentDetails.enrollmentDate,
              status: enrollmentDetails.isCompleted ? 'Completed' : 'Enrolled',
            });

            return {
              id: enrollmentDetails.id,
              courseId,
              status: enrollmentDetails.isCompleted ? 'Completed' : 'Enrolled',
            };
          }
        }

        const apiEnrollment = await getUserEnrollment(courseId);
        if (apiEnrollment?.id) {
          addEnrollment({
            id: apiEnrollment.id,
            courseId,
            userId: user?.id || '',
            userName: user?.fullName || '',
            email: user?.email || '',
            enrollmentDate: new Date().toISOString(),
            status: 'Enrolled',
          });
          return {
            id: apiEnrollment.id,
            courseId,
            status: 'Enrolled',
          };
        }
        return null;
      } catch (error) {
        console.error('Error fetching enrollment:', error);
        return null;
      }
    },
    enabled: !!courseId && !!user?.id,
  });

  const applyMutation = useMutation({
    mutationFn: () => applyForCourse(courseId),
    onSuccess: () => {
      toast.success('Application submitted successfully!');
    },
    onError: (error) => {
      console.error('Application error:', error);
      toast.error('Failed to submit application');
    },
  });

  const enrollMutation = useMutation({
    mutationFn: () => {
      if (!user?.id) {
        throw new Error('You need to be logged in to enroll');
      }
      return enrollInCourse(courseId, user.id);
    },
    onSuccess: (data) => {
      addEnrollment({
        id: data.id,
        courseId,
        userId: user?.id || '',
        userName: user?.fullName || '',
        email: user?.email || '',
        enrollmentDate: new Date().toISOString(),
        status: 'Enrolled',
      });

      toast.success('Enrollment successful!');
      queryClient.invalidateQueries({ queryKey: ['enrollment', courseId] });
    },
    onError: (error) => {
      console.error('Enrollment error:', error);
      toast.error('Failed to enroll in course');
    },
  });

  // Complete course mutation
  const completeMutation = useMutation({
    mutationFn: async () => {
      if (!courseId) {
        throw new Error('Enrollment ID is missing.');
      }
      if (!user?.id) {
        throw new Error('You need to be logged in to complete the course');
      }

      return completeCourse(courseId, user.id);
    },
    onSuccess: () => {
      if (enrollment?.id) {
        updateEnrollmentStatus(enrollment.id, 'Completed');
      }

      toast.success('Course marked as completed!');
      queryClient.invalidateQueries({ queryKey: ['enrollment', courseId] });
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : 'Failed to complete course'
      );
    },
  });

  return {
    enrollment,
    isLoadingEnrollment,
    applyForCourse: applyMutation.mutate,
    isApplying: applyMutation.isPending,
    enrollInCourse: enrollMutation.mutate,
    isEnrolling: enrollMutation.isPending,
    completeCourse: completeMutation.mutate,
    isCompleting: completeMutation.isPending,
  };
}
