import { create } from 'zustand';
import applicantService from '@/zustand/services/settings.services';
import type { ISettings } from '@/types';
import { defaultAppearanceSettings } from '@/constants';
import { updateAppColors } from '@/utils/helper';

type DefaultSettings = {
  isDefault: true;
} & Partial<ISettings>;

type CustomSettings = {
  isDefault: false;
} & Required<ISettings>;

interface SettingsState {
  appearanceSettings: ISettings;
  isLoading: boolean | string;
  getSettings: () => Promise<void>;
  updateSettings: (
    _id: string,
    _settings: DefaultSettings | CustomSettings
  ) => Promise<void>;
}

const useSettingsStore = create<SettingsState>((set) => ({
  appearanceSettings: defaultAppearanceSettings,
  isLoading: false,

  getSettings: async () => {
    try {
      const response = await applicantService.getSettings();
      set({ appearanceSettings: response });
      updateAppColors([
        { brandColor: response.brandColor },
        { topBarBgColor: response.topBarBgColor },
        { topBarButtonColor: response.topBarButtonColor },
        { navMenuColor: response.navMenuColor },
        { buttonColor: response.buttonColor },
        { buttonStyle: response.buttonStyle },
        { headingFont: response.headingFont },
        { bodyTextFont: response.bodyTextFont },
        { headingTextColor: response.headingTextColor },
        { bodyTextColor: response.bodyTextColor },
      ]);
    } catch (error) {
      console.error('Failed to get settings', error);
    }
  },

  updateSettings: async (
    id: string,
    settings: DefaultSettings | CustomSettings
  ) => {
    try {
      set({ isLoading: settings.isDefault ? 'default' : 'custome' });
      const response = await applicantService.updateSettings(id, settings);
      updateAppColors([
        { brandColor: response.brandColor },
        { topBarBgColor: response.topBarBgColor },
        { topBarButtonColor: response.topBarButtonColor },
        { navMenuColor: response.navMenuColor },
        { buttonColor: response.buttonColor },
        { buttonStyle: response.buttonStyle },
        { headingFont: response.headingFont },
        { bodyTextFont: response.bodyTextFont },
        { headingTextColor: response.headingTextColor },
        { bodyTextColor: response.bodyTextColor },
      ]);
      set({ appearanceSettings: response, isLoading: false });
    } catch (error) {
      set({ isLoading: false });
      console.error('Failed to update applicant status', error);
    }
  },
}));

export default useSettingsStore;
