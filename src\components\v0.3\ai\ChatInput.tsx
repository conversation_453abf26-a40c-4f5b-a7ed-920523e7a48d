import { useState } from 'react';
import { Paperclip } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import AIAssistantIcon from '@/assets/images/ws/assistant.svg';
import { Textarea } from '@/components/ui/textarea';
import { SendIcon } from '@/components/icons/SendSvg';

interface ChatInputProps {
  onSendMessage: (_message: string) => void;
}

export const ChatInput = ({ onSendMessage }: ChatInputProps) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage(message);
      setMessage('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="pt-2">
      <div className="border border-gray-200 rounded-xl items-center h-[160px] p-4 py-0">
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Enter your message..."
          className={cn(
            'flex-1 resize-none border-none focus:ring-0 focus:outline-none text-[20px] px-0 placeholder:text-neutral-400 bg-transparent font-normal leading-[32px]'
          )}
          rows={3}
        />

        <div className="flex justify-between">
          <button className="text-neutral-400 border-none" onClick={() => {}}>
            <Paperclip className="w-6 h-6 text-[--buttonColor]" />
          </button>
          <button type="submit" className="">
            <SendIcon className="text-[--buttonColor]" />
          </button>
        </div>
      </div>
    </form>
  );
};

export const CloseChatInput = ({ onSendMessage }: ChatInputProps) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage(message);
      setMessage('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="">
      <div className="flex items-center">
        <div>
          <Image
            className="w-32 h-32 items-center relative bottom-2"
            src={AIAssistantIcon}
            alt="assistantIcon"
          />
        </div>
        <div className=" items-center w-full">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask e-Firstname anything"
            className={cn(
              'flex-1 resize-none border-none focus:ring-0 focus:outline-none text-[20px] mt-2 placeholder:text-[20px] px-0 placeholder:text-neutral-[#374151] bg-transparent font-normal leading-[32px]'
            )}
            rows={3}
          />

          <div className="flex justify-between relative bottom-10">
            <button className="text-neutral-400 border-none" onClick={() => {}}>
              <Paperclip className="w-6 h-6 text-[--buttonColor]" />
            </button>
            <button type="submit" className="">
              <SendIcon />
            </button>
          </div>
        </div>
      </div>
    </form>
  );
};
