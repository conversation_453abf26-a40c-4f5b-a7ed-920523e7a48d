'use client';

import { useJobStore } from '@/zustand/store/jobStore';
import { useEffect, useState } from 'react';
import type { Job } from '@/types/jobTypes';
import JobSearchInput from './JobSearchInput';
import { LoadingState } from '@/components/common/LoaderState';
import JobList from './JobLists';
import { Briefcase } from 'lucide-react';
import { PaginationControls } from '@/components/common/PaginationController';
import { Card } from '@/components/ui/card';
import JobDetails from './JobDetails';
import { useFetchJobs } from '@/queries';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useAppliedJobStore } from '@/zustand/store/appliedStore';

const V03JobCard: React.FC = () => {
  const {
    page,
    size,
    searchTerm,
    location,
    jobType,
    monthlyFrom,
    status,
    total,
    setSearchTerm,
    setLocation,
    setJobType,
    setMonthlyFrom,
    setStatus,
  } = useJobStore();

  useEffect(() => {
    const storedData = localStorage.getItem('job-storage');
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      if (parsedData.state) {
        const { searchTerm, location, jobType, monthlyFrom, status } =
          parsedData.state;
        setSearchTerm(searchTerm);
        setLocation(location);
        setJobType(jobType);
        setMonthlyFrom(monthlyFrom);
        setStatus(status);
      }
    }
  }, [setSearchTerm, setLocation, setJobType, setMonthlyFrom, setStatus]);

  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const { fetchJobs: fetchAppliedJobs } = useAppliedJobStore();
  const { user } = useAuthStore();

  const totalPages = Math.max(1, Math.ceil(total / size));
  const { data, isLoading } = useFetchJobs({
    page,
    size,
    search: searchTerm,
    location,
    jobType,
    monthlyFrom,
    status,
  });

  useEffect(() => {
    if (user?.id) {
      fetchAppliedJobs({ userId: user.id });
    }
  }, [user, fetchAppliedJobs]);

  useEffect(() => {
    if (data) {
      const activeJobs = data.data.filter((job) => job.status === 'Active');
      setFilteredJobs(activeJobs);

      if (data.total !== undefined && data.total !== null) {
        useJobStore.setState({ total: data.total });
      }
    }
  }, [data]);

  const handleJobSelect = (job: Job) => setSelectedJob(job);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      useJobStore.setState({ page: newPage });
    }
  };

  return (
    <div className="w-full">
      <div>
        <JobSearchInput setSelectedJob={setSelectedJob} />
        {isLoading ? (
          <div className="w-full">
            <LoadingState />
          </div>
        ) : (
          <div className="flex flex-col md:flex-row h-full w-full gap-4 py-10">
            <div className="w-full md:w-[40%]">
              <div className="max-h-[920px] overflow-y-auto rounded-lg">
                <div className="flex flex-col space-y-4">
                  {filteredJobs.length > 0 ? (
                    <JobList
                      jobs={filteredJobs}
                      selectedJobId={selectedJob?.id}
                      onJobSelect={handleJobSelect}
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-center">
                      <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
                      <p className="text-neutral-500">No jobs found</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-10">
                {filteredJobs.length > 0 && (
                  <PaginationControls
                    page={page}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                  />
                )}
              </div>
            </div>

            <div className="w-full md:w-[60%]">
              <Card className="p-6 h-full rounded-lg">
                <JobDetails selectedJob={selectedJob} />
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default V03JobCard;
