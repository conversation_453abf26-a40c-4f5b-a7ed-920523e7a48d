'use client';
import React from 'react';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import AppliedJobs from '@/components/dashboard/career-jobs/applied-card';

function Index() {
  return (
    <section className="">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Career Opportunities',
            href: '/dashboard/career-opportunities/jobs',
          },
          {
            label: 'Applied Jobs',
            href: '/dashboard/career-opportunities/applied-jobs',
          },
        ]}
      />

      <div className="justify-center text-center mt-16">
        <h3 className=" text-neutral-900 text-[28px] font-semibold leading-[-0.56px]">
          Applied Jobs
        </h3>

        <p className=" text-[18px] font-medium leading-[28px] text-neutral-500">
          Track the jobs that you have applied for on this page
        </p>
      </div>
      <div>
        <AppliedJobs />
      </div>
    </section>
  );
}

export default Index;
