import CaretDownRedIcon from '@/components/icons/CaretDownRed';
import CaretUpGreenIcon from '@/components/icons/CaretUpGreen';
import Link from 'next/link';
import React from 'react';

interface JobCardProps {
  title: string;
  description: string;
  salary: number;
  jobListings: number;
  growthPercentage: number;
}

const ExploreOccupationsJobCategoryCard: React.FC<JobCardProps> = ({
  title,
  description,
  salary,
  jobListings,
  growthPercentage,
}) => {
  return (
    <div className="w-full border-t border-lightGray-50 p-4">
      <Link href={`job-trends/${12345}`}>
        <h2 className="text-lg font-bold text-blue-400 underline">{title}</h2>
      </Link>

      <p className="text-[16px] font-normal text-gray-20">{description}</p>

      <p className="font-normal mt-4 text-[#6C757D] text-[16px]">
        <span className="text-gray-20 text-base font-semibold">
          Estimated Salary:
        </span>{' '}
        ${salary} Monthly
      </p>

      <p className="font-normal text-[#6C757D] text-[16px] flex items-center">
        <span className="text-gray-20 text-base font-semibold mr-2">
          Job Listings in the past Quarter:
        </span>{' '}
        {jobListings}
        <span
          className={`ml-1 font-normal text-[16px] flex items-center ${
            growthPercentage >= 0 ? 'text-green-400' : 'text-red-400'
          }`}
        >
          {growthPercentage >= 0 ? <CaretUpGreenIcon /> : <CaretDownRedIcon />}{' '}
          {growthPercentage}% growth
        </span>
      </p>
    </div>
  );
};

export default ExploreOccupationsJobCategoryCard;
