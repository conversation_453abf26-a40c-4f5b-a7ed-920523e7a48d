'use client';

import { useCareerData } from '@/constants/V0.3DashboardCardData';
import { CareerCard } from './v0.3DashboardCard';
import { useAuthStore } from '@/zustand/store/useAuthStore';

export function CareerDashboard() {
  const user = useAuthStore((state) => state.user);
  const nameParts = user?.fullName?.trim().split(' ') || [];
  const firstName = nameParts[0] || '';
  const careerData = useCareerData();

  return (
    <div className="mx-auto mt-10">
      <div className="mb-8">
        <h1 className="text-[32px] font-semibold text-neutral-900 dark:text-white flex items-center gap-2">
          <span className="text-[32px]">👋</span> Welcome {firstName}!
        </h1>
        <p className="text-neutral-500 text-[18px] font-medium leading-[28px] dark:text-gray-300 mt-2">
          Here&apos;s how to move closer to your career goals today.
        </p>
      </div>

      <div className="space-y-6">
        {careerData.map((cardData) => (
          <CareerCard key={cardData.id} data={cardData} />
        ))}
      </div>
    </div>
  );
}
