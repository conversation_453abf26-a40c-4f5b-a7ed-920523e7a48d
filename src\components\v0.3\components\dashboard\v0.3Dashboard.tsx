'use client';

import { useCareerData } from '@/constants/V0.3DashboardCardData';
import { CareerCard } from './v0.3DashboardCard';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

interface CareerDashboardProps {
  onSwitchPersona?: () => void;
}

export function CareerDashboard({ onSwitchPersona }: CareerDashboardProps) {
  const user = useAuthStore((state) => state.user);
  const selectedPersona = useSelectedPersona();
  const nameParts = user?.fullName?.trim().split(' ') || [];
  const firstName = nameParts[0] || '';
  const careerData = useCareerData();

  return (
    <div className="mx-auto mt-10">
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-[32px] font-semibold text-neutral-900 dark:text-white flex items-center gap-2">
              <span className="text-[32px]">👋</span> Welcome {firstName}!
            </h1>
            <p className="text-neutral-500 text-[18px] font-medium leading-[28px] dark:text-gray-300 mt-2">
              Here&apos;s how to move closer to your career goals today.
            </p>
          </div>

          {/* Current Persona and Switch Button */}
          {selectedPersona && onSwitchPersona && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="flex-1">
                <div className="text-sm text-neutral-500 mb-1">Current Persona</div>
                <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                  <span className="text-2xl">{selectedPersona.avatar}</span>
                  <div className="flex-1">
                    <div className="text-lg font-semibold text-neutral-900">
                      {selectedPersona.name}
                    </div>
                    <div className="text-xs text-neutral-500 line-clamp-1">
                      {selectedPersona.description}
                    </div>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onSwitchPersona}
                className="flex items-center gap-2 border-neutral-300 hover:border-primary-500 hover:text-primary-500 transition-all duration-200 whitespace-nowrap"
              >
                <RefreshCw className="w-4 h-4" />
                Switch Persona
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {careerData.map((cardData) => (
          <CareerCard key={cardData.id} data={cardData} />
        ))}
      </div>
    </div>
  );
}
