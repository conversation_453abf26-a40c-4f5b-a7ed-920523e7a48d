'use client';

import React from 'react';
import {
  Breadcrumb,
  BreadcrumbElli<PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Home, Slash } from 'lucide-react';
import Image from 'next/image';
import { BreadCrumbIcon } from '@/assets';

interface CustomBreadcrumb {
  label: string;
  href: string;
}

interface DynamicBreadcrumbProps {
  customBreadcrumbs?: CustomBreadcrumb[]; // Custom breadcrumbs to replace the default ones
}

const DynamicBreadcrumb: React.FC<DynamicBreadcrumbProps> = ({
  customBreadcrumbs,
}) => {
  const pathname = usePathname();
  const pathSegments = pathname.split('/').filter(Boolean);

  // If customBreadcrumbs is provided, use it and ignore default breadcrumbs
  if (customBreadcrumbs && customBreadcrumbs.length > 0) {
    return (
      <Breadcrumb className="mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">
              <Home className="w-6 h-6" />
            </BreadcrumbLink>
          </BreadcrumbItem>

          {customBreadcrumbs.map((item, index) => (
            <React.Fragment key={item.href}>
              <BreadcrumbSeparator>
                <Slash />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {index === customBreadcrumbs.length - 1 ? (
                  <BreadcrumbPage className="text-[16px] font-normal text-primary-500 leading-[24px]">
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink
                    href={item.href}
                    className="text-[16px] font-normal text-neutral-700 leading-[24px]"
                  >
                    {item.label}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Default Breadcrumb Logic
  const hasDropdown = pathSegments.length > 3;
  const dropdownItems = pathSegments.slice(1, pathSegments.length - 1);

  return (
    <Breadcrumb className="mb-2">
      <BreadcrumbList>
        {/* Home Link */}
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard">
            <Image src={BreadCrumbIcon} className="w-6 h-6" alt="" />
          </BreadcrumbLink>
        </BreadcrumbItem>

        {pathSegments.length > 0 && (
          <BreadcrumbSeparator>
            <Slash />
          </BreadcrumbSeparator>
        )}

        {/* Dropdown for long paths */}
        {hasDropdown && (
          <>
            <BreadcrumbItem>
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-1">
                  <BreadcrumbEllipsis className="h-4 w-4" />
                  <span className="sr-only">View All</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {dropdownItems.map((segment: string, index: number) => {
                    const path =
                      `/${pathSegments.slice(0, index + 2).join('/')}`;
                    return (
                      <DropdownMenuItem key={path}>
                        <Link
                          href={path}
                          className="text-primary-500 hover:bg-primary-600"
                        >
                          {segment.replace(/-/g, ' ')}
                        </Link>
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </>
        )}

        {/* Default Breadcrumb Links */}
        {pathSegments.map((segment, index) => {
          const path = `/${pathSegments.slice(0, index + 1).join('/')}`;
          const isLast = index === pathSegments.length - 1;

          return (
            <React.Fragment key={path}>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage className="">
                    {segment.replace(/-/g, ' ')}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={path} className="">
                    {segment.replace(/-/g, ' ')}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLast && (
                <BreadcrumbSeparator>
                  <Slash />
                </BreadcrumbSeparator>
              )}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default DynamicBreadcrumb;
