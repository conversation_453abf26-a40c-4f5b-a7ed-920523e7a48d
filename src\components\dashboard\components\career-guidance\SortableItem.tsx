'use client';

import type React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Text from '@/components/ui/text';
import type { Course } from '@/types';

interface SortableCourseItemProps {
  id: string;
  course: Course;
  index: number;
  isLoading: boolean;
  onClick: (_event: React.MouseEvent<HTMLDivElement>) => void;
}

export function SortableCourseItem({
  id,
  course,
  index,
  isLoading,
  onClick,
}: SortableCourseItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.4 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          const syntheticMouseEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
          });
          e.currentTarget.dispatchEvent(syntheticMouseEvent);
        }
      }}
      tabIndex={0}
      role="button"
      className={`p-3 ${index % 2 === 0 ? 'bg-white' : 'bg-neutral-50'} rounded-lg hover:bg-neutral-100 cursor-pointer ${
        isLoading ? 'pointer-events-none opacity-50' : ''
      }`}
    >
      <div className="flex w-full items-center">
        <div className="w-[26px] min-w-[26px]">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M9.5 5C9.5 5.53043 9.28929 6.03914 8.91421 6.41421C8.53914 6.78929 8.03043 7 7.5 7C6.96957 7 6.46086 6.78929 6.08579 6.41421C5.71071 6.03914 5.5 5.53043 5.5 5C5.5 4.46957 5.71071 3.96086 6.08579 3.58579C6.46086 3.21071 6.96957 3 7.5 3C8.03043 3 8.53914 3.21071 8.91421 3.58579C9.28929 3.96086 9.5 4.46957 9.5 5ZM7.5 14C8.03043 14 8.53914 13.7893 8.91421 13.4142C9.28929 13.0391 9.5 12.5304 9.5 12C9.5 11.4696 9.28929 10.9609 8.91421 10.5858C8.53914 10.2107 8.03043 10 7.5 10C6.96957 10 6.46086 10.2107 6.08579 10.5858C5.71071 10.9609 5.5 11.4696 5.5 12C5.5 12.5304 5.71071 13.0391 6.08579 13.4142C6.46086 13.7893 6.96957 14 7.5 14ZM7.5 21C8.03043 21 8.53914 20.7893 8.91421 20.4142C9.28929 20.0391 9.5 19.5304 9.5 19C9.5 18.4696 9.28929 17.9609 8.91421 17.5858C8.53914 17.2107 8.03043 17 7.5 17C6.96957 17 6.46086 17.2107 6.08579 17.5858C5.71071 17.9609 5.5 18.4696 5.5 19C5.5 19.5304 5.71071 20.0391 6.08579 20.4142C6.46086 20.7893 6.96957 21 7.5 21Z"
              fill="#ADB5BD"
            />
          </svg>
        </div>
        <Text className="text-neutral-600 mx-2 w-[80px] min-w-[80px] text-nowrap">
          {course.code}
        </Text>
        <Text className="text-neutral-600 mr-3 w-full">{course.name}</Text>
        <Text className="hidden w-[80px] min-w-[80px] text-neutral-600 md:block">
          {course.credits}
        </Text>
        <Text className="hidden w-[60px] min-w-[60px] text-neutral-600 md:block">
          {
            (course.escoSkills || '').split(';').filter((skill) => skill !== '')
              .length
          }
        </Text>
        <div className="w-[26px] min-w-[26px]">
          <svg className="fill-neutral-500" width="24" height="24">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M8.51192 4.43063C8.82641 4.16107 9.29989 4.19749 9.56946 4.51198L15.5695 11.512C15.8102 11.7928 15.8102 12.2073 15.5695 12.4882L9.56946 19.4882C9.29989 19.8027 8.82641 19.8391 8.51192 19.5695C8.19743 19.2999 8.161 18.8265 8.43057 18.512L14.0122 12.0001L8.43057 5.48817C8.161 5.17367 8.19743 4.7002 8.51192 4.43063Z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}
