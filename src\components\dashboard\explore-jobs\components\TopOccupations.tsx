import CaretDownRedIcon from '@/components/icons/CaretDownRed';
import CaretUpGreenIcon from '@/components/icons/CaretUpGreen';
import EqualToIcon from '@/components/icons/EqualToIcon';
import PlusIcon from '@/components/icons/PlusSvg';
import { occupationsTrendStaticData } from '@/constants';
import React from 'react';

function TopOccupations() {
  return (
    <div className="bg-white rounded-lg p-5 space-y-4">
      <h4 className="text-blue-400 text-xl font-semibold">
        Top Educational Specializations (in demand last 3 years)
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {occupationsTrendStaticData.top_occupations.map((yearData) => (
          <div key={yearData.year} className="mb-6">
            <div
              className={`${yearData.data[0] && 'change' in yearData.data[0] ? 'bg-primary-500 text-white' : 'bg-lightGray-50 text-blue-400'} w-full p-2 rounded flex justify-center items-center font-semibold text-lg`}
            >
              {yearData.year}
            </div>

            <ul className="mt-4 space-y-2">
              {yearData.data.map((occupation) => (
                <li
                  key={occupation.rank}
                  className="flex items-center space-x-2 p-2 border-b"
                >
                  {'change' in occupation && occupation.change && (
                    <span
                      className={
                        occupation.change === 'up'
                          ? 'text-green-500'
                          : occupation.change === 'down'
                            ? 'text-red-500'
                            : occupation.change === 'same'
                              ? 'text-gray-500'
                              : occupation.change === 'new'
                                ? 'text-blue-500'
                                : ''
                      }
                    >
                      {occupation.change === 'up' ? (
                        <CaretUpGreenIcon />
                      ) : occupation.change === 'down' ? (
                        <CaretDownRedIcon />
                      ) : occupation.change === 'same' ? (
                        <EqualToIcon />
                      ) : occupation.change === 'new' ? (
                        <PlusIcon />
                      ) : null}
                    </span>
                  )}
                  <span className="bg-lightGray-50 text-blue-400 px-2 py-1 rounded text-[16px] font-semibold">
                    {occupation.rank}
                  </span>
                  <span className="text-[16px] font-normal text-gray-20">
                    {occupation.title}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      <div className="py-2 w-full">
        <div className="w-fit mx-auto flex space-x-3">
          <div className="flex items-center">
            <CaretUpGreenIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              up
            </h6>
          </div>
          <div className="flex items-center">
            <CaretDownRedIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              Down
            </h6>
          </div>
          <div className="flex items-center">
            <EqualToIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              Same
            </h6>
          </div>
          <div className="flex items-center">
            <PlusIcon />
            <h6 className="text-blue-400 rounded text-[16px] font-semibold">
              New
            </h6>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TopOccupations;
