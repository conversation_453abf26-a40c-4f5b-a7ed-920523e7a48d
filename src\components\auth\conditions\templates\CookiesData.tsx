import { TrackingTechnologiesCookies } from '@/constants/PRIVACY_DEFINITION';
import React from 'react';

function CookiesData() {
  return (
    <div>
      <h5 className="text-blue-300 text-[16px] font-medium mt-4 italic">
        Tracking Technologies and Cookies
      </h5>

      <div>
        {TrackingTechnologiesCookies.map((item) => (
          <div key={item.definition} className="mb-6">
            <p className="text-[16px] font-medium text-neutral-900 mt-2">
              {item.definition}
            </p>

            {item.bulletPoints && (
              <ul className="mt-2 list-disc list-inside ml-10 text-[16px]  flex flex-col gap-3">
                {item.bulletPoints.map((point) => (
                  <li key={point.title} className="text-neutral-800">
                    <strong>{point.title}: </strong> {point.description}
                  </li>
                ))}
              </ul>
            )}

            {item.description && (
              <p className="mt-2 text-neutral-700">{item.description}</p>
            )}

            <p className="mt-4 text-neutral-900 text-[16px]">
              {' '}
              We use both Session and Persistent Cookies for the purposes set
              out below:
            </p>

            <div className="mt-4 p-4 ml-10">
              <h4 className="">
                {item.cookiesSessionPurpose && (
                  <ul>
                    {item.cookiesSessionPurpose.map((sessionPurpose) => (
                      <li key={sessionPurpose.title} className="text-neutral-900">
                        <p className="text-[16px] font-semibold">
                          {' '}
                          {sessionPurpose.title}
                        </p>
                        <p> Type:{sessionPurpose.type}</p>
                        <p> Administered by: {sessionPurpose.AdministeredBy}</p>
                        <p> Purpose: {sessionPurpose.purpose}</p>
                      </li>
                    ))}
                  </ul>
                )}
              </h4>
            </div>

            <p className="mt-4 text-neutral-900 text-[16px]">
              {' '}
              For more information about the cookies we use and your choices
              regarding cookies, please visit our Cookies Policy or the Cookies
              section of our Privacy Policy.
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default CookiesData;
