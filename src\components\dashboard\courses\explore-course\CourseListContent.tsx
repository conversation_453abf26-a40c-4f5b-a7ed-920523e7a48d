/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { PaginationControls } from '@/components/common/PaginationController';
import { Skeleton } from '@/components/ui/skeleton';
import CourseCard from '../CourseCard';

interface CourseListContentProps {
  courses: any[];
  isLoading: boolean;
  page: number;
  totalPages: number;
  totalCourses: number;
  pageSize: number;
  handlePageChange: (_page: number) => void;
}

export function CourseListContent({
  courses,
  isLoading,
  page,
  totalPages,
  totalCourses,
  pageSize,
  handlePageChange,
}: CourseListContentProps) {
  const showingCount = Math.min(page * pageSize, totalCourses);
  const startingCount = totalCourses > 0 ? (page - 1) * pageSize + 1 : 0;

  return (
    <Card className="p-4 h-fit rounded-lg">
      <div className="mb-6">
        <p className="text-[16px] w-[800px] text-muted-foreground">
          {totalCourses > 0
            ? `Showing ${startingCount} - ${showingCount} of ${totalCourses} courses`
            : isLoading
              ? 'Loading courses...'
              : 'No courses found'}
        </p>
      </div>

      {isLoading && courses.length === 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array.from({ length: pageSize }).map((_, i) => (
            <div
              // eslint-disable-next-line react/no-array-index-key
              key={i}
              className="h-80 bg-gray-100 rounded-lg flex items-center justify-center"
            >
              <Loader2 className="h-8 w-8 animate-spin text-primary-500" />
            </div>
          ))}
        </div>
      ) : courses.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {courses.map((course) => (
              <CourseCard key={`${course.id}-${page}`} course={course} />
            ))}
          </div>

          <div className="mt-10">
            <PaginationControls
              page={page}
              totalPages={totalPages}
              handlePageChange={handlePageChange}
            />
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium">No courses found</h3>
          <p className="text-muted-foreground mt-1">
            Try adjusting your filters to find what you&apos;re looking for.
          </p>
        </div>
      )}
    </Card>
  );
}

export function CourseListSkeleton({ pageSize }: { pageSize: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {Array.from({ length: pageSize }).map((_, i) => (
        // eslint-disable-next-line react/no-array-index-key
        <Skeleton key={i} className="h-80 w-full rounded-lg" />
      ))}
    </div>
  );
}
