import {
  AnalyticsData,
  DetailedInfoPersonalData,
  EmailMarketing,
  OtherLegalRequirements,
  OtherLegalRequirementsBulletPoints,
  SecurityPersonalData,
} from '@/constants/PRIVACY_DEFINITION';
import Link from 'next/link';
import React from 'react';

function Details() {
  return (
    <>
      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4 italic">
          Other legal requirements
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {OtherLegalRequirements.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
            </li>
          ))}
        </ul>

        <ul className="mt-2 list-disc list-inside text-[16px]  flex flex-col gap-3">
          {OtherLegalRequirementsBulletPoints.map((point) => (
            <li key={point.def} className="text-neutral-900">
              {point.def}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Security of Your Personal Data
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {SecurityPersonalData.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Detailed Information on the Processing of Your Personal Data
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {DetailedInfoPersonalData.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Detailed Information on the Processing of Your Personal Data
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {AnalyticsData.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col gap-4"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>{' '}
              <span className="font-normal text-[16px] text-neutral-900 ml-10 mt-4">
                <p className=" text-[16px] font-semibold  text-neutral-900">
                  {' '}
                  Google Analytics{' '}
                </p>
                {item.par2}{' '}
                {item.par3 && (
                  <Link
                    href={item.par3}
                    className="text-blue-500 underline ml-1"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {item.par3}
                  </Link>
                )}
              </span>{' '}
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Email Marketing
        </h5>

        <ul className="flex flex-col gap-3 mt-1">
          {EmailMarketing.map((item) => (
            <li
              key={item.par1}
              className="text-[16px] font-normal leading-6 text-neutral-900 flex flex-col"
            >
              <span className="font-normal text-[16px] text-neutral-900">
                {item.par1}
              </span>

              <span className="font-normal text-[16px] text-neutral-900 mt-2">
                {item.par2}
              </span>

              <span className="text-[16px] font-semibold mt-4 ml-10">
                {' '}
                Azure Communication Services{' '}
              </span>
              <span className="font-normal text-[16px] text-neutral-900 ml-10">
                {item.par3}{' '}
                {item.par4 && (
                  <Link
                    href={item.par4}
                    className="text-blue-500 underline ml-1"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {item.par4}
                  </Link>
                )}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </>
  );
}

export default Details;
