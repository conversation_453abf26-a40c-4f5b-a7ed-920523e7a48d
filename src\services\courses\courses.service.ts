/* eslint-disable @typescript-eslint/no-explicit-any */
import type { CourseFilters } from '@/types/courseType';
import axiosClient from '@/utils/axiosClient';

const API_URL = '/Courses';

class CourseServices {
  async all(params: Partial<CourseFilters>) {
    const data = await axiosClient.get('/courses', {
      params,
    });
    return data?.data;
  }

  async getCourseEnrollments(userId: string): Promise<any> {
    const response = await axiosClient.get(`${API_URL}/Enrollments/${userId}`);
    return response.data;
  }

  async getCourseCheckEnrollments(
    userId: string,
    courseId: string
  ): Promise<any> {
    const response = await axiosClient.get(
      `${API_URL}/${courseId}/check-enrollment/${userId}`
    );
    return response.data;
  }

  async getCourseUpdateEnrollments(courseId: string): Promise<any> {
    const response = await axiosClient.put(`${API_URL}/${courseId}/Complete`);
    return response.data;
  }
}

const courseServices = new CourseServices();
export default courseServices;
