'use client';

import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import CourseList from '@/components/dashboard/courses/explore-course/CourseList';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

export default function CourseListPage() {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };
  return (
    <div>
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'UpSkilling',
            href: '/dashboard/upskilling/all-courses',
          },
          {
            label: 'Explore Courses',
            href: 'dashboard/upskilling',
          },
        ]}
      />
      <Button
        variant="outline"
        className="bg-transparent py-6 px-6 my-6"
        onClick={handleBack}
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        Back
      </Button>
      <div>
        <CourseList />
      </div>
    </div>
  );
}
