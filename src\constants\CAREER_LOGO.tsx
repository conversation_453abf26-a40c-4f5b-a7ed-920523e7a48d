import WhitelogoImg from '@/assets/images/ws/logo-countryNav-white.png';
import logoImg from '@/assets/images/ws/logo-countryNav.svg';
import useSettingsStore from '@/zustand/store/settingsStore';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

export interface LogoProps {
  className?: string;
  logoColor?: 'black' | 'white';
  defaultRoute?: string;
}

const Logo: React.FC<LogoProps> = ({
  className = 'w-100',
  logoColor = 'black',
  defaultRoute = '/',
}) => {
  const { appearanceSettings } = useSettingsStore();

  return (
    <Link
      href={defaultRoute}
      className={`ttnc-logo inline-block text-primary-6000 focus:outline-none focus:ring-0 ${className}`}
    >
      <Image
        src={
          appearanceSettings?.platformLogo ||
          (logoColor === 'white' ? WhitelogoImg : logoImg)
        }
        alt="Logo"
        width={280}
        height={32}
        style={{ height: 50, width: 'auto', display: 'block' }}
      />
    </Link>
  );
};

export default Logo;
