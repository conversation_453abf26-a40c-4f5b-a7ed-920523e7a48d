{"personas": {"software-engineer": {"id": "software-engineer", "name": "Software Engineer", "target-role": "Engineering Manager", "description": "Mid-level developer looking to advance to senior roles", "avatar": "👨‍💻", "profile": {"skills": ["React", "Node.js", "TypeScript"], "jobMode": "Remote or Hybrid", "experience": "5–7 years", "industry": "Software / Tech", "location": "Dubai"}, "careerRoadmap": {"header": {"title": "Career Roadmap - Software Engineer", "showBackButton": true}, "careerPath": {"title": "Your career path as a Software Engineer", "subtitle": "Estimated monthly salary progression:", "roles": [{"id": "software-engineer", "title": "Software Engineer (Current)", "salary": "AED 15k-20k", "current": true, "completed": false}, {"id": "senior-software-engineer", "title": "Senior Software Engineer", "salary": "AED 25k-35k", "current": false, "completed": false}, {"id": "tech-lead", "title": "Tech Lead", "salary": "AED 35k-45k", "current": false, "completed": false}, {"id": "engineering-manager", "title": "Engineering Manager", "salary": "AED 45k+", "current": false, "completed": false}], "changeGoalButton": {"text": "Change my career goal", "enabled": true}}, "growthOpportunity": {"type": "promotion", "icon": "sparkles", "title": "GROWTH OPPORTUNITY", "message": "You can be promoted to Senior Software Engineer within the next 18 months!", "targetRole": "Senior Software Engineer", "timeframe": "18 months", "backgroundColor": "blue-50", "borderColor": "blue-200", "textColor": "blue-700"}, "stepsSection": {"title": "Steps to Become a Senior Software Engineer in 18 Months", "description": "Follow this roadmap to advance your technical skills and leadership capabilities. Each step builds the expertise senior engineers need.", "targetRole": "Senior Software Engineer", "timeframe": "18 months"}, "aiAssistant": {"title": "Ask your AI Assistant", "buttons": [{"id": "technical-guidance", "text": "Help me with technical skills", "icon": "zap", "action": "technicalGuidance", "primary": true}, {"id": "career-advice", "text": "Career advancement tips", "icon": "sparkles", "action": "careerAdvice", "primary": false}]}, "growthChecklist": {"title": "Your Growth Checklist", "showProgress": true, "progressLabel": "Progress", "sections": [{"id": "technical-mastery", "title": "1. Master Technical Skills", "isOpen": true, "items": [{"id": "system-design", "text": "Complete system design course and practice designing scalable systems", "completed": false, "priority": "high", "estimatedHours": 60, "category": "technical"}, {"id": "advanced-algorithms", "text": "Master advanced algorithms and data structures", "completed": true, "priority": "high", "estimatedHours": 40, "category": "technical"}, {"id": "cloud-platforms", "text": "Get certified in AWS/Azure/GCP cloud platforms", "completed": false, "priority": "medium", "estimatedHours": 80, "category": "technical"}]}, {"id": "code-quality", "title": "2. Improve Code Quality & Architecture", "isOpen": false, "items": [{"id": "code-reviews", "text": "Lead code reviews and establish coding standards", "completed": false, "priority": "high", "estimatedHours": 20, "category": "leadership"}, {"id": "refactoring", "text": "Refactor legacy codebase and improve architecture", "completed": false, "priority": "medium", "estimatedHours": 100, "category": "technical"}, {"id": "testing-strategy", "text": "Implement comprehensive testing strategy (unit, integration, e2e)", "completed": false, "priority": "high", "estimatedHours": 40, "category": "technical"}]}, {"id": "mentorship", "title": "3. Develop Leadership & Mentorship", "isOpen": false, "items": [{"id": "mentor-junior", "text": "Mentor 2-3 junior developers", "completed": false, "priority": "high", "estimatedHours": 50, "category": "leadership"}, {"id": "tech-talks", "text": "Give technical presentations to the team", "completed": false, "priority": "medium", "estimatedHours": 15, "category": "communication"}, {"id": "project-lead", "text": "Lead a complex project from conception to deployment", "completed": false, "priority": "high", "estimatedHours": 200, "category": "leadership"}]}, {"id": "business-impact", "title": "4. Drive Business Impact", "isOpen": false, "items": [{"id": "performance-optimization", "text": "Optimize system performance and reduce costs by 20%", "completed": false, "priority": "high", "estimatedHours": 60, "category": "impact"}, {"id": "cross-team-collaboration", "text": "Collaborate with product and design teams on feature planning", "completed": false, "priority": "medium", "estimatedHours": 30, "category": "collaboration"}, {"id": "technical-documentation", "text": "Create comprehensive technical documentation and runbooks", "completed": false, "priority": "medium", "estimatedHours": 25, "category": "documentation"}]}]}}}, "senior-consultant": {"id": "senior-consultant", "name": "Senior Consultant", "target-role": "Managing Director", "description": "Experienced consultant aiming for partner track", "avatar": "👔", "profile": {"skills": ["Client Strategy", "Digital Transformation", "Stakeholder Management"], "jobMode": "On-site or Hybrid (client-facing)", "experience": "10+ years", "industry": "Management Consulting / Strategy", "location": "Dubai"}, "careerRoadmap": {"header": {"title": "Career Roadmap - Senior Consultant", "showBackButton": true}, "careerPath": {"title": "Your career path as a Senior Consultant", "subtitle": "Estimated monthly salary progression:", "roles": [{"id": "senior-consultant", "title": "Senior Consultant (Current)", "salary": "AED 30k-40k", "current": true, "completed": false}, {"id": "principal-consultant", "title": "Principal Consultant", "salary": "AED 45k-60k", "current": false, "completed": false}, {"id": "partner-track", "title": "Partner Track", "salary": "AED 70k-90k", "current": false, "completed": false}, {"id": "managing-director", "title": "Managing Director", "salary": "AED 100k+", "current": false, "completed": false}], "changeGoalButton": {"text": "Change my career goal", "enabled": true}}, "growthOpportunity": {"type": "promotion", "icon": "sparkles", "title": "GROWTH OPPORTUNITY", "message": "You're on track for Principal Consultant within the next 24 months!", "targetRole": "Principal Consultant", "timeframe": "24 months", "backgroundColor": "green-50", "borderColor": "green-200", "textColor": "green-700"}, "stepsSection": {"title": "Steps to Become a Principal Consultant in 24 Months", "description": "Advance your consulting expertise and client relationships. Each step builds the strategic thinking and business development skills needed for senior roles.", "targetRole": "Principal Consultant", "timeframe": "24 months"}, "aiAssistant": {"title": "Ask your AI Assistant", "buttons": [{"id": "client-strategy", "text": "Help with client strategy", "icon": "zap", "action": "clientStrategy", "primary": true}, {"id": "business-development", "text": "Business development tips", "icon": "sparkles", "action": "businessDevelopment", "primary": false}]}, "growthChecklist": {"title": "Your Growth Checklist", "showProgress": true, "progressLabel": "Progress", "sections": [{"id": "client-excellence", "title": "1. Excel in Client Delivery", "isOpen": true, "items": [{"id": "client-satisfaction", "text": "Achieve 95%+ client satisfaction scores across 3 major projects", "completed": true, "priority": "high", "estimatedHours": 200, "category": "client-service"}, {"id": "complex-projects", "text": "Lead 2 complex, multi-million dollar transformation projects", "completed": false, "priority": "high", "estimatedHours": 400, "category": "project-management"}, {"id": "industry-expertise", "text": "Develop deep expertise in 2 industry verticals", "completed": false, "priority": "medium", "estimatedHours": 120, "category": "expertise"}]}, {"id": "business-development", "title": "2. Drive Business Development", "isOpen": false, "items": [{"id": "new-clients", "text": "Acquire 3 new enterprise clients worth $2M+ annually", "completed": false, "priority": "high", "estimatedHours": 150, "category": "sales"}, {"id": "proposal-writing", "text": "Lead proposal development with 70%+ win rate", "completed": false, "priority": "high", "estimatedHours": 80, "category": "sales"}, {"id": "networking", "text": "Build strategic network of 50+ C-level contacts", "completed": false, "priority": "medium", "estimatedHours": 100, "category": "networking"}]}, {"id": "thought-leadership", "title": "3. Establish Thought Leadership", "isOpen": false, "items": [{"id": "publications", "text": "Publish 5 industry articles in top-tier publications", "completed": false, "priority": "medium", "estimatedHours": 60, "category": "thought-leadership"}, {"id": "speaking-engagements", "text": "Speak at 3 major industry conferences", "completed": false, "priority": "medium", "estimatedHours": 40, "category": "thought-leadership"}, {"id": "research-initiatives", "text": "Lead proprietary research initiative in your expertise area", "completed": false, "priority": "high", "estimatedHours": 200, "category": "research"}]}, {"id": "team-leadership", "title": "4. Build Team Leadership", "isOpen": false, "items": [{"id": "team-management", "text": "Successfully manage a team of 8-12 consultants", "completed": false, "priority": "high", "estimatedHours": 100, "category": "leadership"}, {"id": "talent-development", "text": "Develop and promote 3 consultants to senior roles", "completed": false, "priority": "medium", "estimatedHours": 80, "category": "mentorship"}, {"id": "practice-building", "text": "Build new service offering generating $5M+ revenue", "completed": false, "priority": "high", "estimatedHours": 300, "category": "innovation"}]}]}}}, "new-graduate": {"id": "new-graduate", "name": "New Graduate", "target-role": "Product Manager", "description": "Recent business graduate starting career journey", "avatar": "🎓", "profile": {"skills": ["SQL", "Excel", "Market Research"], "jobMode": "Hybrid or Entry-Level Remote", "experience": "0–1 year", "industry": "Technology / Product / Business", "location": "Dubai"}, "careerRoadmap": {"header": {"title": "Career Roadmap - New Graduate", "showBackButton": true}, "careerPath": {"title": "Your career path as a Business Graduate", "subtitle": "Estimated monthly salary progression:", "roles": [{"id": "business-analyst", "title": "Business Analyst (Current)", "salary": "AED 8k-12k", "current": true, "completed": false}, {"id": "senior-business-analyst", "title": "Senior Business Analyst", "salary": "AED 15k-20k", "current": false, "completed": false}, {"id": "product-manager", "title": "Product Manager", "salary": "AED 25k-35k", "current": false, "completed": false}, {"id": "director-product", "title": "Director of Product", "salary": "AED 40k+", "current": false, "completed": false}], "changeGoalButton": {"text": "Change my career goal", "enabled": true}}, "growthOpportunity": {"type": "promotion", "icon": "sparkles", "title": "GROWTH OPPORTUNITY", "message": "You can be promoted to Senior Business Analyst within the next 15 months!", "targetRole": "Senior Business Analyst", "timeframe": "15 months", "backgroundColor": "purple-50", "borderColor": "purple-200", "textColor": "purple-700"}, "stepsSection": {"title": "Steps to Become a Senior Business Analyst in 15 Months", "description": "Build foundational business skills and gain practical experience. Each step develops the analytical and strategic thinking skills needed for advancement.", "targetRole": "Senior Business Analyst", "timeframe": "15 months"}, "aiAssistant": {"title": "Ask your AI Assistant", "buttons": [{"id": "skill-development", "text": "Help me build core skills", "icon": "zap", "action": "skillDevelopment", "primary": true}, {"id": "career-planning", "text": "Plan my career path", "icon": "sparkles", "action": "careerPlanning", "primary": false}]}, "growthChecklist": {"title": "Your Growth Checklist", "showProgress": true, "progressLabel": "Progress", "sections": [{"id": "foundational-skills", "title": "1. Build Foundational Skills", "isOpen": true, "items": [{"id": "excel-mastery", "text": "Master advanced Excel (pivot tables, macros, financial modeling)", "completed": false, "priority": "high", "estimatedHours": 40, "category": "technical"}, {"id": "sql-basics", "text": "Learn SQL for data analysis and reporting", "completed": true, "priority": "high", "estimatedHours": 30, "category": "technical"}, {"id": "powerbi-tableau", "text": "Get certified in Power BI or Tableau for data visualization", "completed": false, "priority": "medium", "estimatedHours": 35, "category": "technical"}]}, {"id": "business-analysis", "title": "2. Develop Business Analysis Skills", "isOpen": false, "items": [{"id": "requirements-gathering", "text": "Complete 3 requirements gathering projects with stakeholders", "completed": false, "priority": "high", "estimatedHours": 120, "category": "analysis"}, {"id": "process-mapping", "text": "Create process maps and identify improvement opportunities", "completed": false, "priority": "high", "estimatedHours": 60, "category": "analysis"}, {"id": "business-case", "text": "Write compelling business cases with ROI analysis", "completed": false, "priority": "medium", "estimatedHours": 40, "category": "strategy"}]}, {"id": "communication-skills", "title": "3. Strengthen Communication", "isOpen": false, "items": [{"id": "stakeholder-presentations", "text": "Deliver 5 successful presentations to senior stakeholders", "completed": false, "priority": "high", "estimatedHours": 25, "category": "communication"}, {"id": "cross-functional-collaboration", "text": "Lead cross-functional project with IT, Finance, and Operations", "completed": false, "priority": "medium", "estimatedHours": 80, "category": "collaboration"}, {"id": "executive-summary", "text": "Write executive summaries and strategic recommendations", "completed": false, "priority": "medium", "estimatedHours": 20, "category": "communication"}]}, {"id": "professional-growth", "title": "4. Accelerate Professional Growth", "isOpen": false, "items": [{"id": "industry-knowledge", "text": "Develop expertise in your company's industry and competitors", "completed": false, "priority": "medium", "estimatedHours": 50, "category": "knowledge"}, {"id": "professional-network", "text": "Build professional network through industry events and LinkedIn", "completed": false, "priority": "medium", "estimatedHours": 30, "category": "networking"}, {"id": "certification", "text": "Obtain business analysis certification (CBAP or PMI-PBA)", "completed": false, "priority": "high", "estimatedHours": 100, "category": "certification"}]}]}}}}}