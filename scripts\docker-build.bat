@echo off
REM Docker build script for Career Navigator Pro UI (Windows)

setlocal enabledelayedexpansion

REM Default values
set IMAGE_NAME=career-navigator-ui
set TAG=latest
set BUILD_TYPE=production
set RUN_CONTAINER=false
set USE_COMPOSE=false

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :main
if "%~1"=="-t" (
    set TAG=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--tag" (
    set TAG=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-n" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--name" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set BUILD_TYPE=development
    shift
    goto :parse_args
)
if "%~1"=="--dev" (
    set BUILD_TYPE=development
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set BUILD_TYPE=production
    shift
    goto :parse_args
)
if "%~1"=="--production" (
    set BUILD_TYPE=production
    shift
    goto :parse_args
)
if "%~1"=="-r" (
    set RUN_CONTAINER=true
    shift
    goto :parse_args
)
if "%~1"=="--run" (
    set RUN_CONTAINER=true
    shift
    goto :parse_args
)
if "%~1"=="-c" (
    set USE_COMPOSE=true
    shift
    goto :parse_args
)
if "%~1"=="--compose" (
    set USE_COMPOSE=true
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_usage
if "%~1"=="--help" goto :show_usage

echo Unknown option: %~1
goto :show_usage

:show_usage
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo   -t, --tag TAG          Set image tag (default: latest)
echo   -n, --name NAME        Set image name (default: career-navigator-ui)
echo   -d, --dev              Build development image
echo   -p, --production       Build production image (default)
echo   -r, --run              Run container after building
echo   -c, --compose          Use docker-compose instead of docker build
echo   -h, --help             Show this help message
echo.
echo Examples:
echo   %~nx0                     # Build production image with default settings
echo   %~nx0 -d -r              # Build and run development image
echo   %~nx0 -t v1.0.0 -r       # Build production image with tag v1.0.0 and run
echo   %~nx0 -c                  # Use docker-compose to build and run
goto :eof

:main
echo [INFO] Starting Docker build process...

if "%USE_COMPOSE%"=="true" (
    echo [INFO] Using Docker Compose...
    
    if "%BUILD_TYPE%"=="development" (
        echo [INFO] Building development environment...
        docker-compose --profile dev up --build
    ) else (
        echo [INFO] Building production environment...
        docker-compose up --build
    )
) else (
    REM Determine Dockerfile and image name
    if "%BUILD_TYPE%"=="development" (
        set DOCKERFILE=Dockerfile.dev
        set FULL_IMAGE_NAME=%IMAGE_NAME%:dev-%TAG%
    ) else (
        set DOCKERFILE=Dockerfile
        set FULL_IMAGE_NAME=%IMAGE_NAME%:%TAG%
    )
    
    echo [INFO] Building %BUILD_TYPE% image: !FULL_IMAGE_NAME!
    echo [INFO] Using Dockerfile: !DOCKERFILE!
    
    REM Build the image
    docker build -f "!DOCKERFILE!" -t "!FULL_IMAGE_NAME!" .
    
    if !errorlevel! equ 0 (
        echo [INFO] Build completed successfully!
        echo [INFO] Image: !FULL_IMAGE_NAME!
    ) else (
        echo [ERROR] Build failed!
        exit /b 1
    )
    
    REM Run container if requested
    if "%RUN_CONTAINER%"=="true" (
        echo [INFO] Starting container...
        
        REM Stop and remove existing container if it exists
        docker stop "%IMAGE_NAME%" 2>nul
        docker rm "%IMAGE_NAME%" 2>nul
        
        REM Run the container
        if "%BUILD_TYPE%"=="development" (
            docker run -d -p 3001:3000 --name "%IMAGE_NAME%-dev" "!FULL_IMAGE_NAME!"
            echo [INFO] Development server running at http://localhost:3001
        ) else (
            docker run -d -p 3000:3000 --name "%IMAGE_NAME%" "!FULL_IMAGE_NAME!"
            echo [INFO] Production server running at http://localhost:3000
        )
    )
)

echo [INFO] Done!
goto :eof
