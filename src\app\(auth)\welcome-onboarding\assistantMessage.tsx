import AssistantTooltip from '@/components/common/AssistantTooltip';
import { Button } from '@/components/ui/button';
import React from 'react';

type Props = {
  handleNext: () => void;
};

const AssistantMessage = (props: Props) => {
  return (
    <section className="max-w-[50rem] h-screen flex items-center justify-center mx-auto">
      <AssistantTooltip>
        <div className="w-full  flex flex-col items-start p-7">
          <h2 className="text-[28px] font-medium text-neutral-900 leading-[36px]">
            Hi there!
          </h2>
          <p className="mt-4 text-[18px] font-normal text-neutral-700 text-left leading-[28px]">
            I’m e-<PERSON>, your personal assistant. Think of me as your digital
            twin, here to make things quick and easy. I’ve pulled together some
            of your information to save you time. Let’s check if everything’s
            accurate. Ready to get started?
          </p>
          <Button onClick={props.handleNext} className="mt-4 px-6 ml-auto py-2">
            Continue
          </Button>
        </div>
      </AssistantTooltip>
    </section>
  );
};

export default AssistantMessage;
