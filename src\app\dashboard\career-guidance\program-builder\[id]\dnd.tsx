/* eslint-disable react-hooks/exhaustive-deps */

'use client';

import React, { useEffect, useState } from 'react';
import {
  DragDropContext,
  Draggable,
  Droppable,
  type DropResult,
} from '@hello-pangea/dnd';
import { Loader2, RotateCcw, Search, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

import Text from '@/components/ui/text';
import Title from '@/components/ui/title';
import type { Course, ProgramDetailsResponse } from '@/types';
import { Button } from '@/components/ui/button';
import CourseDetailsDialog from './dialog';
import {
  useDeleteUserProgramCourses,
  useSaveUserProgramCourses,
} from '@/queries';
import { DeleteProgramDialog } from '@/components/dashboard/components/career-guidance/DeleteProgramDialog';
import { useNotificationStore } from '@/zustand/store/notificationStore';

interface ProgramData {
  id: string;
  name: string;
  department?: string;
  totalCredits?: number;
  courses: Course[];
}

interface ApiResponse {
  data: ProgramData;
}

interface DnDProps {
  data: ProgramDetailsResponse | ApiResponse;
  userCourses: { course: Course }[];
  onSave: (_selectedelective: string[]) => void;
}

const reorder = (
  list: Course[],
  startIndex: number,
  endIndex: number
): Course[] => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

const move = (
  source: Course[],
  destination: Course[],
  droppableSource: { droppableId: string; index: number },
  droppableDestination: { droppableId: string; index: number }
): Record<string, Course[]> => {
  const sourceClone = Array.from(source);
  const destClone = Array.from(destination);
  const [removed] = sourceClone.splice(droppableSource.index, 1);

  destClone.splice(droppableDestination.index, 0, removed);

  const result: Record<string, Course[]> = {};
  result[droppableSource.droppableId] = sourceClone;
  result[droppableDestination.droppableId] = destClone;

  return result;
};

function DnD({ data, userCourses }: DnDProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selected, setSelected] = React.useState<Course | null>(null);
  const [searchValues, setSearchValues] = useState<{ [key: string]: string }>({
    elective: '',
    courses: '',
  });
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [programToDelete, setProgramToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const { showNotification } = useNotificationStore();

  const router = useRouter();

  const programData = (
    'data' in data ? data.data : data
  ) as ProgramDetailsResponse;
  const courses = Array.isArray(programData?.courses)
    ? programData.courses
    : [];

  const [state, setState] = useState<
    { data: Course[]; label: string; id: 'elective' | 'courses' }[]
  >([
    { label: 'My elective', data: [], id: 'elective' },
    { label: 'Available Courses', data: [], id: 'courses' },
  ]);

  const [displayState, setDisplayState] = useState<
    { data: Course[]; label: string; id: 'elective' | 'courses' }[]
  >([
    { label: 'My elective', data: [], id: 'elective' },
    { label: 'Available Courses', data: [], id: 'courses' },
  ]);

  const mandatoryCourses = React.useMemo(
    () => courses?.filter((course) => course.type === 'Mandatory') || [],
    [courses]
  );
  const electiveCourses = React.useMemo(
    () => courses?.filter((course) => course.type === 'Elective') || [],
    [courses]
  );

  const totalMandatoryCredits = mandatoryCourses.reduce(
    (acc, course) =>
      course.type === 'Mandatory' && course.credits
        ? acc + course.credits
        : acc,
    0
  );
  const totalSelectedCredits = state[0].data.reduce(
    (acc, course) => (course.credits ? acc + course.credits : acc),
    0
  );

  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (!courses || courses.length === 0) return;

    const mandatoryCoursesList =
      courses.filter((course) => course.type === 'Mandatory') || [];
    const electiveCoursesList =
      courses.filter((course) => course.type === 'Elective') || [];

    const userCoursesIds = userCourses.map((item) => item.course.id);
    const mandatoryCoursesIds = mandatoryCoursesList.map((course) => course.id);

    const selectedIds = userCoursesIds.filter(
      (id) => !mandatoryCoursesIds.includes(id)
    );

    const newState: {
      data: Course[];
      label: string;
      id: 'elective' | 'courses';
    }[] = [
      {
        label: 'My elective',
        data: [
          ...(electiveCoursesList.filter((course) =>
            selectedIds.includes(course.id)
          ) || []),
        ],
        id: 'elective',
      },
      {
        label: 'Available Courses',
        data: [
          ...(electiveCoursesList.filter(
            (course) => !selectedIds.includes(course.id)
          ) || []),
        ],
        id: 'courses',
      },
    ];

    setState(newState);
    setDisplayState(newState);
    setHasChanges(false);
  }, [courses, userCourses]);

  const filteredData = () => {
    const filteredelective = state[0].data.filter(
      (course) =>
        course.type !== 'Mandatory' &&
        (course.name
          .toLowerCase()
          .includes(searchValues.elective.toLowerCase()) ||
          course.code
            .toLowerCase()
            .includes(searchValues.elective.toLowerCase()))
    );

    const filteredCourses = state[1].data.filter(
      (course) =>
        course.type !== 'Mandatory' &&
        (course.name
          .toLowerCase()
          .includes(searchValues.courses.toLowerCase()) ||
          course.code
            .toLowerCase()
            .includes(searchValues.courses.toLowerCase()))
    );

    setDisplayState([
      {
        label: 'My electives',
        data: searchValues.elective ? filteredelective : [...state[0].data],
        id: 'elective',
      },
      {
        label: 'Available Courses',
        data: searchValues.courses ? filteredCourses : [...state[1].data],
        id: 'courses',
      },
    ]);
  };

  useEffect(() => {
    filteredData();
  }, [searchValues, state]);

  const { mutate: saveUserProgramCourses } = useSaveUserProgramCourses();

  const handleRowClick = (event: React.MouseEvent<unknown>, id: string) => {
    event.preventDefault();
    event.stopPropagation();

    const item = courses.find((course) => course.id === id);

    if (item) {
      setSelected(item);
    }
  };

  const handleSave = () => {
    setIsLoading(true);
    const selectedCourses = {
      programId: programData.id,
      courseIds: [
        ...mandatoryCourses.map((course) => course?.id),
        ...state[0].data.map((course) => course.id),
      ],
    };

    saveUserProgramCourses(selectedCourses, {
      onSuccess: () => {
        setHasChanges(false);
        router.push('/dashboard/career-guidance/program-builder');
      },
      onSettled: () => {
        setIsLoading(false);
      },
    });
  };

  const { mutate: deleteProgramsProgramCourses, isPending: isDeleting } =
    useDeleteUserProgramCourses();

  const openDeleteDialog = (id: string, name: string) => {
    setProgramToDelete({ id, name });
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = () => {
    if (!programToDelete) return;
    try {
      const courseIds = [
        ...mandatoryCourses.map((course) => course.id),
        ...state[0].data.map((course) => course.id),
      ];
      deleteProgramsProgramCourses(
        {
          programId: programToDelete.id,
          courseIds,
        },
        {
          onSuccess: () => {
            showNotification('Program deleted successfully!', 'success');
            router.push('/dashboard/career-guidance/program-builder');
          },
          onError: () => {
            showNotification(
              'Failed to delete program. Please try again.',
              'error'
            );
          },
          onSettled: () => {
            setIsDeleteDialogOpen(false);
            setProgramToDelete(null);
          },
        }
      );
    } catch (error) {
      console.error('Error deleting program:', error);
      showNotification('An unexpected error occurred', 'error');
      setIsDeleteDialogOpen(false);
      setProgramToDelete(null);
    }
  };

  const handleReset = () => {
    setIsLoading(true);
    const newState: {
      label: string;
      data: Course[];
      id: 'elective' | 'courses';
    }[] = [
      { label: 'My elective', data: [], id: 'elective' },
      {
        label: 'Available Courses',
        data: [...electiveCourses],
        id: 'courses',
      },
    ];
    setState(newState);
    setDisplayState(newState);
    setSearchValues({ elective: '', courses: '' });
    setHasChanges(true);

    const selectedCourses = {
      programId: programData.id,
      courseIds: [...mandatoryCourses.map((course) => course.id)],
    };

    saveUserProgramCourses(selectedCourses, {
      onSettled: () => {
        setIsLoading(false);
      },
    });
  };

  const handleAddToProgram = (course: Course) => {
    const newState = [...state];
    const courseIndex = newState[1].data.findIndex((c) => c.id === course.id);

    if (courseIndex !== -1) {
      const [movedCourse] = newState[1].data.splice(courseIndex, 1);
      newState[0].data.push(movedCourse);

      setState(newState);
      setDisplayState(newState);
      setSelected(null);
      setHasChanges(true);
    }
  };

  const handleRemoveFromProgram = (course: Course) => {
    const newState = [...state];
    const courseIndex = newState[0].data.findIndex((c) => c.id === course.id);

    if (courseIndex !== -1) {
      const [movedCourse] = newState[0].data.splice(courseIndex, 1);
      newState[1].data.push(movedCourse);

      setState(newState);
      setDisplayState(newState);
      setSelected(null);
      setHasChanges(true);
    }
  };

  const handleClearSearch = (field: 'elective' | 'courses') => {
    setSearchValues((prev) => ({
      ...prev,
      [field]: '',
    }));
  };

  const isSelectedCourseInProgram = React.useMemo(() => {
    if (!selected) return false;
    return state[0].data.some((course) => course.id === selected.id);
  }, [selected, state]);

  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) {
      return;
    }

    const sInd = +source.droppableId;
    const dInd = +destination.droppableId;

    if (sInd === dInd) {
      const items = reorder(state[sInd].data, source.index, destination.index);
      const newState = [...state];
      newState[sInd].data = items;
      setState(newState);
      setDisplayState(newState);
      setHasChanges(true);
    } else {
      const result = move(
        state[sInd].data,
        state[dInd].data,
        source,
        destination
      );
      const newState = [...state];
      newState[sInd].data = result[sInd];
      newState[dInd].data = result[dInd];

      setState(newState.filter((group) => group.data.length > -1));
      setDisplayState(newState.filter((group) => group.data.length > -1));
      setHasChanges(true);
    }
  };

  return (
    <>
      <Title className="text-blue-400 mt-6" variant="h5">
        Customize elective
      </Title>
      <Text className="text-neutral-600 max-w-[800px] mt-3">
        Customize your elective for your program by simply dragging and dropping
        them into the &quot;My elective&quot; section below or clicking on the
        course to see more details. Once you&apos;re satisfied with your
        selections, click &quot;Save Changes&quot; to see how they impact the
        occupations matched based on the skills acquired upon completion of your
        program.
      </Text>
      <div className="flex w-full border-none flex-wrap xl:flex-nowrap xl:justify-between">
        <DragDropContext onDragEnd={onDragEnd}>
          {displayState.map((el, ind) => (
            <Droppable
              key={el.id}
              droppableId={`${ind}`}
              isDropDisabled={isLoading}
            >
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  className={`mt-6 p-4 border border-blue-100 rounded-xl ${snapshot.isDraggingOver ? 'bg-neutral-300' : 'bg-white'} w-full xl:w-[calc(50%_-_8px)]`}
                  {...provided.droppableProps}
                  style={{
                    minHeight: '500px',
                    overflow: 'hidden',
                  }}
                >
                  <Title variant="h5">{el.label}</Title>
                  <div className="flex justify-between items-center gap-3 border border-neutral-200 h-[48px] my-4 p-4 rounded-full">
                    <div className="flex items-center">
                      <Search className="text-neutral-500" />
                      <input
                        value={searchValues[el.id]}
                        onChange={(e) => {
                          setSearchValues((prev) => ({
                            ...prev,
                            [el.id]: e.target.value,
                          }));
                        }}
                        className={`!mt-4 !mb-4 border-none w-[300px] !focus:border-none focus:outline-none ring-0 focus:ring-0 px-4`}
                        placeholder={'Search course name or code...'}
                      />
                    </div>
                    {searchValues[el.id] && (
                      <button
                        className="cursor-pointer"
                        onClick={() => handleClearSearch(el.id)}
                        aria-label="Clear search"
                      >
                        <X className="w-[24px] h-[24px] text-neutral-700" />
                      </button>
                    )}
                  </div>
                  <div
                    className={`rounded-lg flex p-4 w-full ${el.id === 'elective' ? 'bg-blue-400' : 'bg-neutral-300'}`}
                  >
                    <div className={`min-w-[80px] ml-8`}>
                      <Text
                        className={`font-semibold ${el.id === 'elective' ? '!text-white' : 'text-neutral-700'}`}
                      >
                        Code
                      </Text>
                    </div>
                    <div className={`ml-2 w-full`}>
                      <Text
                        className={`font-semibold ${el.id === 'elective' ? '!text-white' : 'text-neutral-700'}`}
                      >
                        Name
                      </Text>
                    </div>
                    <div className={`hidden min-w-[80px] w-[80px] md:block`}>
                      <Text
                        className={`font-semibold ${el.id === 'elective' ? '!text-white' : 'text-neutral-700'}`}
                      >
                        Credits
                      </Text>
                    </div>
                    <div className={`hidden min-w-[60px] w-[60px] md:block`}>
                      <Text
                        className={`font-semibold ${el.id === 'elective' ? '!text-white' : 'text-neutral-700'}`}
                      >
                        Skills
                      </Text>
                    </div>
                    <div className={`w-[26px] min-w-[26px]`} />
                  </div>
                  <div style={{ overflowY: 'auto', maxHeight: '400px' }}>
                    {el.data.map((item, index) => (
                      <Draggable
                        key={item.id}
                        draggableId={item.id}
                        index={index}
                        isDragDisabled={isLoading}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={{
                              ...provided.draggableProps.style,
                              marginBottom: '8px',
                            }}
                            onClick={(event) => handleRowClick(event, item.id)}
                            role="button"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                              }
                            }}
                            tabIndex={0}
                            className={`p-3 ${index % 2 === 0 ? 'bg-white' : 'bg-neutral-50'} rounded-lg ${snapshot.isDragging ? 'opacity-80' : 'opacity-1'} hover:bg-neutral-100 cursor-pointer`}
                          >
                            <div className={`flex w-full items-center`}>
                              <div className={`w-[26px] min-w-[26px]`}>
                                <svg
                                  width="24"
                                  height="24"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                >
                                  <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M9.5 5C9.5 5.53043 9.28929 6.03914 8.91421 6.41421C8.53914 6.78929 8.03043 7 7.5 7C6.96957 7 6.46086 6.78929 6.08579 6.41421C5.71071 6.03914 5.5 5.53043 5.5 5C5.5 4.46957 5.71071 3.96086 5.08579 3.58579C5.46086 3.21071 5.96957 3 6.5 3C7.03043 3 7.53914 3.21071 7.91421 3.58579C8.28929 3.96086 8.5 4.46957 8.5 5ZM7.5 14C8.03043 14 8.53914 13.7893 8.91421 13.4142C9.28929 13.0391 9.5 12.5304 9.5 12C9.5 11.4696 9.28929 10.9609 8.91421 10.5858C8.53914 10.2107 8.03043 10 7.5 10C6.96957 10 6.46086 10.2107 6.08579 10.5858C5.71071 10.9609 5.5 11.4696 5.5 12C5.5 12.5304 5.71071 13.0391 6.08579 13.4142C6.46086 13.7893 6.96957 14 7.5 14ZM7.5 21C8.03043 21 8.53914 20.7893 8.91421 20.4142C9.28929 20.0391 9.5 19.5304 9.5 19C9.5 18.4696 9.28929 17.9609 8.91421 17.5858C8.53914 17.2107 8.03043 17 7.5 17C6.96957 17 6.46086 17.2107 6.08579 17.5858C5.71071 17.9609 5.5 18.4696 5.5 19C5.5 19.5304 5.71071 20.0391 6.08579 20.4142C6.46086 20.7893 6.96957 21 7.5 21ZM18.5 5C18.5 5.53043 18.2893 6.03914 17.9142 6.41421C17.5391 6.78929 17.0304 7 16.5 7C15.9696 7 15.4609 6.78929 15.0858 6.41421C14.7107 6.03914 14.5 5.53043 14.5 5C14.5 4.46957 14.7107 3.96086 15.0858 3.58579C15.4609 3.21071 15.9696 3 16.5 3C17.0304 3 17.5391 3.21071 17.9142 3.58579C18.2893 3.96086 18.5 4.46957 18.5 5ZM16.5 14C17.0304 14 17.5391 13.7893 17.9142 13.4142C18.2893 13.0391 18.5 12.5304 18.5 12C18.5 11.4696 18.2893 10.9609 17.9142 10.5858C17.5391 10.2107 17.0304 10 16.5 10C15.9696 10 15.4609 10.2107 15.0858 10.5858C14.7107 10.9609 14.5 11.4696 14.5 12C14.5 12.5304 14.7107 13.0391 15.0858 13.4142C15.4609 13.7893 15.9696 14 16.5 14ZM16.5 21C17.0304 21 17.5391 20.7893 17.9142 20.4142C18.2893 20.0391 18.5 19.5304 18.5 19C18.5 18.4696 18.2893 17.9609 17.9142 17.5858C17.5391 17.2107 17.0304 17 16.5 17C15.9696 17 15.4609 17.2107 15.0858 17.5858C14.7107 17.9609 14.5 18.4696 14.5 19C14.5 19.5304 14.7107 20.0391 15.0858 20.4142C15.4609 20.7893 15.9696 21 16.5 21Z"
                                    fill="#ADB5BD"
                                  />
                                </svg>
                              </div>
                              <Text
                                className={`text-neutral-600 mx-2 w-[80px] min-w-[80px] text-nowrap`}
                              >
                                {item.code}
                              </Text>
                              <Text className={`text-neutral-600 mr-3 w-full`}>
                                {item.name}
                              </Text>
                              <Text
                                className={`hidden w-[80px] min-w-[80px] text-neutral-600 md:block`}
                              >
                                {item.credits}
                              </Text>
                              <Text
                                className={`hidden w-[60px] min-w-[60px] text-neutral-600 md:block`}
                              >
                                {
                                  (item.escoSkills || '')
                                    .split(';')
                                    .filter((skill) => skill !== '').length
                                }
                              </Text>
                              <div className={`w-[26px] min-w-[26px]`}>
                                <svg
                                  className={`fill-neutral-500`}
                                  width="24"
                                  height="24"
                                >
                                  <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M8.51192 4.43063C8.82641 4.16107 9.29989 4.19749 9.56946 4.51198L15.5695 11.512C15.8102 11.7928 15.8102 12.2073 15.5695 12.4882L9.56946 19.4882C9.29989 19.8027 8.82641 19.8391 8.51192 19.5695C8.19743 19.2999 8.161 18.8265 8.43057 18.512L14.0122 12.0001L8.43057 5.48817C8.161 5.17367 8.19743 4.7002 8.51192 4.43063Z"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  </div>
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          ))}
        </DragDropContext>
      </div>
      <div className="bg-neutral-50 px-6 py-4 rounded-lg my-6 w-[396px] flex items-center">
        <Text className="mr-2 text-blue-400 text-[1.75rem] font-semibold">
          {totalMandatoryCredits + totalSelectedCredits}/
          {programData.totalCredits || 0}
        </Text>
        <Text className="text-neutral-700">
          Total Credits (
          {programData.totalCredits &&
          programData.totalCredits -
            totalMandatoryCredits -
            totalSelectedCredits >
            0
            ? programData.totalCredits -
              totalMandatoryCredits -
              totalSelectedCredits
            : 0}{' '}
          remaining)
        </Text>
      </div>
      <div className="flex gap-3">
        <div>
          <Button
            disabled={isLoading || !hasChanges}
            className="!text-white"
            onClick={handleSave}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Save Changes
          </Button>
        </div>
        <div>
          <Button disabled={isLoading} onClick={handleReset} variant="outline">
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset elective
          </Button>
        </div>

        <div>
          <Button
            onClick={() => openDeleteDialog(programData.id, programData.name)}
            disabled={isDeleting}
            variant="outline"
            className="border-red-500 text-red-500 hover:bg-red-50"
          >
            {isDeleting && programToDelete?.id === programData.id ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Remove Program
          </Button>
        </div>
      </div>
      <CourseDetailsDialog
        selected={selected}
        setSelected={setSelected}
        isInProgram={isSelectedCourseInProgram}
        onAddToProgram={handleAddToProgram}
        onRemoveFromProgram={handleRemoveFromProgram}
        programId={programData.id}
      />

      <DeleteProgramDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        programToDelete={programToDelete}
        onDelete={handleDelete}
        isDeleting={isDeleting}
      />
    </>
  );
}

export default DnD;
