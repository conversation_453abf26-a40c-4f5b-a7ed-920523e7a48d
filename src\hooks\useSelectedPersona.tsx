'use client';

import { usePersonaStore } from '@/zustand/store/personaStore';
import personasData from '@/constants/PERSONA_DATA.json';

export function useSelectedPersona() {
  const { selectedPersonaId } = usePersonaStore();

  if (!selectedPersonaId) {
    return null;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const personas = personasData.personas as any;
  const selectedPersona = personas[selectedPersonaId];

  return {
    id: selectedPersonaId,
    ...selectedPersona,
  };
}
