'use client';
import React from 'react';
import EligibleBenefits from '../components/EligibleBenefits';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import CantFindWhatYouNeed from '../components/CantFindWhatYouNeed';

const EligibleTmeplate = () => {
  return (
    <div className="space-y-4">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Financial Benefits',
            href: '/dashboard/financial-benefits/eligible-benefits',
          },
          {
            label: 'Eligible Benefits',
            href: '/dashboard/financial-benefits/eligible-benefits',
          },
        ]}
      />
      <div className="space-y-3  md:max-w-[60%] leading-[30px]">
        <h4 className="text-neutral-900 font-semibold text-2xl">
          Eligible Benefits
        </h4>
        <p className="text-neutral-700 text-[16px] font-normal">
          Learn about financial aid and support programs you can apply for and
          claim.
        </p>
      </div>
      <EligibleBenefits />
      <CantFindWhatYouNeed />
    </div>
  );
};

export default EligibleTmeplate;
