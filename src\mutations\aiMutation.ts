'use client';

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import aiService from '@/services/ai';
import type {
  ISkillsRecommendation,
  IUpskillingPlan,
  IGrowthOpportunitySteps,
  IGrowthOpportunityChecklist,
  IJobMatchParams,
  IJob,
} from '@/types/aiTypes';

const useGenerateSkillRecommendationMutation = (
  onSuccessCallback?: (_data: { data: ISkillsRecommendation }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: (targetRole: string) =>
      aiService.generateSkillRecommendation(targetRole),
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useGenerateSkillRecommendationMutation: error });
    },
  });

  return {
    generateSkillRecommendation: mutation.mutate,
    isLoading,
    ...mutation,
  };
};

const useGenerateUpskillingSkillDetails = (
  onSuccessCallback?: (_data: { data: IUpskillingPlan }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: (skillName: string) =>
      aiService.generateUpskillingSkillDetails(skillName),
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useGenerateUpskillingRecommendationMutation: error });
    },
  });

  return {
    ...mutation,
    isLoading,
  };
};

const useGenerateGrowthStepsMutation = (
  onSuccessCallback?: (_data: { data: IGrowthOpportunitySteps }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: (targetRole: string) =>
      aiService.generateGrowthSteps(targetRole),
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useGenerateGrowthStepsMutation: error });
    },
  });

  return {
    generateCareerRoadMapGrowthOpportunity: mutation.mutate,
    isLoading,
    ...mutation,
  };
};

const useGenerateOpportunityChecklistMutation = (
  onSuccessCallback?: (_data: { data: IGrowthOpportunityChecklist }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: (targetRole: string) =>
      aiService.generateOpportunityChecklist(targetRole),
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useGenerateOpportunityChecklistMutation: error });
    },
  });

  return {
    generateCareerRoadMapGrowthCheckList: mutation.mutate,
    isLoading,
    ...mutation,
  };
};

const useGenerateTopJobMatchesMutation = (
  onSuccessCallback?: (_data: { data: IJob[] }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: (params: IJobMatchParams) =>
      aiService.generateTopJobMatches(params),
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useGenerateTopJobMatchesMutation: error });
    },
  });

  return {
    ...mutation,
    isLoading,
  };
};

export {
  useGenerateSkillRecommendationMutation,
  useGenerateUpskillingSkillDetails,
  useGenerateGrowthStepsMutation,
  useGenerateOpportunityChecklistMutation,
  useGenerateTopJobMatchesMutation,
};
