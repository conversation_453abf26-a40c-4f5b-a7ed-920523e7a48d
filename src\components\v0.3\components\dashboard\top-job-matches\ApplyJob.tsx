'use client';
import Image from 'next/image';
import { Progress } from '@/components/ui/progress';
import Avatar from '@/assets/images/dashboard/avatar.svg';
import BasicDetailForm from './BasicDetailForm';
import SendApplicationForm from './SendApplicationForm';
import type { IJobMatch } from '@/types';

interface ApplyForJobPropsType {
  step: number;
  job: IJobMatch | null;
  handleBasicDetails: (_details: { email?: string; mobile?: string }) => void;
  handleSendApplication: (_values: { message?: string }) => void;
}

const ApplyForJob: React.FC<ApplyForJobPropsType> = ({
  job,
  step,
  handleBasicDetails,
  handleSendApplication,
}) => {
  return (
    <div className="w-full p-8  gap-8 inline-flex flex-col justify-center relative bg-white rounded-bl-[20px] rounded-br-[20px] border-[1px] border-t-0 border-neutral-200">
      <div>
        <h3 className="font-[600] text-[24px] text-[--headingTextColor]">
          {job?.title}
        </h3>
        <p className="font-medium !text-neutral-500">{`Step ${step} of 2`}</p>
        <div className="pt-[10px]">
          <Progress
            value={(step - 1) * 50}
            className="h-2 bg-gray-200 dark:bg-gray-700"
          />
        </div>
      </div>
      <div className="flex flex-col gap-3">
        <div className="rounded-full bg-gradient-to-br from-[#EECDA3] to-[#EF629F] w-[48px] h-[48px]">
          <Image
            src={Avatar}
            width={48}
            height={48}
            alt="Profile"
            className="rounded-full"
          />
        </div>
        <p className="font-[400] text-neutral-700">
          {step === 1
            ? `I’ll highlight your top strengths for this role — just confirm your contact details below to get started.`
            : `Here’s a message tailored to this employer. Feel free to edit it or send as is.`}
        </p>
      </div>
      <div>
        {step === 1 && (
          <BasicDetailForm handleBasicDetails={handleBasicDetails} />
        )}
        {step === 2 && (
          <SendApplicationForm handleSendApplication={handleSendApplication} />
        )}
      </div>
    </div>
  );
};

export default ApplyForJob;
