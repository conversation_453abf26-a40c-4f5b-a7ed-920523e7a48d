'use client';

import { usePersonaStore } from '@/zustand/store/personaStore';
import PersonaSelector from '@/components/v0.3/components/dashboard/persona/PersonaSelector';
import { CareerDashboard } from '@/components/v0.3/components/dashboard/v0.3Dashboard';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useEffect, useState } from 'react';

export default function Page() {
  const { getJobMatches } = useJobMatchesStore();
  const { selectedPersonaId } = usePersonaStore();
  const selectedPersona = useSelectedPersona();
  const [showPersonaSelector, setShowPersonaSelector] = useState(false);

  // Fetch job matches when persona changes or when initially loaded
  useEffect(() => {
    if (selectedPersonaId && selectedPersona) {
      // Always fetch new job matches when persona changes
      getJobMatches({
        role: selectedPersona?.name,
        skills: 'React, Nodejs',
        jobMode: 'Remote',
        experience: 'Expert',
        industry: 'IT',
        location: 'Dubai',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPersonaId]); // Depend on selectedPersonaId to refetch when persona changes

  // Show persona selector only if no persona is selected
  useEffect(() => {
    setShowPersonaSelector(!selectedPersonaId);
  }, [selectedPersonaId]);

  const handlePersonaSelected = () => {
    setShowPersonaSelector(false);
  };

  const handleSwitchPersona = () => {
    setShowPersonaSelector(true);
  };

  return (
    <div>
      {showPersonaSelector ? (
        <div className="py-6">
          <PersonaSelector onPersonaSelected={handlePersonaSelected} />
        </div>
      ) : (
        <CareerDashboard onSwitchPersona={handleSwitchPersona} />
      )}
    </div>
  );
}
