'use client';

import { usePersonaStore } from '@/zustand/store/personaStore';
import PersonaSelector from '@/components/v0.3/components/dashboard/persona/PersonaSelector';
import { CareerDashboard } from '@/components/v0.3/components/dashboard/v0.3Dashboard';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useEffect, useState } from 'react';

export default function Page() {
  const { selectedPersonaId } = usePersonaStore();
  const [showPersonaSelector, setShowPersonaSelector] = useState(false);

  useEffect(() => {
    setShowPersonaSelector(!selectedPersonaId);
  }, [selectedPersonaId]);

  const handlePersonaSelected = () => {
    setShowPersonaSelector(false);
  };

  const handleSwitchPersona = () => {
    setShowPersonaSelector(true);
  };

  return (
    <div>
      {showPersonaSelector ? (
        <div className="py-6">
          <PersonaSelector onPersonaSelected={handlePersonaSelected} />
        </div>
      ) : (
        <CareerDashboard onSwitchPersona={handleSwitchPersona} />
      )}
    </div>
  );
}
