'use client';

import { usePersonaStore } from '@/zustand/store/personaStore';
import PersonaSelector from '@/components/v0.3/components/dashboard/persona/PersonaSelector';
import { CareerDashboard } from '@/components/v0.3/components/dashboard/v0.3Dashboard';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';
import { useSelectedPersona } from '@/hooks/useSelectedPersona';
import { useEffect, useState } from 'react';

export default function Page() {
  const { getJobMatches, jobMatches } = useJobMatchesStore();
  const { selectedPersonaId } = usePersonaStore();
  const selectedPersona = useSelectedPersona();
  const [showPersonaSelector, setShowPersonaSelector] = useState(true);

  useEffect(() => {
    if (jobMatches?.length === 0 && selectedPersonaId) {
      getJobMatches({
        role: selectedPersona?.name,
        skills: 'React, Nodejs',
        jobMode: 'Remote',
        experience: 'Expert',
        industry: 'IT',
        location: 'Dubai',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePersonaSelected = () => {
    setShowPersonaSelector(false);
  };

  return (
    <div>
      {showPersonaSelector ? (
        <div className="py-6">
          <PersonaSelector onPersonaSelected={handlePersonaSelected} />
        </div>
      ) : (
        <CareerDashboard />
      )}
    </div>
  );
}
