'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { XIcon, Clock5Icon } from 'lucide-react';

export default function InactiveModules() {
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setShowModal(true);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    router.back();
  };

  return (
    <div>
      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 flex justify-center items-center bg-[#0000]/40 backdrop-blur-sm">
          <div className="bg-white px-4 py-6 rounded-lg shadow-lg w-96 text-center">
            <div className="flex justify-end">
              <XIcon
                onClick={closeModal}
                className="text-neutral-400 h-[18px] w-[18px] cursor-pointer"
              />
            </div>

            <div>
              <Clock5Icon className="mx-auto mb-6 w-[24px] h-[24px] text-primary-500" />
              <h2 className="text-[18px] font-bold mb-2">Module Inactive</h2>
              <p className="text-gray-600 mb-4">
                Please contact the administrator to request the module to be
                activated.
              </p>
              <button
                onClick={closeModal}
                className="w-full bg-[--buttonColor] rounded-[--buttonStyle] text-white py-2"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
