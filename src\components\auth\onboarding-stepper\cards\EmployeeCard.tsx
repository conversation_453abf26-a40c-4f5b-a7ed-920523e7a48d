/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import UpdateModal from '@/components/modals/UpdateModal';
import { Button } from '@/components/ui/button';
import { CircleAlert } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';

type Props = {
  employmentDetails: {
    status: string;
    jobStartDate: string;
    profession: string;
    category: string;
    monthlySalary: string;
    pensionableSalary: string;
  };
};

const EmployeeCard: React.FC<Props> = ({ employmentDetails }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitUpdate = (data: any) => {
    console.warn('Employment info update requested:', data);
  };

  return (
    <div className="bg-white text-left  px-7 pb-4 rounded-full">
      <h2 className="text-[18px] font-semibold text-neutral-900 mb-2 mt-5">
        Employment
      </h2>
      <div>
        {/* Employment Details */}
        <div className="py-2 space-y-2 mb-3">
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Status:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.status}
            </span>
          </div>
          <div className="flex justify-between text-[16px]">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Current job start date:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.jobStartDate}
            </span>
          </div>
          <div className="flex justify-between text-[16px]">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Profession:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.profession}
            </span>
          </div>
          <div className="flex justify-between text-[16px">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Category:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.category}
            </span>
          </div>
        </div>
        <hr />
        {/* Salary Details */}
        <h2 className="text-[18px] font-semibold text-neutral-900 mb-2 mt-5">
          Salary
        </h2>
        <div className="py-2 space-y-3">
          <div className="flex justify-between text-[16px]">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Monthly salary:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.monthlySalary}
            </span>
          </div>
          <div className="flex justify-between text-[16px]">
            <div className="flex items-center">
              <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
                Pensionable monthly salary:{' '}
              </span>
              <CircleAlert className="h-5 w-5 ml-1 text-neutral-500" />
            </div>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.pensionableSalary}
            </span>
          </div>
        </div>

        <Button
          className="w-full py-[14px] text-[18px] font-semibold mt-4"
          variant="outline"
          onClick={handleOpenModal}
        >
          My details require updating
        </Button>
      </div>

      <UpdateModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitUpdate}
        type="employment"
      />
    </div>
  );
};

export default EmployeeCard;
