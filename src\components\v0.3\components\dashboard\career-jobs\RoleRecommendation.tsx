'use client';

import { Card, CardContent } from '@/components/ui/card';
import SparklesIcon from '@/components/icons/SparklesIcon';

export default function RoleRecommendationCard() {
  return (
    <Card className="my-8 mx-auto bg-white border border-neutral-200 rounded-[20px] shadow-sm">
      <CardContent className="p-5 space-y-6">
        {/* Why This Role Suits You Section */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <SparklesIcon />
            <h3 className="text-[18px] font-medium text-purple-600 uppercase tracking-wide">
              WHY THIS ROLE SUITS YOU
            </h3>
          </div>
          <p className="text-[16px] text-neutral-700 leading-relaxed">
            With your CS degree and strong analytical mindset, you&apos;re
            exactly who Google is looking for. This role offers real-world
            impact, access to world-class mentors, and a springboard into tech
            innovation—where your skills will shine.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
