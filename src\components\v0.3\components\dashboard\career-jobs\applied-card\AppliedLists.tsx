/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import Image from 'next/image';
import { Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import FallbackImage from '@/assets/fallback_image.svg';
import type { AppliedJob } from '@/types/jobTypes';
import { formatPostedDate, formatSalary } from '@/utils';

interface JobListProps {
  jobs: AppliedJob[];
  selectedJobId?: string;
  onJobSelect: (_job: AppliedJob) => void;
  user: any;
  onRefresh: () => void;
}

const JobList: React.FC<JobListProps> = ({
  jobs,
  selectedJobId,
  onJobSelect,
  onRefresh,
}) => {
  if (jobs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-40 text-center">
        <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
        <p className="text-neutral-500">No jobs found</p>
        <Button
          variant="outline"
          size="sm"
          className="mt-2"
          onClick={onRefresh}
        >
          Refresh
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4 px-2">
      {jobs.map((job) => (
        <Card
          key={job.id}
          className={`cursor-pointer p-4 border border-neutral-200 hover:bg-neutral-50 transition-colors rounded-lg ${
            selectedJobId === job.id
              ? 'border-2 border-primary-500 rounded-lg shadow-sm'
              : ''
          }`}
          onClick={() => onJobSelect(job)}
        >
          <div className="items-start gap-3">
            <div className="flex justify-between">
              <div className="h-[48px] w-[84px] rounded-md flex items-center justify-center overflow-hidden mb-4">
                {job?.job?.company?.logo ? (
                  <Image
                    src={job?.job?.company?.logo || '/placeholder.svg'}
                    alt={`${job?.job?.company?.name || 'Company'} logo`}
                    width={84}
                    height={48}
                    className="object-cover h-[48px] w-[84px]"
                  />
                ) : (
                  <Image
                    src={FallbackImage || '/placeholder.svg'}
                    alt=""
                    width={48}
                    height={48}
                    className="object-cover h-[48px] w-[84px]"
                  />
                )}
              </div>

              <JobStatusBadge status={job.status} />
            </div>

            <div className="flex-1">
              <h3 className="text-[16px] font-semibold line-clamp-1 text-neutral-900 leading-[24px] mb-2">
                {job.job.title}
              </h3>

              <div className="flex flex-wrap gap-2 mb-2">
                <Badge className="text-[12px] bg-neutral-100 text-[--bodyTextColor] font-medium leading-[16px]">
                  {formatSalary(job.job.monthlyFrom)}
                </Badge>
                <Badge className="text-[12px] bg-neutral-100 text-[--bodyTextColor] font-medium leading-[16px]">
                  {job.job.jobType || 'Full-time'}
                </Badge>
                <Badge className="text-[12px] bg-neutral-100 text-[--bodyTextColor] font-medium leading-[16px] capitalize">
                  {job.job.jobMode || 'N/A'}
                </Badge>
              </div>
              <p className="text-[16px] text-neutral-500 mb-2">
                {job.job.companyName || 'Unknown Company'} |{' '}
                {job.job.location || 'Location not specified'}
              </p>
              <p className="text-[16px] text-neutral-500 mt-2">
                {formatPostedDate(job.job.postedDate)}
              </p>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

const JobStatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const statusLower = status.toLowerCase();

  let className = 'bg-transparent text-neutral-900 border border-neutral-100';
  let displayText = 'Applied';

  if (statusLower === 'shortlisted') {
    className = 'bg-transparent text-warning-600 border border-warning-600';
    displayText = 'Shortlisted';
  } else if (statusLower === 'contacted') {
    className = 'bg-transparent text-neutral-900 border border-neutral-100';
    displayText = 'Contacted';
  } else if (statusLower === 'hired') {
    className = 'bg-transparent text-success-500 border border-success-500';
    displayText = 'Hired';
  } else if (statusLower === 'rejected') {
    className =
      'bg-transparent text-destructive-500 border border-destructive-500';
    displayText = 'Not Progressed';
  }

  return (
    <div
      className={`flex items-center justify-center rounded-full h-[32px] px-3 ${className}`}
    >
      <span className="text-[16px]">{displayText}</span>
    </div>
  );
};

export default JobList;
