import MoneyIcon from '@/components/icons/CashAdmin';
import useSettingsStore from '@/zustand/store/settingsStore';
export default function SalaryBrackets() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  const salaryData = [
    { amount: 401, label: 'average Entry Level' },
    { amount: 1114, label: 'average Mid Experience' },
    { amount: 2449, label: 'average High Experience' },
  ];

  return (
    <div className="">
      <div className="flex items-center gap-2 mb-3">
        <MoneyIcon stroke={brandColor} />
        <h2 className="text-lg font-semibold text-black-100">
          Salary Brackets
        </h2>
        <div className="bg-blue-400 text-white rounded-full px-2 py-1">
          <h4 className="text-[16px] font medium">Monthly</h4>
        </div>
        <h4 className="text-[16px] font medium text-black-100">YEARLY</h4>
      </div>
      <div className="flex items-center justify-between space-y-2">
        {salaryData.map((salary) => (
          <div className="flex flex-col" key={salary.amount}>
            <div className="flex items-center space-x-2">
              <p className="text-2xl text-blue-400 font-bold">
                ${salary.amount}
              </p>
              <p className="text-[16px] font-normal text-gray-20">/ Month</p>
            </div>
            <h3 className="text-[16px] text-gray-500">{salary.label}</h3>
          </div>
        ))}
      </div>
    </div>
  );
}
