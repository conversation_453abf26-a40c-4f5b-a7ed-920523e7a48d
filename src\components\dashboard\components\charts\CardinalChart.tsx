import React, { PureComponent } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

const data = [
  { name: 'Dec 23', monthData: 550 },
  { name: '', monthData: 800 },
  { name: 'Jan 24', monthData: 850 },
  { name: '', monthData: 750 },
  { name: 'Feb 24', monthData: 980 },
  { name: 'Mar 24', monthData: 1050 },
];

export default class CardinalCharts extends PureComponent<{
  brandColor: string;
}> {
  constructor(props: { brandColor: string }) {
    super(props);
  }

  render() {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          width={500}
          height={400}
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 10,
          }}
        >
          <CartesianGrid
            strokeDasharray="6 6"
            vertical={false}
            horizontal={true}
          />
          <XAxis
            dataKey="name"
            interval={0}
            tickFormatter={(tick) => (tick === '' ? '' : tick)}
            tick={{ fontSize: 12, textAnchor: 'middle' }}
            padding={{ right: 18 }}
            tickLine={false}
          />
          <YAxis
            domain={[0, 1200]}
            ticks={[0, 500, 750, 1000, 1200]}
            axisLine={false}
            tickLine={false}
          />
          <Tooltip />
          <Area
            type="linear"
            dataKey="monthData"
            stroke={this.props.brandColor}
            fill={this.props.brandColor}
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  }
}
