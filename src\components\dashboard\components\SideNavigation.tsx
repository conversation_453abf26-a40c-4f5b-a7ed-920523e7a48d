/* eslint-disable @typescript-eslint/no-unused-expressions */
'use client';

import type React from 'react';
import { useState } from 'react';
import { ChevronDown, X } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { dashboardSideBarRoutes } from '@/constants/NAV_CONFIG';
import Image from 'next/image';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';

interface SideNavigationProps {
  onClose?: () => void;
}

const SideNavigation: React.FC<SideNavigationProps> = ({ onClose }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const { appearanceSettings } = useSettingsStore();

  const toggleSection = (label: string) => {
    setExpandedSections((prev) =>
      prev.includes(label)
        ? prev.filter((section) => section !== label)
        : [...prev, label]
    );
  };

  const isActive = (route: string) => pathname === route;

  const handleNavigation = (route: string) => {
    router.push(route);
    if (onClose) onClose();
  };

  const handleClose = () => {
    if (onClose) onClose();
  };

  return (
    <div className="bg-[--navMenuColor] !text-white max-w-[280px] h-full flex flex-col overflow-y-auto relative z-10">
      <div className="flex justify-end items-center p-4">
        <button
          onClick={handleClose}
          className="text-white hover:bg-[--navMenuColorOpacity10] p-2 rounded-md transition-colors"
          aria-label="Close sidebar"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="px-3 pb-6">
          <div className="flex flex-col space-y-2">
            {dashboardSideBarRoutes.map((item) => (
              <div key={item.route}>
                <div
                  className={`flex items-center justify-between cursor-pointer px-3 py-6 ${
                    isActive(item.route || '')
                      ? 'bg-[--navMenuColorOpacity10] border-l-4 border-white rounded-r'
                      : 'hover:bg-[--navMenuColorOpacity10]'
                  }`}
                  onClick={() =>
                    item.children
                      ? toggleSection(item.label)
                      : handleNavigation(item.route)
                  }
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      item.children
                        ? toggleSection(item.label)
                        : handleNavigation(item.route);
                    }
                  }}
                >
                  <div className="flex items-center space-x-4">
                    <span className="text-[18px] font-medium !text-white">
                      {item.label}
                    </span>
                  </div>
                  {item.children && (
                    <ChevronDown
                      className={`w-4 h-4 transition-transform !text-white ${
                        expandedSections.includes(item.label)
                          ? 'rotate-180'
                          : ''
                      }`}
                    />
                  )}
                </div>
                {item.children && expandedSections.includes(item.label) && (
                  <div className="ml-7 mt-2 space-y-2 text-[16px] font-medium">
                    {item.children.map((child) => (
                      <button
                        key={child.route}
                        onClick={() => handleNavigation(child.route)}
                        className={`cursor-pointer px-4 py-3 w-full text-left ${
                          isActive(child.route)
                            ? 'bg-[--navMenuColorOpacity10] border-l-4 border-white rounded-r '
                            : 'hover:bg-[--navMenuColorOpacity10]'
                        }`}
                      >
                        <div>
                          <span className="text-[18px] font-medium">
                            {child.label}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="relative min-h-[100px]">
        <div className="absolute bottom-6 left-[12px]">
          <Image
            src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
            width={100}
            height={100}
            className="w-[175px] h-[68px] cursor-pointer"
            alt="bubbleIcon"
            onClick={() => handleNavigation('/dashboard/contact-support')}
          />
        </div>
      </div>
    </div>
  );
};

export default SideNavigation;
