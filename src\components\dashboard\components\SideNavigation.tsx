/* eslint-disable @typescript-eslint/no-unused-expressions */
'use client';

import type React from 'react';
import { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { dashboardSideBarRoutes } from '@/constants/NAV_CONFIG';
import Image from 'next/image';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';

const SideNavigation: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const { appearanceSettings } = useSettingsStore();

  const toggleSection = (label: string) => {
    setExpandedSections((prev) =>
      prev.includes(label)
        ? prev.filter((section) => section !== label)
        : [...prev, label]
    );
  };

  const isActive = (route: string) => pathname === route;

  return (
    <div className="bg-[--navMenuColor] text-white w-full h-full flex flex-col overflow-y-auto relative z-10">
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="px-3 py-6">
          <div className="flex flex-col space-y-2">
            {dashboardSideBarRoutes.map((item) => (
              <div key={item.route}>
                <div
                  className={`flex items-center justify-between cursor-pointer px-3 py-6 ${
                    isActive(item.route || '')
                      ? 'bg-[--navMenuColorOpacity10] border-l-4 border-white rounded-r'
                      : 'hover:bg-[--navMenuColorOpacity10]'
                  }`}
                  onClick={() =>
                    item.children
                      ? toggleSection(item.label)
                      : router.push(item.route)
                  }
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      item.children
                        ? toggleSection(item.label)
                        : router.push(item.route);
                    }
                  }}
                >
                  <div className="flex items-center space-x-4">
                    <span className="text-[18px] font-medium">
                      {item.label}
                    </span>
                  </div>
                  {item.children && (
                    <ChevronDown
                      className={` w-4 h-4 transition-transform ${
                        expandedSections.includes(item.label)
                          ? 'rotate-180'
                          : ''
                      }`}
                    />
                  )}
                </div>
                {item.children && expandedSections.includes(item.label) && (
                  <div className="ml-7 mt-2 space-y-2 text-[16px] font-medium">
                    {item.children.map((child) => (
                      <button
                        key={child.route}
                        onClick={() => router.push(child.route)}
                        className={`cursor-pointer px-4 py-3 ${
                          isActive(child.route)
                            ? 'bg-[--navMenuColorOpacity10] border-l-4 border-white rounded-r '
                            : 'hover:bg-[--navMenuColorOpacity10]'
                        }`}
                      >
                        <div>
                          <span className="text-[18px] font-medium">
                            {child.label}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="relative min-h-[100px]">
        <div className="absolute bottom-6  left-[12px]">
          <Image
            src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
            width={100}
            height={100}
            className="w-[175px] h-[68px] cursor-pointer"
            alt="bubbleIcon"
            onClick={() => router.push('/dashboard/contact-support')}
          />
        </div>
      </div>
    </div>
  );
};

export default SideNavigation;
