import React from 'react';
import { SectionHeader } from '@/components/dashboard/components/SectionHeader';
import { ActionCard } from '@/components/dashboard/components/ActionCard';
import ClockIcon from '@/components/icons/ClockIcon';
import SupportIcon from '@/components/icons/SupportIcon';
import useSettingsStore from '@/zustand/store/settingsStore';

function CantFindWhatYouNeed() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;

  return (
    <div>
      <SectionHeader title="Can't find what you're looking for?" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-4">
        <ActionCard
          icon={<ClockIcon stroke={brandColor} />}
          title={'View your benefits payments history'}
          description={
            'Track your upcoming financial benefits and review payment history.'
          }
        />
        <ActionCard
          icon={<SupportIcon stroke={brandColor} />}
          title={'Contact support'}
          description={
            'Our team is here to assist you with any issues or inquiries.'
          }
        />
      </div>
    </div>
  );
}

export default CantFindWhatYouNeed;
