'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SubTopicTabsProps {
  activeCategory: string;
  activeSubTopic: string;
  onSubTopicChange: (_subTopic: string) => void;
}
// Static subtopics without "All Sub-Topics"
const staticSubTopics = [
  'Data Analysis',
  'Machine Learning',
  'Deep Learning',
  'Natural Language Processing',
  'Computer Vision',
  'Big Data',
  'Data Visualization',
  'Statistical Analysis',
  'Predictive Modeling',
  'Data Mining',
  'Business Intelligence',
  'Data Engineering',
  'Cloud Computing',
  'Database Management',
  'Artificial Intelligence',
];

export default function SubTopicTabs({
  activeSubTopic,
  onSubTopicChange,
}: SubTopicTabsProps) {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const checkScroll = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft <
        container.scrollWidth - container.clientWidth - 10
      );
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScroll);
      checkScroll();
      setTimeout(checkScroll, 100);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
      }
    };
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  return (
    <div className="relative mb-6">
      <div className="flex items-center">
        {showLeftArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 z-10 text-white bg-primary-500 shadow-md rounded-full h-12 w-12 hover:bg-primary-500 hover:text-white"
            onClick={scrollLeft}
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Scroll left</span>
          </Button>
        )}

        <div
          ref={scrollContainerRef}
          className="flex overflow-x-auto pb-2 scrollbar-hide mx-4 scroll-smooth"
          style={{ msOverflowStyle: 'none', scrollbarWidth: 'none' }}
        >
          {staticSubTopics.map((subTopic) => (
            <button
              key={subTopic}
              onClick={() => onSubTopicChange(subTopic)}
              className={`px-3 py-1 text-[16px] whitespace-nowrap mr-2 rounded-md ${activeSubTopic === subTopic
                  ? 'border-2 border-primary-500 bg-primary-400 text-primary-500'
                  : 'bg-transparent text-primary-500 hover:bg-primary-400'
                }`}
            >
              {subTopic}
            </button>
          ))}
        </div>

        {showRightArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 z-10 text-white bg-primary-500 shadow-md rounded-full h-12 w-12 hover:bg-primary-500 hover:text-white"
            onClick={scrollRight}
          >
            <ChevronRight className="h-6 w-6 text-white" />
            <span className="sr-only">Scroll right</span>
          </Button>
        )}
      </div>
    </div>
  );
}
