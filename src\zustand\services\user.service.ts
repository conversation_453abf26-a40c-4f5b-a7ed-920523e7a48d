// services/userService.ts
import axiosClient from '@/utils/axiosClient';
import type { ApiUser, UserStats } from '@/types';

export const fetchUsers = async (
  page = 1,
  size = 10,
  search = ''
): Promise<{ data: ApiUser[]; total: number }> => {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('size', size.toString());
  if (search) {
    params.append('search', search);
  }

  const response = await axiosClient.get(`/users?${params.toString()}`);
  return response.data;
};

export const fetchUserById = async (id: string | number): Promise<ApiUser> => {
  const response = await axiosClient.get(`/users/${id}`);
  return response.data;
};

export const updateUser = async (
  id: string | number,
  userData: Partial<ApiUser>
): Promise<ApiUser> => {
  const response = await axiosClient.put(`/users/${id}`, userData);
  return response.data;
};

export const deleteUser = async (id: string | number): Promise<void> => {
  await axiosClient.delete(`/users/${id}`);
};

export const fetchUserStats = async (): Promise<UserStats> => {
  const response = await axiosClient.get('/user/stats');
  return response.data;
};
