'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, X } from 'lucide-react';

interface SkillsSectionProps {
  title: string;
  count?: number | null;
  skills: string[];
  showBackground: boolean;
  collapsed?: boolean;
}

export default function SkillsSection({
  title,
  skills,
  showBackground,
  collapsed = false,
}: SkillsSectionProps) {
  const [isCollapsed, setIsCollapsed] = useState(collapsed);
  const [visibleSkills, setVisibleSkills] = useState(skills);

  const toggleCollapse = () => {
    setIsCollapsed((prev) => !prev);
  };

  const removeSkill = (skillToRemove: string) => {
    setVisibleSkills((prev) => prev.filter((skill) => skill !== skillToRemove));
  };

  return (
    <div
      className={`${showBackground ? 'bg-neutral-50' : ''} p-4 rounded-lg my-6`}
    >
      <button
        onClick={toggleCollapse}
        className="w-full flex justify-between items-center cursor-pointer"
      >
        <div className="font-medium">
          {title} ({visibleSkills.length} skills)
        </div>
        {isCollapsed ? (
          <ChevronDown className="h-4 w-4 !text-[--bodyTextColor]" />
        ) : (
          <ChevronUp className="h-4 w-4 text-[--bodyTextColor]" />
        )}
      </button>

      {!isCollapsed && visibleSkills.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-3">
          {visibleSkills.map((skill) => (
            <div
              key={skill}
              className="bg-neutral-100 text-[16px] px-3 py-2 rounded border w-fit h-fit flex items-center gap-2"
            >
              <span>{skill}</span>
              <button
                onClick={() => removeSkill(skill)}
                className="text-neutral-500 hover:text-neutral-500"
              >
                <X className="h-4 w-4 !text-[--bodyTextColor]" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
