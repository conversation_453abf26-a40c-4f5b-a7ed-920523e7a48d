'use client';
import type { FC } from 'react';
import { useState } from 'react';
import Image from 'next/image';
import AssistantMessage from './assistantMessage';
import WelcomeOnboardTemplate from '@/components/auth/onboarding-stepper/WelcomeOnboardTemplate';
import { GovernmentEmblem } from '@/components/auth/GovermentEmblems';

const PageLogin: FC = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => setCurrentStep(currentStep + 1);

  return (
    <div className="min-h-screen flex flex-col relative">
      <div className="absolute inset-0 -z-10">
        <Image
          src="/images/ws/confirm-info-bg.png"
          alt="Background"
          fill
          quality={100}
          priority
          className="object-cover object-center"
        />
      </div>

      <div className="flex-1 px-5 py-12">
        {currentStep === 0 ? (
          <AssistantMessage handleNext={handleNext} />
        ) : (
          <WelcomeOnboardTemplate />
        )}
      </div>

      <GovernmentEmblem />
    </div>
  );
};

export default PageLogin;
