'use client';

import { Command } from '@/components/ui/command';

import { Button } from '@/components/ui/button';
import {
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import Text from '@/components/ui/text';
import { cn } from '@/lib/utils';
import {
  useGetUniversityList,
  useRequestProgramsForUniversity,
} from '@/queries';
import type { UniversityItem } from '@/types';
import { Check, ChevronDown, Search } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNotificationStore } from '@/zustand/store/notificationStore';

interface RequestProgramProps {
  isRequestModalOpen: boolean;
  setIsRequestModalOpen: (_open: boolean) => void;
}

export default function RequestProgram({
  isRequestModalOpen,
  setIsRequestModalOpen,
}: RequestProgramProps) {
  const [programUrl, setProgramUrl] = useState('');
  const [requestProgramName, setRequestProgramName] = useState('');
  const [selectedUniversity, setSelectedUniversity] =
    useState<UniversityItem | null>(null);
  const [universityOpen, setUniversityOpen] = useState(false);
  const [urlError, setUrlError] = useState('');
  const [isUrlTouched, setIsUrlTouched] = useState(false);

  const [isMounted, setIsMounted] = useState(false);
  const { showNotification } = useNotificationStore.getState();
  console.warn(isUrlTouched);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const { data: universitiesResponse, isLoading: isUniversitiesLoading } =
    useGetUniversityList();
  const universities =
    isMounted && Array.isArray(universitiesResponse?.result)
      ? universitiesResponse.result
      : [];

  const { mutate: requestProgramsForUniversity, status } =
    useRequestProgramsForUniversity();
  const isSubmitting = status === 'pending';

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (err) {
      console.error('Invalid URL:', err);
      return false;
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setProgramUrl(url);

    if (url && !validateUrl(url)) {
      setUrlError('Please enter a valid URL (e.g., https://example.com)');
    } else {
      setUrlError('');
    }
  };

  const handleSubmitRequest = async () => {
    if (!selectedUniversity || !requestProgramName || !programUrl) {
      return;
    }

    if (!validateUrl(programUrl)) {
      setUrlError('Please enter a valid URL');
      showNotification('Invalid URL format', 'error');
      return;
    }

    try {
      requestProgramsForUniversity(
        {
          universityId: selectedUniversity.id,
          otherUniversityName: selectedUniversity.name,
          programName: requestProgramName,
          programUrl,
        },
        {
          onSuccess: () => {
            showNotification('Request Submitted!', 'success');
            setIsRequestModalOpen(false);
            resetForm();
          },
          onError: (error) => {
            console.error('Error submitting request:', error);
            showNotification('Request failed, Please try again!', 'error');
          },
        }
      );
    } catch (error) {
      console.error('Error in handleSubmitRequest:', error);
    }
  };

  const resetForm = () => {
    setSelectedUniversity(null);
    setRequestProgramName('');
    setProgramUrl('');
    setUrlError('');
    setIsUrlTouched(false);
  };
  const isFormValid = () => {
    return (
      selectedUniversity !== null &&
      requestProgramName.trim() !== '' &&
      programUrl !== '' &&
      validateUrl(programUrl)
    );
  };

  const renderDropdownContent = () => {
    if (!isMounted) return null;

    return (
      <CommandList>
        <CommandEmpty>
          {isUniversitiesLoading ? 'Loading...' : 'No university found.'}
        </CommandEmpty>
        <CommandGroup className="max-h-[300px] overflow-y-auto">
          {Array.isArray(universities) &&
            universities.map((university: UniversityItem) => (
              <CommandItem
                key={university.id}
                value={`${university.name || ''} ${university.location || ''}`}
                onSelect={() => {
                  setSelectedUniversity(
                    university.id === selectedUniversity?.id ? null : university
                  );
                  setUniversityOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    selectedUniversity?.id === university.id
                      ? 'opacity-100'
                      : 'opacity-0'
                  )}
                />
                <div className="flex-1 min-w-0">
                  <div className="truncate">{university.name || ''}</div>
                  {university.location && (
                    <div className="text-neutral-500 text-sm truncate">
                      {university.location}
                    </div>
                  )}
                </div>
              </CommandItem>
            ))}
        </CommandGroup>
      </CommandList>
    );
  };

  return (
    <Dialog
      open={isRequestModalOpen}
      onOpenChange={(open) => {
        setIsRequestModalOpen(open);
        if (!open) resetForm();
      }}
    >
      <DialogContent className="w-[850px] flex flex-col flex-wrap">
        <DialogHeader>
          <DialogTitle className="text-[20px] !text-blue-400 font-semibold leading-[28px]">
            Add Another Program
          </DialogTitle>
          <DialogDescription className="text-[18px] !text-neutral-500 font-normal leading-[24px]">
            Please provide the program details below.
          </DialogDescription>
        </DialogHeader>

        <div className="flex w-full flex-col md:flex-row gap-4 py-4">
          <div className="w-1/2">
            <Label htmlFor="university" className="text-[18px] font-semibold">
              University Name
            </Label>
            <Popover
              open={universityOpen}
              onOpenChange={(open) => {
                if (!isMounted) return;
                if (open && isUniversitiesLoading) return;
                setUniversityOpen(open);
              }}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={universityOpen}
                  className="w-full mt-2 max-w-full justify-between bg-white hover:bg-white rounded-md overflow-hidden"
                  disabled={isUniversitiesLoading || !isMounted}
                >
                  <div className="flex items-center truncate">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <span className="truncate">
                      {selectedUniversity
                        ? `${selectedUniversity.name}${selectedUniversity.location ? ` (${selectedUniversity.location})` : ''}`
                        : isUniversitiesLoading || !isMounted
                          ? 'Loading universities...'
                          : 'Select a university'}
                    </span>
                  </div>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <div className="flex items-center border-b px-3">
                    <CommandInput placeholder="Search universities..." />
                  </div>
                  {renderDropdownContent()}
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="w-1/2">
            <Label htmlFor="programName" className="text-[18px] font-semibold">
              Program Name
            </Label>
            <Input
              id="programName"
              placeholder="Program Name"
              className="mt-2"
              value={requestProgramName}
              onChange={(e) => setRequestProgramName(e.target.value)}
            />
          </div>
        </div>

        <div className="w-full">
          <Label htmlFor="url" className="text-[18px] font-semibold">
            URL to Program Page
          </Label>
          <Input
            id="url"
            placeholder="https://uaeu.ac.ae/en/catalog/undergraduate/programs/bachelor-of-business-administration"
            className="mt-2"
            value={programUrl}
            onChange={handleUrlChange}
          />
          {urlError && (
            <Text className="!text-destructive-500 text-sm mt-1">
              {urlError}
            </Text>
          )}
        </div>

        <div className="mt-4">
          <Text className="text-[16px] text-neutral-600">
            For your request to be fulfilled, the program page must contain the
            following details:
          </Text>
          <ul className="list-disc pl-5 mt-2 text-[16px] text-neutral-600 leading-[30px]">
            <li>Total course credits of program</li>
            <li>List of all courses in the program</li>
            <li>
              Detailed description or syllabus of each course, including credit
              hours per course
            </li>
          </ul>
        </div>

        <div className="mt-6 items-start justify-start flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsRequestModalOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmitRequest}
            disabled={!isFormValid() || isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
