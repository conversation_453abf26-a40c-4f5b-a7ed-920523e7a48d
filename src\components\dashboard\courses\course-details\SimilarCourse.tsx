'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { getCourses } from '@/zustand/services/course.services';
import type { Course } from '@/types/courseType';
import FallbackImage from '@/assets/images/ws/dashboard1.svg';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatUSD } from '@/utils';

interface SimilarCoursesTableProps {
  currentCourse: Course;
}

export default function SimilarCoursesTable({
  currentCourse,
}: SimilarCoursesTableProps) {
  const [similarCourses, setSimilarCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const fetchSimilarCourses = async () => {
    try {
      setIsLoading(true);
      const response = await getCourses({
        mainTopic: currentCourse.mainTopic,
        pageSize: 4,
      });

      const filteredCourses = response.data
        .filter((course) => course.id !== currentCourse.id)
        .slice(0, 4);

      setSimilarCourses(filteredCourses);
    } catch (error) {
      console.error('Error fetching similar courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (currentCourse?.mainTopic) {
      fetchSimilarCourses();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentCourse]);

  const getExperienceLevelColor = (experienceLevel?: string) => {
    switch (experienceLevel?.toLowerCase()) {
      case 'beginner':
        return 'bg-success-100 text-neutral-900';
      case 'intermediate':
        return 'bg-warning-100 text-neutral-900';
      case 'advanced':
        return 'bg-destructive-100 text-neutral-900';
      default:
        return 'bg-neutral-100 text-neutral-900';
    }
  };

  const truncateName = (name: string) => {
    return name.length > 30 ? `${name.substring(0, 30)}...` : name;
  };

  const handleViewAllCourses = () => {
    router.push('/dashboard/upskilling/all-courses');
  };

  if (isLoading) {
    return (
      <Card className="mt-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">
            <Skeleton className="h-6 w-[250px]" />
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, index) => (
              <Card
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                className="overflow-hidden border border-neutral-200 h-full flex flex-col rounded-lg"
              >
                <div className="p-4 flex flex-col h-full">
                  <Skeleton className="w-full h-64 rounded-md" />
                  <div className="flex-grow mt-4 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-1/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-6 w-20" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                    <div className="flex gap-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                    <Skeleton className="h-4 w-1/3" />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (similarCourses.length === 0) {
    return (
      <Card className="mt-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">
            Other recommended courses similar to{' '}
            <span className="capitalize">
              {truncateName(currentCourse.mainTopic)}
            </span>
          </h2>
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-neutral-500 text-lg">No similar courses found</p>
            <Button
              variant="outline"
              className="mt-4 px-8"
              onClick={handleViewAllCourses}
            >
              Browse all courses
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-8 rounded-lg">
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-4">
          Other recommended courses similar to{' '}
          <span className="capitalize">
            {truncateName(currentCourse.mainTopic)}
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {similarCourses.map((course) => (
            <Card
              key={course.id}
              className="overflow-hidden border border-neutral-200 h-full flex flex-col rounded-lg"
            >
              <div className="p-4 flex flex-col h-full">
                <div className="relative">
                  <Image
                    src={course.coverImage || FallbackImage}
                    alt={course.name}
                    width={400}
                    height={240}
                    className="w-full h-64 object-cover rounded-md"
                  />
                </div>

                <div className="mt-4">
                  <div className="relative h-[24px] w-[101px]">
                    <div className="relative h-full w-full">
                      <Image
                        src={course.partner?.logo || FallbackImage}
                        alt="Company logo"
                        fill
                        className="object-cover"
                        quality={100}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex-grow mt-4">
                  <h3 className="font-semibold text-[20px] mb-1 line-clamp-2 leading-[28px] text-neutral-900">
                    {course.name}
                  </h3>
                  <p className="text-[16px] text-neutral-600 mb-2">
                    {course.partner?.name || 'Training Provider Name'}
                  </p>
                  <div className="font-semibold text-[24px] text-neutral-700 leading-[24px] mb-4">
                    USD {formatUSD(course.courseFee || 0)}
                  </div>

                  <div className="flex items-center gap-2 mb-2">
                    <span
                      className={`px-2 py-1 rounded text-[16px] ${getExperienceLevelColor(course.experienceLevel)}`}
                    >
                      {course.experienceLevel || 'N/A'}
                    </span>
                    <span className="bg-neutral-100 px-2 py-1 rounded text-[16px]">
                      {course.deliveryMode || 'N/A'}
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-2 text-[16px] text-neutral-600 mb-3">
                    <span className="bg-neutral-100 px-2 py-1 rounded">
                      Next Start: {formatDate(course.startDate) || 'N/A'}
                    </span>
                    <span className="bg-neutral-100 px-2 py-1 rounded">
                      {course.duration} {course.durationUnit || 'N/A'}
                    </span>
                  </div>

                  <p className="text-[16px] text-neutral-600 mb-1 capitalize">
                    {course.city || 'Unknown City'}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-6 flex">
          <Button className="px-8" onClick={handleViewAllCourses}>
            Explore all courses
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
