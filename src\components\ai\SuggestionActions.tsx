interface SuggestedActionsProps {
  actions?: string[]
  onActionSelect?: (_action: string) => void
}

export const SuggestedActions = ({
  actions,
  onActionSelect,
}: SuggestedActionsProps) => {
  return (
    <div className="space-y-2">
      <p className="text-sm text-gray-500">- can I help with today?</p>
      <div className="flex flex-wrap gap-2">
        {(actions ?? []).map((action, index) => (
          <button
            key={index}
            onClick={() => onActionSelect?.(action)}
            className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full border border-gray-200"
          >
            {action}
          </button>
        ))}
      </div>
    </div>
  );
};
