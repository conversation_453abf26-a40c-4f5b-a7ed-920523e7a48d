'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { usePersonaStore } from '@/zustand/store/personaStore';
import personasData from '@/constants/PERSONA_DATA.json';

interface PersonaSelectorProps {
  onPersonaSelected?: () => void;
}

export default function PersonaSelector({ onPersonaSelected }: PersonaSelectorProps) {
  const { setSelectedPersona } = usePersonaStore();
  const [localSelectedPersona, setLocalSelectedPersona] = useState<
    string | null
  >(null);
  const personas = Object.values(personasData.personas);

  const handlePersonaSelect = (personaId: string) => {
    setLocalSelectedPersona(personaId);
  };

  const handleConfirmSelection = () => {
    if (localSelectedPersona) {
      setSelectedPersona(localSelectedPersona);
      if (onPersonaSelected) {
        onPersonaSelected();
      }
    }
  };

  return (
    <div className="mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Choose Your Career Path
        </h1>
        <p className="text-lg text-neutral-500">
          Select a persona to explore their personalized career roadmap
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {personas.map((persona) => (
          <Card
            key={persona.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg rounded-xl ${localSelectedPersona === persona.id
                ? 'ring-2 ring-blue-500 shadow-lg bg-blue-50'
                : 'hover:shadow-md border-gray-200'
              }`}
            onClick={() => handlePersonaSelect(persona.id)}
          >
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-4">{persona.avatar}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {persona.name}
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                {persona.description}
              </p>

              {/* Preview career path */}
              <div className="text-left space-y-2 mb-4">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Career Path Preview:
                </div>
                {persona.careerRoadmap.careerPath.roles
                  .slice(0, 2)
                  .map((role) => (
                    <div key={role.id} className="flex items-center text-sm">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${role.current ? 'bg-blue-500' : 'bg-gray-300'}`}
                      />
                      <span
                        className={
                          role.current
                            ? 'font-medium text-blue-700'
                            : 'text-gray-600'
                        }
                      >
                        {role.title}
                      </span>
                    </div>
                  ))}
              </div>

              <Button
                variant={
                  localSelectedPersona === persona.id ? 'default' : 'outline'
                }
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation();
                  handlePersonaSelect(persona.id);
                }}
              >
                {localSelectedPersona === persona.id
                  ? '✓ Selected'
                  : 'Select This Path'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Confirm Selection Button */}
      {localSelectedPersona && (
        <div className="text-center mt-8">
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Ready to Start Your Journey?
            </h3>
            <p className="text-gray-600 mb-4">
              You&apos;ve selected the{' '}
              <strong>
                {personas.find((p) => p.id === localSelectedPersona)?.name}
              </strong>{' '}
              career path. Click below to proceed to your personalized
              dashboard.
            </p>
            <Button
              onClick={handleConfirmSelection}
              size="lg"
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Start My Career Journey →
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
