import { Content } from '@/assets';
import Image from 'next/image';
import React from 'react';
import PartnerCard from './components/PartnerCard';
import { Button } from '../ui/button';
import ArrowRightWhiteIcon from '../icons/ArrowRightWhite';

const cardsData = [
  {
    id: 1,
    title: 'Manage Jobs & Training Opportunities',
    description:
      'Easily share job openings and apprenticeship programs with qualified candidates.',
  },
  {
    id: 2,
    title: 'Access to a Rich Talent Pool',
    description:
      'Browse detailed profiles of job seekers to find the right fit for your roles.',
  },
  {
    id: 3,
    title: 'Access Financial Benefits',
    description:
      'Access financial benefits information for your company, streamlining payments and eligibility.',
  },
];

function PotentialBenefits() {
  return (
    <section className="relative w-full h-[110vh] mb-12 bg-no-repeat bg-cove">
      <div className="absolute inset-0 flex flex-col  text-black fade-in-animation w-[80%] md:w[70%] mx-auto py-8">
        <p className="text-center text-primary-500 leading-[20px] text-[16px] font-semibold">
          Partner Benefits
        </p>
        <h3 className=" font-semibold text-neutral-900 leading-[44px] text-3xl mt-4 text-center w-[70%] mx-auto">
          What Can You Do as a Partner?
        </h3>
        <p className=" font-normal text-[18px] leading-[28px] text-center text-[#374151] text-neutral-700  w-[70%] mx-auto mt-4">
          As a partner company, you&apos;ll have everything you need to manage
          your recruitment, training, and up-skilling programs effectively—all
          in one place.
        </p>

        <div className="grid grid-cols-1 mt-10 sm:grid-cols-2 md:grid-cols-3 gap-8">
          {cardsData.map((card) => (
            <PartnerCard
              key={card.id}
              id={card.id}
              title={card.title}
              description={card.description}
            />
          ))}
        </div>

        <div className="flex mt-20 gap-8">
          <div className="w-[60%] space-y-4">
            <p className="font-semibold text-primary-500 text-[16px] leading-[20px] uppercase">
              Join a Community of Leading Employers{' '}
            </p>
            <div className="">
              <h3 className=" text-3xl text-neutral-900 leading-[44px] font-semibold">
                {' '}
                Become a Partner
              </h3>
              <p className="text-neutral-700 font-normal text-[16px]">
                As a partner company, you’ll have everything you need to manage
                your recruitment, training, and up-skilling programs
                effectively—all in one place. Here’s how you can make the most
                of the platform.
              </p>
            </div>
            <Button>
              Join Now
              <ArrowRightWhiteIcon />
            </Button>
          </div>

          <div className="w-[40%] relative">
            <Image src={Content} alt="ContentImage" />
          </div>
        </div>
      </div>
    </section>
  );
}

export default PotentialBenefits;
