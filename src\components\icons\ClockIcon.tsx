import React from 'react';

interface ClockIconProps {
  stroke?: string;
}

function ClockIcon({ stroke = 'var(--primary-500)' }: ClockIconProps) {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.84961 9.60579V15.6664H14.9102M7.33398 23.9997C7.33398 33.2044 14.7959 40.6663 24.0007 40.6663C33.2054 40.6663 40.6673 33.2044 40.6673 23.9997C40.6673 14.7949 33.2054 7.33301 24.0007 7.33301C17.8323 7.33301 12.4466 10.6839 9.56463 15.6648M24.0043 13.9997L24.0033 24.007L31.0694 31.0732"
        stroke={stroke}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default ClockIcon;
