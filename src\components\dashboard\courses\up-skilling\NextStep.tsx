import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react';

export default function NextStepCard() {
  return (
    <div className="bg-white p-4 rounded-lg border border-neutral-100 shadow-sm mb-8">
      <h2 className="font-medium mb-2">Up Next</h2>
      <div className="flex items-start">
        <div className="bg-blue-100 p-2 rounded-lg mr-3">
          <Info className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h3 className="font-medium">Take a career test</h3>
          <p className="text-[16px] text-gray-600">
            Discover your strengths, personality, and preferences to match with
            the right job for you.
          </p>
        </div>
      </div>
      <div className="flex justify-end mt-4 space-x-3">
        <Button variant="outline" size="sm">
          Skip This Step
        </Button>
        <Button variant={'default'}>Complete Now</Button>
      </div>
    </div>
  );
}
