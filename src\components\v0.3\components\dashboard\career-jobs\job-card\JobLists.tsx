'use client';

import type React from 'react';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import type { Job } from '@/types/jobTypes';
import useFormatSalary from '@/hooks/useFormatSalary';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { formatPostedDate, normalizeText } from '@/utils';
import FallbackImage from '@/components/icons/FallBackImage';
import useSettingsStore from '@/zustand/store/settingsStore';
import { useAppliedJobs } from '@/queries';

interface JobListProps {
  jobs: Job[];
  selectedJobId?: string;
  onJobSelect: (_job: Job) => void;
}

const JobList: React.FC<JobListProps> = ({
  jobs,
  selectedJobId,
  onJobSelect,
}) => {
  const { appearanceSettings } = useSettingsStore();
  const formatSalary = useFormatSalary();
  const { user } = useAuthStore();
  const { data } = useAppliedJobs({
    userId: user?.id,
  });

  const brandColor = appearanceSettings?.brandColor;

  const applicationStatusMap = new Map<string, string>();
  data?.data?.forEach((application) => {
    applicationStatusMap.set(application.jobId, application.status);
  });

  const getStatusBadge = (jobId: string) => {
    const status = applicationStatusMap.get(jobId);
    if (!status) return null;

    const statusDisplayText = status === 'Pending' ? 'Applied' : status;
    const statusStyles = {
      Pending: 'bg-transparent text-primary-500 border-primary-500',
      Shortlisted: 'bg-transparent text-primary-500 border-primary-500',
      Contacted: 'bg-transparent text-primary-500 border-primary-500',
      Rejected: 'bg-transparent text-destructive-500 border-destructive-500',
      Hired: 'bg-green-100 text-green-800',
    };

    return (
      <div
        className={`flex items-center justify-center border rounded-full h-[32px] px-3 ${
          statusStyles[status as keyof typeof statusStyles] ||
          'bg-neutral-100 text-neutral-900 border-neutral-100'
        }`}
      >
        <span className="text-[16px] capitalize">{statusDisplayText}</span>
      </div>
    );
  };

  return (
    <div className="space-y-4 px-2">
      {jobs.map((job) => (
        <Card
          key={job.id}
          className={`cursor-pointer p-4 border border-neutral-200 hover:bg-neutral-50 transition-colors rounded-lg ${
            selectedJobId === job.id
              ? 'border-2 border-primary-500 rounded-lg shadow-sm'
              : ''
          }`}
          onClick={() => onJobSelect(job)}
        >
          <div className="gap-3">
            <div className="flex justify-between">
              <div className="h-[48px] w-[84px] rounded-md flex items-center justify-center overflow-hidden mb-4">
                {job?.company?.logo ? (
                  <Image
                    src={job?.company?.logo || '/placeholder.svg'}
                    alt={`${job.companyName || 'Company'} logo`}
                    width={84}
                    height={48}
                    className="object-cover h-[48px] w-[84px]"
                  />
                ) : (
                  <FallbackImage stroke={brandColor} />
                )}
              </div>
              {applicationStatusMap.has(job.id) && getStatusBadge(job.id)}
            </div>

            <div className="flex-1">
              <h3 className="text-[16px] font-semibold line-clamp-1 text-neutral-900 leading-[24px] mb-2">
                {job.title}
              </h3>

              <div className="flex flex-wrap gap-2 mb-2">
                <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] rounded-md">
                  {formatSalary(job.monthlyFrom)}
                </Badge>
                <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] capitalize rounded-md">
                  {normalizeText(job.jobType || 'N/A')}
                </Badge>
                <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] capitalize rounded-md">
                  {job.jobMode || 'Not specified'}
                </Badge>
              </div>
              <p className="text-[16px] text-neutral-500 mb-2">
                {job.companyName || 'Unknown Company'} |{' '}
                {job.location || 'Location not specified'}
              </p>
              <p className="text-[16px] text-neutral-500">
                {formatPostedDate(job?.postedDate)}
              </p>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default JobList;
