import type { FC } from 'react';

export interface ISCOTabsProps {
  iscoType: string;
  handleChangeISCOType: (_iscoType: string) => void;
}

const iscoTabStyle =
  'inline-block cursor-pointer text-neutral-600 font-semibold border-b-[2px] border-b-[solid] border-b-[transparent] px-0 py-2';
const activeIscoTabStyle = ' !text-blue-400 !border-b-blue-400';

const ISCOTabs: FC<ISCOTabsProps> = ({ iscoType, handleChangeISCOType }) => {
  return (
    <div className="w-full">
      <button
        className={`${iscoTabStyle} ${iscoType === 'isco4' ? activeIscoTabStyle : ''}`}
        onClick={() => handleChangeISCOType('isco4')}
      >
        Job Categories
      </button>
      <button
        className={`ml-4 ${iscoTabStyle} ${iscoType === 'isco6' ? activeIscoTabStyle : ''}`}
        onClick={() => handleChangeISCOType('isco6')}
      >
        Job Titles
      </button>
    </div>
  );
};

export default ISCOTabs;
