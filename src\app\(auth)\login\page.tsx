'use client';

import Image from 'next/image';
import LoginFormTemplate from '@/components/auth/login/templates/LoginFormTemplate';
import { Card, CardContent } from '@/components/ui/card';

const PageLogin = () => {
  return (
    <div className="relative h-screen w-full flex flex-row">
      <Image
        src="/images/ws/login-bg.png"
        alt="Login Background"
        fill
        priority
        className="object-cover object-center -z-10"
      />

      <div className="w-full lg:w-1/2 z-10 mx-auto flex justify-center items-center">
        <Card className="max-w-[488px] flex-1 rounded-2xl px-5 py-12 md:px-12 min-h-screen md:min-h-0">
          <CardContent className="p-0 pt-6">
            <LoginFormTemplate />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PageLogin;
