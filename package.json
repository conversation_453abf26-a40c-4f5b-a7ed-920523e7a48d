{"name": "careernavigatorproui-beta", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier \"src/**/*.{js,jsx,ts,tsx,json,css,md}\" --write", "postinstall": "if [ \"$NODE_ENV\" != \"production\" ]; then husky install; fi || echo 'Skipping Husky'"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --cache --fix", "prettier --write"]}, "dependencies": {"@amcharts/amcharts5": "^5.11.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.66.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "formik": "^2.4.6", "framer-motion": "^12.4.7", "lucide-react": "^0.474.0", "next": "15.1.6", "next-themes": "^0.4.6", "react": "^19.0.0", "react-confetti": "^6.3.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "recharts": "^2.15.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@eslint/eslintrc": "^3.3.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.19.0", "eslint-config-next": "^15.3.2", "eslint-plugin-eslint-comments": "^3.2.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "npm@10.9.2"}