'use client';

import { But<PERSON> } from '@/components/ui/button';
import { MapPin, Search } from 'lucide-react';
import { useState } from 'react';

interface CourseSearchProps {
  onSearch: (_searchTerm: string) => void
  initialSearchTerm?: string
}

export default function CourseSearch({
  onSearch,
  initialSearchTerm = '',
}: CourseSearchProps) {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [location, setLocation] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const searchQuery = location
      ? `${searchTerm.trim()} ${location.trim()}`
      : searchTerm.trim();
    onSearch(searchQuery);
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-center w-full gap-4">
      <div className="relative rounded-full bg-gradient-to-r from-neutral-200 to-neutral-200 p-[2px] w-full">
        <div className="flex items-center w-full rounded-full bg-white px-4 py-2">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center flex-1">
              <Search className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none min-w-0"
                placeholder="Search a topic..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="h-6 w-1 bg-neutral-100 mx-2" />

            <div className="flex items-center flex-1">
              <MapPin className="h-6 w-6 text-neutral-400" />
              <input
                type="text"
                className="p-2 flex-1 focus:outline-none focus:ring-0 border-none min-w-0"
                placeholder="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>
      <Button
        className="h-[56px] py-3.5 px-7"
        type="submit"
        onClick={handleSubmit}
      >
        Search Courses
      </Button>
    </form>
  );
}
