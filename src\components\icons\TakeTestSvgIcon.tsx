'use client';

import React from 'react';

interface BookOpenIconProps {
  width?: number
  height?: number
  className?: string
  strokeColor?: string
}

const BookOpenIcon: React.FC<BookOpenIconProps> = ({
  width = 48,
  height = 48,
  className = '',
  strokeColor = '#043C5B',
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill="none"
      className={className}
    >
      <path
        d="M24 22.4423C21.4354 20.5463 17.592 17.2309 6.57258 17.2309V40.9703C17.592 40.9703 21.4354 44.2857 24 46.1817M24 22.4423V46.1817M24 22.4423C26.5646 20.5463 30.408 17.2309 41.4274 17.2309V40.9703C30.408 40.9703 26.5646 44.2857 24 46.1817M30.8092 1.9703V10.3086M36.9017 1.78516H14.0092C13.3696 1.78438 12.7425 1.96252 12.1989 2.29944L6.17487 6.0503L12.1989 9.7943C12.7425 10.1312 13.3696 10.3094 14.0092 10.3086H36.9017C37.4713 10.3247 38.0384 10.2265 38.5694 10.0197C39.1003 9.81288 39.5844 9.50168 39.993 9.10451C40.4016 8.70733 40.7264 8.23225 40.9482 7.70735C41.17 7.18246 41.2843 6.61842 41.2843 6.04859C41.2843 5.47876 41.17 4.91472 40.9482 4.38982C40.7264 3.86493 40.4016 3.38984 39.993 2.99267C39.5844 2.59549 39.1003 2.28429 38.5694 2.07748C38.0384 1.87066 37.4713 1.769 36.9017 1.78516Z"
        stroke={strokeColor}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default BookOpenIcon;
