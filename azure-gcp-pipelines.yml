trigger:
  branches:
    include: 
      - main

pool:
  vmImage: ubuntu-latest

steps:
  - script: 'echo Current Branch is $(Build.SourceBranchName)'
    displayName: Current Branch

  - script: ls -a
    displayName: Get List of Files

  - script: |
      echo "Setting up environment for $(Build.SourceBranch)"
      if [ "$(Build.SourceBranch)" == "refs/heads/main" ]; then
        echo "##vso[task.setvariable variable=VERSION]production"
      else
        echo "##vso[task.setvariable variable=VERSION]staging"
      fi
      echo "##vso[task.setvariable variable=BUILD_ID]$(Build.BuildId)"
    displayName: 'Set environment variables'

  - script: 'echo Deployment stage: $(VERSION)'
    displayName: 'Print deployment stage'

  - task: DownloadSecureFile@1
    inputs:
      secureFile: 'whiteshield-public-google-cloud-key.json'
    name: downloadKey

  - script: |
      gcloud auth activate-service-account --key-file $(downloadKey.secureFilePath)
      gcloud config set project careernavigator-430608
    displayName: 'Authenticate with Google Cloud'

  - task: SonarCloudPrepare@3
    inputs:
      SonarQube: 'SonarQube Cloud Service Connection'
      organization: 'ws-sonar'
      scannerMode: 'cli'
      configMode: 'manual'
      cliProjectKey: 'whiteshieldTech_CareerNavigatorProUI'
      cliProjectName: 'CareerNavigatorProUI'
      cliSources: '.'

  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Install Node.js'

  - script: |
      npm install
      npm run build
    displayName: 'npm install and build'

  - task: SonarCloudAnalyze@3
    inputs:
      jdkversion: 'JAVA_HOME_17_X64'

  - task: SonarCloudPublish@3
    inputs:
      pollingTimeoutSec: '300'

  - script: |
      gcloud auth configure-docker gcr.io -q
    displayName: 'Configure Docker for GCR'

  - script: |
      docker build -t gcr.io/careernavigator-430608/career-navigator-pro-ui:$(VERSION)-$(BUILD_ID) .
      docker push gcr.io/careernavigator-430608/career-navigator-pro-ui:$(VERSION)-$(BUILD_ID)
    displayName: 'Build and push Docker image to GCR'

  - script: |
      echo "Deploying using app.$(VERSION).yaml..."
      if [ -f "app.$(VERSION).yaml" ]; then
        gcloud app deploy app.$(VERSION).yaml --quiet --version $(VERSION)-$(BUILD_ID)
      else
        echo "ERROR: app.$(VERSION).yaml not found!"
        exit 1
      fi
    displayName: 'Deploy to Google App Engine (Docker with static YAML)'
