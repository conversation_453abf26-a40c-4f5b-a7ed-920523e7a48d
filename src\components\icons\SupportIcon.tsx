import React from 'react';

interface SupportIconProps {
  stroke?: string;
}

function SupportIcon({ stroke = '#4568DC' }: SupportIconProps) {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 16.5H33M15 22.5H24M4.5 25.5187C4.5 28.7208 6.74683 31.5081 9.91492 31.9739C12.1719 32.3058 14.4545 32.5591 16.7597 32.731C17.4601 32.7832 18.1003 33.1505 18.4899 33.7349L24 42L29.51 33.735C29.8996 33.1506 30.5399 32.7833 31.2403 32.7311C33.5455 32.5593 35.828 32.306 38.085 31.9742C41.2531 31.5084 43.5 28.7211 43.5 25.5189V13.4811C43.5 10.2789 41.2531 7.49165 38.085 7.02587C33.4881 6.35003 28.7852 6 24.0006 6C19.2155 6 14.5122 6.35009 9.91493 7.02605C6.74683 7.49187 4.5 10.2791 4.5 13.4813V25.5187Z"
        stroke="url(#paint0_linear_4110_28111)"
        strokeWidth={3}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4110_28111"
          x1="4.5"
          y1="6"
          x2="40.385"
          y2="44.8754"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={stroke} />
          <stop offset="1" stopColor={stroke} />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default SupportIcon;
