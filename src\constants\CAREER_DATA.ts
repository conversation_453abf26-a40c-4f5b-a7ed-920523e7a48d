import type {
  CareerLevel,
  GrowthStep,
  RoleStats,
  EducationLevel,
  SalaryBracket,
  Employer,
  Skill,
  JobTrend,
} from '@/types/career';

export const careerLevels: CareerLevel[] = [
  {
    id: '1',
    title: 'Junior Data Analyst (Current)',
    salaryRange: 'AED 10k-12.5k',
    isCurrent: true,
  },
  {
    id: '2',
    title: 'Data Analyst',
    salaryRange: 'AED 14k-16k',
  },
  {
    id: '3',
    title: 'Senior Data Analyst',
    salaryRange: 'AED 18k-22k',
  },
  {
    id: '4',
    title: 'Analytics Lead',
    salaryRange: 'AED 25k+',
  },
];

export const growthSteps: GrowthStep[] = [
  {
    id: '1',
    title: 'Build Core Skills',
    description: 'Master fundamental data analysis tools and techniques',
    tasks: [
      {
        id: '1-1',
        title: 'Complete an Intermediate SQL course',
        completed: true,
      },
      {
        id: '1-2',
        title: 'Learn basic data visualization (Tableau, Power BI, or Looker)',
        completed: false,
      },
      {
        id: '1-3',
        title: 'Practice Excel formulas & pivot tables',
        completed: false,
      },
    ],
  },
  {
    id: '2',
    title: 'Apply What You Learn',
    description: 'Put your skills into practice with real projects',
    tasks: [
      {
        id: '2-1',
        title: 'Complete 1 portfolio project (real or simulated)',
        completed: false,
      },
      { id: '2-2', title: 'Publish on GitHub or LinkedIn', completed: false },
      {
        id: '2-3',
        title: 'Write a short case study (what you learned, your process)',
        completed: false,
      },
    ],
  },
  {
    id: '3',
    title: 'Strengthen Communication',
    description: 'Develop skills to present data insights effectively',
    tasks: [
      {
        id: '3-1',
        title: 'Present 1 insight to a peer or mentor',
        completed: false,
      },
      {
        id: '3-2',
        title: 'Join a local or online data community',
        completed: false,
      },
      {
        id: '3-3',
        title: 'Practice explaining data to non-technical audiences',
        completed: false,
      },
    ],
  },
  {
    id: '4',
    title: 'Prove Readiness',
    description: 'Demonstrate your readiness for the next level',
    tasks: [
      {
        id: '4-1',
        title: 'Ask your manager or mentor for a growth review',
        completed: false,
      },
      {
        id: '4-2',
        title: 'Apply to 1 internal or external Data Analyst role',
        completed: false,
      },
      {
        id: '4-3',
        title: 'Update your CV with outcomes & metrics',
        completed: false,
      },
    ],
  },
];

export const roleStats: RoleStats = {
  jobPosts: 741,
  experienceRange: '1-3 years',
  description:
    'Software developers research, analyse and evaluate requirements for existing or new software applications and operating systems, and design, develop, test and maintain software solutions to meet these requirements.',
};

export const educationLevels: EducationLevel[] = [
  { level: 'Bachelor', percentage: 68, color: '#1e293b' },
  { level: 'Master', percentage: 16, color: '#334155' },
  { level: 'Diploma', percentage: 9, color: '#64748b' },
  { level: 'Doctorate', percentage: 5, color: '#94a3b8' },
];

export const salaryBrackets: SalaryBracket[] = [
  { level: 'Entry Level', amount: 401, period: 'Month' },
  { level: 'Mid Experience', amount: 1114, period: 'Month' },
  { level: 'High Experience', amount: 2449, period: 'Month' },
];

export const topEmployers: Employer[] = [
  { id: '1', name: 'Energy Jobline', jobCount: 309 },
  { id: '2', name: 'EPAM Anywhere', jobCount: 239 },
  { id: '3', name: 'Airswift', jobCount: 211 },
  { id: '4', name: 'Mondelez International', jobCount: 122 },
  { id: '5', name: 'EPAM Systems', jobCount: 107 },
];

export const topSkills: Skill[] = [
  {
    id: '1',
    name: 'Software Engineering',
    description:
      'Software engineering is a branch of computer science that involves the systematic application of engineering approaches to the development, operation, and maintenance of software systems.',
    demandPercentage: 100,
    category: 'primary',
  },
  {
    id: '2',
    name: 'Infrastructure',
    description:
      'Infrastructure refers to the fundamental facilities and systems serving an area, including the services and facilities necessary for its economy to function.',
    demandPercentage: 90,
    category: 'primary',
  },
  {
    id: '3',
    name: 'Leadership',
    description:
      'SQL or Structured Query Language is a programming language used to manage, manipulate and retrieve data stored in relational databases.',
    demandPercentage: 85,
    category: 'primary',
  },
];

export const jobTrends: JobTrend[] = [
  { month: 'Dec 23', count: 800 },
  { month: 'Jan 24', count: 1000 },
  { month: 'Feb 24', count: 900 },
  { month: 'Mar 24', count: 1400 },
];

export const otherSkills = [
  'SQL (Programming Language)',
  'Docker (Software)',
  'Angular (Web Framework)',
  'Node.js',
  'Python (Programming Language)',
  'TypeScript',
  'Management',
  'Integration',
  'Java (Programming Language)',
  'Writing',
  'Planning',
  'Research',
  'Collaboration',
  'Sales',
  'Written English',
];
