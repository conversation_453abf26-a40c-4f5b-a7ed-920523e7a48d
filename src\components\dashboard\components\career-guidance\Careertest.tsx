'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import CareerTestSvg from '@/components/icons/svg/CareerTestIconSvg';
import Text from '@/components/ui/text';
import type { Question } from '@/types';
import {
  useCurrentAssessment,
  useUpdateAssessment,
  useRevokeAssessment,
  useCreateAssessment,
} from '@/queries';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader } from '@/components/common/Loader';

const TOTAL_QUESTIONS = 60;
const QUESTIONS_PER_STEP = 5;
const TOTAL_STEPS = TOTAL_QUESTIONS / QUESTIONS_PER_STEP;

const answerOptions = [
  { value: '0', label: 'Strongly Dislike', number: '1' },
  { value: '1', label: 'Dislike', number: '2' },
  { value: '2', label: 'Neutral', number: '3' },
  { value: '3', label: 'Like', number: '4' },
  { value: '4', label: 'Strongly Like', number: '5' },
];

const steps = Array.from({ length: TOTAL_STEPS }, (_, i) => i + 1);
const stepsLabel = steps.map(
  (step) =>
    `${(step - 1) * QUESTIONS_PER_STEP + 1} - ${step * QUESTIONS_PER_STEP}`
);

const processInitialState = (questions: Question[]) => {
  const answers: Record<string, string> = {};
  let answeredCount = 0;

  const uniqueQuestions = new Set<string>();
  questions.forEach((question) => {
    if (!uniqueQuestions.has(question.id)) {
      uniqueQuestions.add(question.id);
      if (question.answerValue !== null) {
        answers[question.id] = String(question.answerValue);
        answeredCount++;
      }
    }
  });

  const allUnanswered = Object.keys(answers).length === 0;
  const currentStep = Math.floor(answeredCount / QUESTIONS_PER_STEP);

  return {
    answers,
    step: currentStep,
    showIntroduction: allUnanswered,
  };
};

export default function CareerTest() {
  const router = useRouter();
  const refToTop = useRef<HTMLDivElement>(null);

  const [selectedAnswers, setSelectedAnswers] = useState<
    Record<string, string>
  >({});
  const [activeStep, setActiveStep] = useState(0);
  const [isTestSaved, setIsTestSaved] = useState(true);
  const [showIntroduction, setShowIntroduction] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [initialized, setInitialized] = useState(false);

  const {
    data: questions = [],
    isLoading: isLoadingQuestions,
    isError: isErrorQuestions,
  } = useCurrentAssessment();

  const { mutate: createAssessment, isPending: isCreatingAssessment } =
    useCreateAssessment();
  const { mutate: updateAssessment, isPending: isUpdatingAssessment } =
    useUpdateAssessment();
  const { isPending: isRevokingAssessment } = useRevokeAssessment();

  const isLoading =
    isLoadingQuestions ||
    isCreatingAssessment ||
    isUpdatingAssessment ||
    isRevokingAssessment;

  useEffect(() => {
    if (questions.length > 0 && !initialized) {
      // Filter out duplicate questions before processing
      const uniqueQuestions = questions.filter(
        (question, index, self) =>
          index === self.findIndex((q) => q.id === question.id)
      );
      const initialState = processInitialState(uniqueQuestions);
      setSelectedAnswers(initialState.answers);
      setActiveStep(initialState.step);
      setShowIntroduction(initialState.showIntroduction);
      setInitialized(true);
    }
  }, [questions, initialized]);

  useEffect(() => {
    if (questions.length === 0 && !initialized) {
      createAssessment();
      setShowIntroduction(true);
      setInitialized(true);
    }
  }, [questions, initialized, createAssessment]);

  useEffect(() => {
    refToTop.current?.scrollIntoView({ behavior: 'smooth' });
  }, [activeStep]);

  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (!isTestSaved) {
      e.preventDefault();
      e.returnValue =
        'You have unsaved changes, are you sure you want to leave?';
    }
  };

  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTestSaved]);

  const handleAnswerSelection = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSelectedAnswers((prev) => ({ ...prev, [name]: value }));
    setIsTestSaved(false);
  };

  const handleStepNavigation = (direction: 'next' | 'prev') => {
    setActiveStep((prev) => (direction === 'next' ? prev + 1 : prev - 1));
  };

  const saveAssessment = async (isFinalSave = false) => {
    try {
      const answers = Object.entries(selectedAnswers).map(
        ([id, answerValue]) => ({
          id,
          answerValue: Number(answerValue),
        })
      );

      updateAssessment(answers, {
        onSuccess: () => {
          setIsTestSaved(true);
          if (isFinalSave) {
            router.push('/dashboard/career-guidance/career-test/results');
          } else {
            const progress = Math.floor(
              (Object.keys(selectedAnswers).length * 100) / TOTAL_QUESTIONS
            );
            router.push(
              `/dashboard/career-guidance/career-test/introduction?progress=${progress}`
            );
          }
          setIsDialogOpen(false);
        },
      });
    } catch (error) {
      console.error('Error saving assessment:', error);
    }
  };

  const renderQuestions = () => {
    // Filter out duplicate questions before rendering
    const uniqueQuestions = questions.filter(
      (question, index, self) =>
        index === self.findIndex((q) => q.id === question.id)
    );

    const startIndex = activeStep * QUESTIONS_PER_STEP;
    const currentQuestions = uniqueQuestions.slice(
      startIndex,
      startIndex + QUESTIONS_PER_STEP
    );

    return currentQuestions.map((question) => (
      <div key={question.id} className="mt-5">
        <Text className="w-full text-neutral-700 mb-3 font-semibold">
          {question.order}. {question.question}
        </Text>
        <div className="flex flex-wrap mb-2 w-full justify-between">
          {answerOptions.map((option) => (
            <div
              key={option.value}
              className="flex mb-5 w-full justify-between md:w-[calc((100%_-_32px)_/_5)]"
            >
              <Input
                className="peer sr-only"
                onChange={handleAnswerSelection}
                type="radio"
                id={`radio-${question.id}-${option.value}`}
                name={question.id}
                value={option.value}
                checked={selectedAnswers[question.id] === option.value}
              />
              <Label
                className="py-2 rounded-md cursor-pointer bg-neutral-50 text-neutral-600 w-full flex items-center text-sm flex-row px-4 md:justify-center md:px-1 md:flex-col hover:bg-blue-50 focus:outline-primary-500 peer-checked:bg-primary-500 peer-checked:!text-white text-center"
                htmlFor={`radio-${question.id}-${option.value}`}
              >
                {option.number} <br />
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      </div>
    ));
  };

  const renderIntroduction = () => (
    <div>
      <CareerTestSvg />
      <h2 className="text-xl font-bold text-neutral-700 mb-3">
        Welcome to the Career Test!
      </h2>
      <p className="text-neutral-600 mb-5">
        The test comprises {TOTAL_QUESTIONS} questions regarding various work
        activities commonly encountered in jobs. Take your time to carefully
        read each question and assess how you feel about engaging in each type
        of work.
      </p>
      <div className="flex flex-wrap justify-between md:flex-nowrap">
        {answerOptions.map((option) => (
          <div
            key={option.number}
            className="w-full bg-neutral-50 rounded-md flex flex-row justify-start py-2 items-center text-neutral-600 px-4 mb-3 md:justify-center md:flex-col md:mb-0 text-sm md:px-1 md:w-[18%]"
          >
            <span className="inline-block mr-1 md:mr-0">{option.number}</span>
            <span>{option.label}</span>
          </div>
        ))}
      </div>
      <p className="text-neutral-600 my-5">
        You can save your progress and come back later by clicking{' '}
        <span className="font-semibold text-neutral-600">Save and Exit</span>.
      </p>
      <div className="flex justify-end">
        <Button
          onClick={() => setShowIntroduction(false)}
          className="bg-blue-400 hover:bg-blue-500"
        >
          Next <ChevronRight />
        </Button>
      </div>
    </div>
  );

  const renderLoading = () => (
    <div className="min-h-screen flex justify-center items-center">
      <div className="text-center">
        <div className="flex justify-center items-center mb-4">
          <div>
            {' '}
            <Loader />{' '}
          </div>
        </div>
        <p className="text-blue-400 text-lg">
          Please wait while we prepare your Career Test....
        </p>
      </div>
    </div>
  );

  if (isLoadingQuestions) return renderLoading();
  if (isErrorQuestions) return <div>Error loading assessment</div>;

  const uniqueQuestions = questions.filter(
    (question, index, self) =>
      index === self.findIndex((q) => q.id === question.id)
  );
  const currentQuestionsAnswered = uniqueQuestions
    .slice(
      activeStep * QUESTIONS_PER_STEP,
      (activeStep + 1) * QUESTIONS_PER_STEP
    )
    .every((q) => selectedAnswers[q.id]);

  return (
    <div className="min-h-screen">
      <div ref={refToTop} />
      <div className="bg-transparent flex min-h-full justify-center items-center relative z-10 md:p-8">
        <div className="w-full bg-white p-4 rounded-none md:rounded-xl md:my-12 md:p-6 lg:w-[840px]">
          <div className="flex justify-between md:mb-5">
            <h1 className="text-2xl font-bold text-blue-400">Career Test</h1>
            <Button
              disabled={isLoading || showIntroduction}
              onClick={() => setIsDialogOpen(true)}
              variant="outline"
              className="border-blue-400 text-blue-400"
            >
              {isLoading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-400 border-t-transparent"></div>
                  Loading...
                </>
              ) : (
                'Save and Exit'
              )}
            </Button>
          </div>

          <p className="py-4 md:py-5 text-neutral-600 text-lg">
            {showIntroduction
              ? 'Introduction'
              : `Questions ${stepsLabel[activeStep]} of ${TOTAL_QUESTIONS}`}
          </p>

          {/* Progress bar */}
          <div className="mt-1 md:mt-0 bg-neutral-50 h-2 rounded-full overflow-hidden flex">
            {steps.map((step) => {
              const questionId =
                uniqueQuestions[(step - 1) * QUESTIONS_PER_STEP]?.id;
              const isCompleted =
                !showIntroduction && selectedAnswers[questionId] !== undefined;
              return (
                <div
                  key={step}
                  className={`h-full flex-1 ${isCompleted ? 'bg-blue-400' : 'bg-neutral-50'}`}
                />
              );
            })}
          </div>

          {showIntroduction ? renderIntroduction() : renderQuestions()}

          {!showIntroduction && (
            <div className="flex justify-between items-center mt-5">
              <Button
                variant="outline"
                disabled={activeStep === 0}
                onClick={() => handleStepNavigation('prev')}
                className="text-neutral-700"
              >
                <ChevronLeft /> Back
              </Button>

              <Button
                disabled={!currentQuestionsAnswered || isUpdatingAssessment}
                onClick={() => {
                  if (activeStep === TOTAL_STEPS - 1) {
                    saveAssessment(true);
                  } else {
                    handleStepNavigation('next');
                  }
                }}
                className="bg-blue-400 hover:bg-blue-500"
              >
                {activeStep === TOTAL_STEPS - 1 ? 'Finish' : 'Next'}
                <ChevronRight />
              </Button>
            </div>
          )}
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-blue-400 text-xl">
              Save & Exit
            </DialogTitle>
            <DialogDescription>
              You can resume your progress later.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-row justify-between gap-2">
            <Button
              onClick={() => saveAssessment(false)}
              className="flex-1 bg-blue-400 hover:bg-blue-500"
              disabled={isUpdatingAssessment}
            >
              {isUpdatingAssessment ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-400 border-t-transparent"></div>
                  Saving...
                </>
              ) : (
                'Save & Exit'
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              className="flex-1 border-blue-400 text-blue-400"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
