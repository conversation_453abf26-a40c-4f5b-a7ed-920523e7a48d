@tailwind base;
@tailwind components;
@tailwind utilities;

[contenteditable] ul,
[contenteditable] ol {
  padding-left: 1.5rem;
  list-style-position: inside;
}

[contenteditable] ul {
  list-style-type: disc;
}

[contenteditable] ol {
  list-style-type: decimal;
}

@layer base {
  :root {
    --radius: 0.5rem;

    --genericWhite: #ffffff;
    --genericBlack: #000000;
    --neutral_50: #f0f2f5;
    --neutral_100: #e2e4e9;
    --neutral_200: #d7dae0;
    --neutral_300: #c0c6ce;
    --neutral_400: #9ca2b0;
    --neutral_500: #646a77;
    --neutral_600: #4b5563;
    --neutral_700: #374151;
    --neutral_800: #1f2937;
    --neutral_900: #111827;
    --primary_50: #f3f5f7;
    --primary_100: #c8d0d9;
    --primary_200: #93a4b4;
    --primary_300: #5f7990;
    --primary_400: #2a516e;
    --primary_500Main: #043c5b;
    --primary_600: #0d3148;
    --primary_700: #0f2535;
    --primary_800: #0f1f2b;
    --primary_900: #0e1922;
    --success_50: #f4fce9;
    --success_100: #e5f8cb;
    --success_200: #c7f19b;
    --success_300: #96d763;
    --success_400: #66b039;
    --success_500Main: #2f7c0d;
    --success_600: #206a09;
    --success_700: #156006;
    --success_800: #0c4d04;
    --success_900: #043b02;
    --warning_50: #fefce2;
    --warning_100: #fdfacb;
    --warning_200: #fbf398;
    --warning_300: #f5e863;
    --warning_400: #ecd93c;
    --warning_500Main: #e0c602;
    --warning_600: #c0a701;
    --warning_700: #a18a01;
    --warning_800: #816e00;
    --warning_900: #6b5900;
    --destructive_50: #fceae8;
    --destructive_100: #f9d1cd;
    --destructive_200: #f49e9d;
    --destructive_400: #be3f56;
    --destructive_500Main: #931035;
    --destructive_600: #7e0b37;
    --destructive_700: #690836;
    --destructive_800: #550533;
    --destructive_900: #460330;

    /* Appearance settings colors */
    --topBarBgColor: #2a516e;
    --topBarBtnColor: #ffffff;
    --navMenuColor: #043c5b;
    --navMenuColorOpacity10: #2a516e;
    --buttonColor: #043c5b;
    --buttonColorDark: #0d3148;
    --buttonColorLight: #043c5b;
    --buttonStyle: 50px;
    --headingFont: 'Montserrat';
    --bodyTextFont: 'Montserrat';
    --headingTextColor: #111827;
    --bodyTextColor: #374151;
  }
}

::-webkit-scrollbar {
  width: 8px;
  color: #c0c6ce;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--headingFont);
  color: var(--headingTextColor) !important;
}

p,
span,
li,
label,
textarea,
select,
th,
td,
link,
option,
div,
a {
  font-family: var(--bodyTextFont);
  color: var(--bodyTextColor) !important;
}

::-webkit-scrollbar-thumb {
  background: #c0c6ce;
  border-radius: 10px;
}
