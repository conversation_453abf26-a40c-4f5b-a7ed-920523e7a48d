/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Upload, Eye, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useApplicationStore } from '@/zustand/store/userActionStore';
import { useCvs } from '@/queries';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import UploadStep from '../../../job-application/UploadStep';
import UserCardIcon from '@/components/icons/UploadSvgIcon';
import useSettingsStore from '@/zustand/store/settingsStore';
import type { ProfileStep } from '../ProfileCompleteness';
interface UploadCVStepProps {
  step: ProfileStep;
  onComplete: () => void;
  showModal: boolean;
  setShowModal: (_show: boolean) => void;
}

// eslint-disable-next-line no-unused-vars
interface Skill {
  name: string;
  selected: boolean;
}

const UploadCVStep: React.FC<UploadCVStepProps> = ({
  step,
  onComplete,
  showModal,
  setShowModal,
}) => {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  const [showCvError, setShowCvError] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showUpload, setShowUpload] = useState(true);
  const [extractedSkills, setExtractedSkills] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<{
    fileName: string;
    fileSize: string;
    date: string;
  } | null>(null);

  const { selectedDocumentId } = useApplicationStore((state) => state);
  const userId = useAuthStore((state) => state.user?.id);
  const { data: myCvs, isLoading } = useCvs(userId as string);

  const extractSkillsFromCV = () => {
    return [
      'Angular (Web Framework)',
      'Blockchain',
      'Bootstrap (Front-End Framework)',
      'Finance',
      'Market Research',
      'Ideation',
      'Cybersecurity',
      'Blockchain',
      'Artificial Intelligence & Machine Learning',
      'Project Management',
      'Blockchain Knowledge',
      'Financial Literacy',
      'Agile Methodologies',
    ];
  };

  // Check if CV exists when modal is opened
  useEffect(() => {
    if (showModal && !isLoading && myCvs?.data && myCvs.data.length > 0) {
      setShowUpload(false);
      setShowConfirmation(true);

      const cvToUse = selectedDocumentId
        ? myCvs.data.find((cv: any) => cv.id === selectedDocumentId)
        : myCvs.data[0];

      if (cvToUse) {
        const fileSizeInKB = Math.round((cvToUse.size || 525000) / 1024);

        const date = new Date(cvToUse.createdOn || new Date());
        const formattedDate = `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;

        setSelectedFile({
          fileName: cvToUse.fileName || 'john-smith-cv.pdf',
          fileSize: `${fileSizeInKB}KB`,
          date: formattedDate,
        });

        setExtractedSkills(extractSkillsFromCV());
      }
    } else if (showModal) {
      setShowUpload(true);
      setShowConfirmation(false);
    }
  }, [showModal, isLoading, myCvs, selectedDocumentId]);

  useEffect(() => {
    if (selectedDocumentId && myCvs?.data) {
      const selectedCV = myCvs.data.find(
        (cv: any) => cv.id === selectedDocumentId
      );
      if (selectedCV) {
        const fileSizeInKB = Math.round((selectedCV.size || 525000) / 1024);

        const date = new Date(selectedCV.createdOn || new Date());
        const formattedDate = `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;

        setSelectedFile({
          fileName: selectedCV.fileName || 'john-smith-cv.pdf',
          fileSize: `${fileSizeInKB}KB`,
          date: formattedDate,
        });

        setExtractedSkills(extractSkillsFromCV());
      }
    }
  }, [selectedDocumentId, myCvs?.data]);

  const handleContinue = () => {
    if (!selectedDocumentId) {
      setShowCvError(true);
      return;
    }
    setShowCvError(false);
    setShowConfirmation(true);
    setShowUpload(false);
  };

  const handleAddToProfile = () => {
    setShowConfirmation(false);
    setShowModal(false);
    onComplete();
  };

  const handleUploadNew = () => {
    setShowConfirmation(false);
    setShowUpload(true);
  };

  return (
    <>
      <div className="mb-4">
        <h3 className="font-medium text-neutral-900 mb-4">Up Next:</h3>

        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 p-2 bg-primary-50 rounded-lg">
            <UserCardIcon strokeColor={brandColor} />
          </div>

          <div className="flex-1">
            <h4 className="font-semibold text-[18px] text-neutral-900">
              {step?.title}
            </h4>
            <p className="text-neutral-900 text-[16px] mt-1 font-normal">
              {step?.description}
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 mt-6">
        <Button
          className="bg-primary-500 hover:bg-primary-600 text-white"
          onClick={() => setShowModal(true)}
        >
          Upload Now!
        </Button>
      </div>

      {/* Upload CV Modal */}
      <Dialog
        open={showModal && showUpload && !showConfirmation}
        onOpenChange={(open) => {
          if (!open) {
            setShowModal(false);
            setShowConfirmation(false);
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <UploadStep showCvError={showCvError} />

          <div className="flex justify-end gap-3 mt-4">
            <Button
              variant="outline"
              className="rounded-full"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-primary-500 hover:bg-primary-600 text-white"
              onClick={handleContinue}
            >
              Continue
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Modal */}
      <Dialog
        open={showModal && showConfirmation}
        onOpenChange={(open) => {
          if (!open) {
            setShowModal(false);
            setShowConfirmation(false);
          }
        }}
      >
        <DialogContent className="max-w-[776px] p-6 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Confirm CV Upload</h2>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-full"
              onClick={() => setShowConfirmation(false)}
            >
              <span className="sr-only">Close</span>
            </Button>
          </div>

          {selectedFile && (
            <div className="border border-neutral-200 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between">
                <div className="text-base font-medium">
                  {selectedFile.fileName}
                </div>
                <Eye className="h-6 w-6 text-neutral-500 items-center" />
              </div>
              <div className="text-sm text-neutral-500">
                {selectedFile.fileSize} • {selectedFile.date}
              </div>
            </div>
          )}

          <Button
            variant="default"
            className="mb-4 flex items-center gap-2 w-1/2"
            onClick={handleUploadNew}
          >
            <Upload size={18} />
            Upload a new CV
          </Button>

          <div className="space-y-3">
            <p className="text-[16px] font-normal text-neutral-900">
              We&apos;ve identified the following new skills from your CV.
              Review them before adding to your profile. You can also add more
              skills later in your Skills Profile section.
            </p>

            <div className="flex flex-wrap gap-2 mt-3">
              {extractedSkills.map((skill, index) => (
                <div
                  key={skill}
                  className="bg-neutral-200 text-neutral-900 px-3 py-1 rounded-md text-[14px] flex items-center gap-1"
                >
                  {skill}
                  <button
                    className="ml-1 text-neutral-500 hover:text-neutral-900"
                    onClick={() => {
                      setExtractedSkills((skills) =>
                        skills.filter((_, i) => i !== index)
                      );
                    }}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 mt-4">
            <Button
              variant="outline"
              className="rounded-full"
              onClick={() => setShowConfirmation(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleAddToProfile}>Add to Profile</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadCVStep;
