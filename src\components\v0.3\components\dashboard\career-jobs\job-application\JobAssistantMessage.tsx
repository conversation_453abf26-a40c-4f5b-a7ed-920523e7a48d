'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import AssistantTooltip from '@/components/common/AssistantTooltip';
import { RxCaretLeft, RxCaretRight } from 'react-icons/rx';
import Stepper from '@/components/auth/onboarding-stepper/Stepper';
import UploadStep from './UploadStep';
import ContactForm from './ContactForm';
import SuccessModal from '@/components/modals/SuccessModal';
import { useApplicationStore } from '@/zustand/store/userActionStore';
import { useParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function FileUpload() {
  const [activeStep, setActiveStep] = React.useState(0);
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCvError, setShowCvError] = useState(false);
  const applyForJob = useApplicationStore((state) => state.applyForJob);
  const params = useParams();
  const { selectedDocumentId } = useApplicationStore((state) => state);

  const handleNext = () => {
    if (!selectedDocumentId) {
      setShowCvError(true);
      return;
    }
    setShowCvError(false);
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setShowCvError(false);
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const steps = [
    {
      description:
        'Thank you for your interest in the job! First, please select the CV that you would like to apply with.',
    },
    {
      description:
        "Next, please review the contact information we have on file. If it is different, click the 'Update My Contact' button to update it.",
    },
  ];

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedDocumentId) {
      setShowCvError(true);
      return;
    }

    setIsSubmitting(true);
    try {
      await applyForJob(params.id as string);
      setOpen(true);
    } catch (error) {
      console.error('Failed to apply for job:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center p-4">
      <div className="w-full max-w-[41rem]">
        <div className="mt-10 text-left px-4">
          <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
            <p className="mt-2 text-[18px] font-normal text-neutral-700 text-left px-6 pt-6">
              {steps[activeStep]?.description}
            </p>

            {activeStep === 0 && <UploadStep showCvError={showCvError} />}
            {activeStep === 1 && <ContactForm />}
          </AssistantTooltip>
        </div>

        <div className="mt-6 flex justify-between space-x-4 px-4 mx-auto">
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
            variant="outline"
            className="px-4 py-2 flex items-center"
          >
            <RxCaretLeft />
            Back
          </Button>
          <Stepper currentStep={activeStep} steps={steps.length} />
          {activeStep === steps.length - 1 ? (
            <Button
              onClick={onSubmit}
              disabled={isSubmitting || !selectedDocumentId}
              className="px-4 py-2 flex items-center justify-center min-w-[80px]"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <Loader2 className="animate-spin mr-2 !text-white" />
                  Submitting...
                </div>
              ) : (
                'Submit'
              )}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!selectedDocumentId}
              className="px-4 py-2 flex items-center"
            >
              Next
              <RxCaretRight />
            </Button>
          )}
        </div>
      </div>

      <SuccessModal open={open} setOpen={setOpen} />
    </div>
  );
}
