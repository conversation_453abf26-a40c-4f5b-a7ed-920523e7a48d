'use client';

import { Card } from '@/components/ui/card';
import Text from '@/components/ui/text';
import Title from '@/components/ui/title';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import ProgramAccordion from './accordion';
import DnD from './dnd';
import type { Course } from '@/types';
import { Loader } from '@/components/common/Loader';
import { useGetProgramId, useGetUserCourses } from '@/queries';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ProfileCompletionProcess from '@/components/dashboard/career-jobs/job-card/career-profile-completeness/ProfileCompleteness';

interface ProgramDetailsPageProps {
  params: Promise<{ id: string }>;
}

const ProgramDetailsPage: React.FC<ProgramDetailsPageProps> = ({ params }) => {
  const unwrappedParams = React.use(
    params as unknown as Promise<{ id: string }>
  );
  const programId = unwrappedParams.id;

  const router = useRouter();
  const searchParams = useSearchParams();
  const iscoTypeParam = searchParams.get('iscoType');

  const [profileCompleted, setProfileCompleted] = useState(false);

  const handleProfileComplete = () => {
    setProfileCompleted(true);
  };

  const isInitialMount = useRef(true);
  const [iscoType, setISCOType] = useState<string>('isco4');
  const [selectedCourseIds, setSelectedCourseIds] = useState<string[]>([]);
  const [escoSkills, setEscoSkills] = useState<string[]>([]);
  const [shouldFetchProgram, setShouldFetchProgram] = useState<boolean>(false);

  const { data: userCoursesData, isLoading: isUserCoursesLoading } =
    useGetUserCourses();

  const {
    data: programData,
    isLoading: isProgramLoading,
    error: programsError,
  } = useGetProgramId(programId);

  console.warn(
    selectedCourseIds,
    escoSkills,
    shouldFetchProgram,
    profileCompleted
  );

  useEffect(() => {
    if (isInitialMount.current) {
      const currentISCOType = iscoTypeParam || iscoType;
      setISCOType(currentISCOType);
      setShouldFetchProgram(true);
      isInitialMount.current = false;
    }
  }, [iscoTypeParam, iscoType]);

  useEffect(() => {
    if (programData && userCoursesData) {
      const userCourses = Array.isArray(userCoursesData)
        ? userCoursesData.map((item: { course: Course }) => item.course.id)
        : [];

      const mandatoryCourseIds =
        programData?.data?.courses
          ?.filter((course: Course) => course.type === 'Mandatory')
          .map((course: Course) => course.id) || [];

      const selectedIds = userCourses.filter(
        (id) => !mandatoryCourseIds.includes(id)
      );
      setSelectedCourseIds(selectedIds);

      const filteredCourses =
        programData?.data?.courses?.filter(
          (course: Course) =>
            course.type === 'Mandatory' || selectedIds.includes(course.id)
        ) || [];

      const uniqueEscoSkills = getDistinctEscoSkills(filteredCourses);
      setEscoSkills(uniqueEscoSkills);
    }
  }, [programData, userCoursesData]);

  const getDistinctEscoSkills = (courses: Course[]): string[] => {
    const allEscoSkills =
      courses?.flatMap((course) => (course.escoSkills || '').split(';')) || [];
    return Array.from(new Set(allEscoSkills?.filter((item) => item !== '')));
  };

  const navigateToProgramsList = () => {
    router.push(`?backToProgramDetails=true`);
  };

  const isDetailsLoading = isUserCoursesLoading || isProgramLoading;

  const details = programData?.data;

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="px-2 py-4 relative z-10 min-h-full">
      <div className="mb-6">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'Career Guidance',
              href: '/dashboard/career-guidance/career-test',
            },
            {
              label: 'Program Builder',
              href: '/dashboard/career-guidance/program-builder',
            },
            {
              label: 'My Study Path',
              href: `/dashboard/career-guidance/program-builder/${programId}`,
            },
          ]}
        />

        <Button
          className="bg-transparent py-6 px-6 my-4 border !border-primary-500 cursor-pointer hover:bg-transparent "
          onClick={handleBack}
        >
          <ChevronLeft className="h-8 w-8 mr-1 !text-primary-500" />
          <p>Back</p>
        </Button>
      </div>
      <div className=" !p-0">
        <Card className="p-4 rounded-md">
          {isDetailsLoading ? (
            <div className="flex items-center justify-center h-[400px]">
              <Loader />
            </div>
          ) : programsError ? (
            <div className="flex flex-col items-center justify-center h-[400px]">
              <Text className="text-red-500 text-lg mb-4">
                Error loading program details. Please try again.
              </Text>
              <Button onClick={() => setShouldFetchProgram(true)}>Retry</Button>
            </div>
          ) : !programData ? (
            <div className="flex flex-col items-center justify-center h-[400px]">
              <Text className="text-neutral-500 text-lg mb-4">
                No program data found.
              </Text>
              <Button onClick={navigateToProgramsList}>Back to Programs</Button>
            </div>
          ) : (
            <div className="w-full">
              <div className="">
                <Title
                  className="hidden mt-4 text-blue-400 md:block md:mt-6"
                  variant="h2"
                >
                  {details?.name}
                </Title>
                <Title
                  className="mt-4 text-blue-400 md:hidden md:mt-6"
                  variant="h3"
                >
                  {details?.name}
                </Title>

                <Text
                  className="text-neutral-600 font-semibold mt-4"
                  variant="large"
                >
                  {details?.code || 'Program Code'}
                </Text>

                <Text className="text-neutral-600 mt-4 md:mt-6">
                  {details?.summary}
                </Text>
              </div>

              <div className="flex mt-4 flex-wrap md:flex-nowrap md:mt-6">
                <div className="w-full flex flex-col text-neutral-600 mt-4 md:mt-0 md:w-1/4">
                  <Text className="font-semibold">Faculty:</Text>
                  <Text className="max-w-[70%]">
                    {details?.department || 'N/A'}
                  </Text>
                </div>
                <div className="w-full flex flex-col text-neutral-600 mt-4 md:mt-0 md:w-1/4">
                  <Text className="font-semibold">Delivery Mode:</Text>
                  <Text>
                    {details?.isFullTime ? 'Full time' : ''}
                    {details?.isOnCampus ? ', on-campus' : ''}
                  </Text>
                </div>
                <div className="w-full flex flex-col text-neutral-600 mt-4 md:mt-0 md:w-1/4">
                  <Text className="font-semibold">Duration:</Text>
                  <Text>
                    {details?.durationAmount || 'N/A'} {details?.durationPeriod}
                  </Text>
                </div>
                <div className="w-full flex flex-col text-neutral-600 mt-4 md:mt-0 md:w-1/4">
                  <Text className="font-semibold">Total ECTS credit:</Text>
                  <Text>{details?.totalCredits || 0}</Text>
                </div>
              </div>

              <div>
                {programData && (
                  <ProgramAccordion
                    label="Mandatory Courses"
                    data={programData}
                    type="Mandatory"
                  />
                )}
              </div>
              <div>
                {programData && (
                  <DnD
                    onSave={(courseIds: string[]) => {
                      setSelectedCourseIds(courseIds);
                    }}
                    data={programData}
                    userCourses={
                      Array.isArray(userCoursesData)
                        ? userCoursesData.map((item: { course: Course }) => ({
                            course: item.course,
                          }))
                        : []
                    }
                  />
                )}
              </div>
            </div>
          )}
        </Card>

        <ProfileCompletionProcess
          showSearchInput={false}
          onComplete={handleProfileComplete}
          showEducationProgramStep={false}
        />
      </div>
    </div>
  );
};

export default ProgramDetailsPage;
