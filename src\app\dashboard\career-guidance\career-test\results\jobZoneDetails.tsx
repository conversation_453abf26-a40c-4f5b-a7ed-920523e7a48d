// 'use client';

// import type React from 'react';

// import LoaderImage from '@/assets/loader.gif';
// import Button from '@/components/common/Buttons';

// import Text from '@/components/ui/text';
// import Title from '@/components/ui/title';
// import Image from 'next/image';

// import { useRouter } from 'next/navigation';
// import ISCOTabs from '@/components/common/IscoTabs';
// import { Card } from '@/components/ui/card';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import type { JobZone, ISCO } from '@/types/index';

// interface JobZoneDetailsProps {
//   jobZones: JobZone[];
//   selectedJobZone: JobZone;
//   onSelectionChange: (_selectedItem: JobZone | null) => void;
//   onReportDownload: () => void;
//   reportLoading: boolean;
//   iscoType: string;
//   handleChangeISCOType: (_iscoType: string) => void;
// }

// const JobZoneDetails: React.FC<JobZoneDetailsProps> = ({
//   reportLoading,
//   onReportDownload,
//   jobZones,
//   selectedJobZone,
//   onSelectionChange,
//   iscoType,
//   handleChangeISCOType,
// }) => {
//   const router = useRouter();

//   const sortObjectsByFitCategory = (iscos: ISCO[]) => {
//     const fitCategoryOrder = {
//       'Best Fit': 2,
//       'Great Fit': 1,
//       'Good Fit': 0,
//     };

//     return iscos.sort((obj1, obj2) => {
//       const category1 = fitCategoryOrder[obj1.fitCategory] || -1;
//       const category2 = fitCategoryOrder[obj2.fitCategory] || -1;
//       return category2 - category1; // Sort in descending order (Best Fit first)
//     });
//   };

//   const navigateToJobDetails = (
//     e: React.MouseEvent<HTMLAnchorElement>,
//     code: string
//   ) => {
//     e.preventDefault();
//     router.push(
//       `/dashboard/career-guidance/career-test/results/job-details?code=${code}&jobZone=${selectedJobZone.code}&iscoType=${iscoType}`
//     );
//   };

//   return (
//     <>
//       <div>
//         <div className="mt-1 mb-4 md:mb-6 lg:hidden">
//           <a
//             href="#"
//             onClick={(e) => {
//               onSelectionChange(null);
//               e.preventDefault();
//             }}
//           >
//             <Text className="text-neutral-700 inline font-semibold hover:text-blue-400 active:text-blue-300">
//               Career Test
//             </Text>
//           </a>
//           <span className="inline-block mx-2">/</span>
//           {selectedJobZone ? (
//             <Text className="text-neutral-700 inline font-semibold">
//               Job Zone {selectedJobZone.code}
//             </Text>
//           ) : null}
//         </div>
//         <div className="flex flex-wrap md:justify-between">
//           {selectedJobZone ? (
//             <Title
//               className="hidden order-1 lg:block text-blue-400"
//               variant="h5"
//             >
//               Job Zone {selectedJobZone.code}
//             </Title>
//           ) : null}
//           {jobZones && selectedJobZone ? (
//             <div className="order-1 lg:hidden">
//               <Select
//                 value={selectedJobZone.code.toString()}
//                 onValueChange={(value) => {
//                   const code = Number.parseInt(value, 10);
//                   onSelectionChange(
//                     jobZones.find(
//                       (item: JobZone) => item.code === code
//                     ) as JobZone
//                   );
//                 }}
//               >
//                 <SelectTrigger className="border-none p-0 text-blue-400 text-xl font-semibold">
//                   <SelectValue placeholder="Select Job Zone" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   {jobZones.map((item: JobZone) => (
//                     <SelectItem key={item.code} value={item.code.toString()}>
//                       Job Zone {item.code}
//                     </SelectItem>
//                   ))}
//                 </SelectContent>
//               </Select>
//             </div>
//           ) : null}
//           <Text className="mt-3 mb-4 text-neutral-600 w-full order-2 md:order-3">
//             {selectedJobZone.name}
//           </Text>
//           <Button
//             className="order-3 md:order-2"
//             variant="secondary"
//             size="medium"
//             onClick={onReportDownload}
//             disabled={reportLoading}
//             icon={
//               reportLoading ? (
//                 <Image
//                   src={LoaderImage || '/placeholder.svg'}
//                   alt="Loading..."
//                   className="mx-auto d-block"
//                   style={{ width: 24 }}
//                   height={24}
//                 />
//               ) : (
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   width="24"
//                   height="24"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                 >
//                   <path
//                     d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M7 10L12 15M12 15L17 10M12 15V3"
//                     strokeWidth="1.33333"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                 </svg>
//               )
//             }
//           >
//             Download Job Zone Report
//           </Button>
//         </div>
//         <div className="flex flex-wrap md:justify-between">
//           <Card className="mt-4 w-full md:mt-6 md:w-[calc(50%_-_10px)]">
//             <Text
//               variant="large"
//               className="font-semibold text-neutral-700 mb-2"
//             >
//               Experience
//             </Text>
//             <Text className="text-neutral-600">
//               {selectedJobZone.experience}
//             </Text>
//           </Card>
//           <Card className="mt-4 w-full md:mt-6 md:w-[calc(50%_-_10px)]">
//             <Text
//               variant="large"
//               className="font-semibold text-neutral-700 mb-2"
//             >
//               Education
//             </Text>
//             <Text className="text-neutral-600">
//               {selectedJobZone.education}
//             </Text>
//           </Card>
//           <Card className="mt-4 w-full md:mt-6 md:w-[calc(50%_-_10px)]">
//             <Text
//               variant="large"
//               className="font-semibold text-neutral-700 mb-2"
//             >
//               Training
//             </Text>
//             <Text className="text-neutral-600">{selectedJobZone.training}</Text>
//           </Card>
//           <Card className="mt-4 w-full md:mt-6 md:w-[calc(50%_-_10px)]">
//             <Text
//               variant="large"
//               className="font-semibold text-neutral-700 mb-2"
//             >
//               Examples
//             </Text>
//             <Text className="text-neutral-600">{selectedJobZone.examples}</Text>
//           </Card>
//         </div>
//         <Text
//           variant="large"
//           className="font-semibold text-neutral-700 my-4 md:my-6"
//         >
//           {selectedJobZone?.iscos?.length} careers that fit your interests in
//           Job Zone {selectedJobZone.code}.
//         </Text>
//         <div className="mb-4 lg:mb-6">
//           <ISCOTabs
//             iscoType={iscoType}
//             handleChangeISCOType={handleChangeISCOType}
//           />
//         </div>
//         <div>
//           {(selectedJobZone && selectedJobZone.iscos
//             ? sortObjectsByFitCategory(selectedJobZone.iscos)
//             : []
//           ).map((item: ISCO) => (
//             <div
//               key={item.iscoCode}
//               className="p-4 md:py-6 border-b border-neutral-200 first:border-t"
//             >
//               <div className="flex flex-wrap items-baseline">
//                 <Title
//                   className="hover:cursor-pointer text-blue-400 underline inline-block mr-3 mb-3"
//                   variant="h5"
//                 >
//                   <a
//                     href="#"
//                     onClick={(e) => {
//                       navigateToJobDetails(e, item.iscoCode);
//                     }}
//                   >
//                     {item.iscoLabel}
//                   </a>
//                 </Title>
//                 {item.fitCategory ? (
//                   <div
//                     className={`
//                       inline-flex h-[32px] items-center px-2 rounded-[5px] mb-4 ml-0 first:ml-0 ml-3
//                       ${item.fitCategory === 'Best Fit' ? 'bg-blue-400' : ''}
//                       ${item.fitCategory === 'Great Fit' ? 'bg-neutral-50' : ''}
//                       ${item.fitCategory === 'Good Fit' ? 'bg-neutral-200' : ''}
//                     `}
//                   >
//                     <svg
//                       width="24"
//                       height="24"
//                       viewBox="0 0 24 24"
//                       fill="none"
//                       xmlns="http://www.w3.org/2000/svg"
//                     >
//                       <path
//                         className={`
//                           ${item.fitCategory === 'Best Fit' ? 'fill-white' : ''}
//                           ${item.fitCategory === 'Great Fit' ? 'fill-blue-400' : ''}
//                           ${item.fitCategory === 'Good Fit' ? 'fill-neutral-600' : ''}
//                         `}
//                         d="M23 12L20.56 9.22001L20.9 5.54001L17.29 4.72001L15.4 1.54001L12 3.00001L8.6 1.54001L6.71 4.72001L3.1 5.53001L3.44 9.21001L1 12L3.44 14.78L3.1 18.47L6.71 19.29L8.6 22.47L12 21L15.4 22.46L17.29 19.28L20.9 18.46L20.56 14.78L23 12ZM10 17L6 13L7.41 11.59L10 14.17L16.59 7.58001L18 9.00001L10 17Z"
//                       />
//                     </svg>
//                     <Text
//                       className={`
//                         font-semibold ml-1
//                         ${item.fitCategory === 'Best Fit' ? 'text-white' : ''}
//                         ${item.fitCategory === 'Great Fit' ? 'text-blue-400' : ''}
//                         ${item.fitCategory === 'Good Fit' ? 'text-neutral-600' : ''}
//                       `}
//                       variant="small"
//                     >
//                       {item.fitCategory}
//                     </Text>
//                   </div>
//                 ) : null}
//               </div>
//               <Text className="text-neutral-600">{item.iscoDecription}</Text>
//             </div>
//           ))}
//         </div>
//       </div>
//     </>
//   );
// };

// export default JobZoneDetails;
