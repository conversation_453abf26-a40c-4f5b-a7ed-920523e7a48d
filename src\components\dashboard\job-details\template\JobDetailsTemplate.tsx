import React from 'react';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import JobDescription from '../components/JobDescription';
import StatsCard from '../components/StatsCard';
import EducationLevelsPie from '../../components/charts/EducationPieChart';
import SalaryBrackets from '../components/SalaryBrackets';
import TopSkillsDemand from '../components/TopSkillsDemand';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import FieldsEducation from '../components/FieldsEducation';
import TopEmployers from '../../explore-jobs/components/TopEmployers';

const JobDetailsTemplate = () => {
  return (
    <div className="w-full p-8 space-y-4 mb-32">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'Career Planner', href: '/dashboard' },
          { label: 'Explore Job Trends', href: '/' },
        ]}
      />
      <div className="bg-white rounded-lg p-6 space-y-6">
        <div className="w-full flex justify-between items-center">
          <h4 className="text-blue-400 text-xl font-semibold">
            Software Developer
          </h4>
          <Button variant="outline">
            <Download />
            Download Job Card
          </Button>
        </div>
        <JobDescription />

        {/* Stats row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StatsCard
            title="Software Developer job posts in the quarter"
            value="741"
          />
          <StatsCard title="Average Years of Experience" value="1–3 years" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <EducationLevelsPie />
          <FieldsEducation />
        </div>

        <SalaryBrackets />
        <TopEmployers />
        <TopSkillsDemand />
      </div>
    </div>
  );
};

export default JobDetailsTemplate;
