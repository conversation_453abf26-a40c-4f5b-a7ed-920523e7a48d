'use client';

import Title from '@/components/ui/title';
import Text from '@/components/ui/text';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, Plus, Minus, Loader2 } from 'lucide-react';
import type { Course } from '@/types';
import {
  useGetCoursePdf,
  useSaveUserProgramCourses,
  useDeleteUserProgramCourses,
} from '@/queries';

interface CourseDetailsDialogProps {
  selected: Course | null;
  setSelected: (_selected: Course | null) => void;
  isInProgram?: boolean;
  onAddToProgram?: (_course: Course) => void;
  onRemoveFromProgram?: (_course: Course) => void;
  programId?: string;
  refreshCourses?: () => void;
}

function CourseDetailsDialog({
  selected,
  setSelected,
  isInProgram = false,
  onAddToProgram,
  onRemoveFromProgram,
  programId,
  refreshCourses,
}: CourseDetailsDialogProps) {
  // const { toast } = useToast()
  const { mutate: getCoursePdf, isPending: isDownloading } = useGetCoursePdf();
  const { mutate: saveUserProgramCourses, isPending: isSaving } =
    useSaveUserProgramCourses();
  const { mutate: deleteUserProgramCourses, isPending: isDeleting } =
    useDeleteUserProgramCourses();

  const handleAddToProgram = () => {
    if (!selected?.id || !programId) {
      return;
    }

    const data = {
      programId,
      courseIds: [selected.id],
    };

    saveUserProgramCourses(data, {
      onSuccess: () => {
        if (onAddToProgram) {
          onAddToProgram(selected);
        }

        setSelected(null);

        // Refresh the course list if the function is provided
        if (refreshCourses) {
          refreshCourses();
        }
      },
      onError: (error) => {
        console.error('Error adding course to program:', error);
        // toast({
        //   title: "Error",
        //   description: "Failed to add course to program",
        //   variant: "destructive",
        // })
      },
    });
  };

  const handleRemoveFromProgram = () => {
    if (!selected?.id || !programId) {
      return;
    }

    const data = {
      programId,
      courseIds: [selected.id],
    };

    deleteUserProgramCourses(data, {
      onSuccess: () => {
        if (onRemoveFromProgram) {
          onRemoveFromProgram(selected);
        }
        setSelected(null);
        if (refreshCourses) {
          refreshCourses();
        }
      },
      onError: (error) => {
        console.error('Error removing course from program:', error);
      },
    });
  };

  const handleDownloadCard = () => {
    if (!selected?.id) return;

    const requestBody = {
      coursePdfDto: {
        // Wrap in coursePdfDto object
        programId: programId || selected.id || '',
        courses: [
          {
            id: selected.id,
            code: selected.code || '',
            name: selected.name || '',
            mandatory: selected.type === 'Mandatory',
            credits: `${selected.credits}` || '',
            score: selected.score || '',
            added: isInProgram,
          },
        ],
      },
    };

    getCoursePdf(requestBody, {
      onSuccess: (response) => {
        if (response) {
          const url = URL.createObjectURL(response);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${selected.code || 'course'}_card.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        } else {
          console.error('Error: Response is not available');
        }
      },
      onError: (error) => {
        console.error('Error downloading course card:', error);
      },
    });
  };

  return (
    <Dialog
      open={!!selected}
      onOpenChange={(open) => {
        if (!open) setSelected(null);
      }}
    >
      <DialogContent className="bg-white p-0 rounded-xl w-[800px] max-w-[100%] max-h-[80vh] flex flex-col">
        <div className="flex justify-between items-center p-4 md:p-6">
          <DialogTitle className="hidden text-blue-400 md:block">
            Course Details
          </DialogTitle>
          <Title className="text-blue-400 md:hidden" variant="h4">
            Course Details
          </Title>
        </div>

        <div className="overflow-y-auto scrollbar-hide relative bottom-8 p-4 md:p-6">
          <Title className="text-neutral-700" variant="h3">
            {selected?.name}
          </Title>
          <div className="flex mt-2">
            <Text
              variant="large"
              className="font-semibold text-neutral-600 mr-4"
            >
              {selected?.code}
            </Text>
            <Text variant="large" className="font-semibold text-neutral-600">
              Credits: {selected?.credits}
            </Text>
          </div>

          <div className="mt-4">
            {isInProgram ? (
              <Button
                variant="outline"
                className="border-destructive-500 text-white bg-destructive-500 hover:bg-red-50"
                onClick={handleRemoveFromProgram}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Minus className="mr-2 h-4 w-4" />
                )}
                Remove from Program
              </Button>
            ) : (
              <Button onClick={handleAddToProgram} disabled={isSaving}>
                {isSaving ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="mr-2 h-4 w-4" />
                )}
                Add to Program
              </Button>
            )}
          </div>

          <Text className="mt-4 text-neutral-600 md:mt-6">
            {selected?.description}
          </Text>
          <div className="mt-4 bg-neutral-50 rounded-lg p-6 md:mt-6">
            <Title variant="h5">Key Skills Covered in the Course:</Title>
            <ul className="mt-5 pl-8 list-disc">
              {(selected?.emsiSkills || '')
                .split(';')
                .filter((skill: string) => skill !== '')
                .map((skill: string) => (
                  <li key={skill} className="[&::marker]:text-blue-400 py-1">
                    <Text variant="large" className="text-blue-400 capitalize">
                      {skill}
                    </Text>
                  </li>
                ))}
            </ul>
          </div>
        </div>

        <div className="mt-auto p-4 md:p-6">
          <Button
            variant="outline"
            onClick={handleDownloadCard}
            className="flex items-center"
            disabled={isDownloading}
          >
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Downloading...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download Course Card
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default CourseDetailsDialog;
