export interface ISkillBadge {
  title: string;
}

export interface ISkill {
  title: string;
  whyLearn: string;
  Recommended: boolean;
  badge: ISkillBadge;
}

export interface ISkillsRecommendation {
  role: string;
  skills: ISkill[];
}

export interface IWhyLearn {
  title: string;
  reasons: string[];
  icon: string;
}

export interface ICourse {
  title: string;
  provider: string;
  level: string;
  mode: string;
  duration: string;
  nextStartDate: string;
  price: string;
  imageUrl: string;
  link?: string;
}

export interface IUpskillingPlan {
  skill: string;
  headline: string;
  whyLearn: IWhyLearn;
  courses: ICourse[];
}

export interface IRole {
  title: string;
  salary: string;
}

export interface ICareerPath {
  title: string;
  timeframe: string;
  roles: IRole[];
}

export interface IGrowthOpportunity {
  targetRole: string;
  timeframe: string;
}

export interface IGrowthOpportunitySteps {
  careerPath: ICareerPath;
  growthOpportunity: IGrowthOpportunity;
}

export interface IChecklistItem {
  text: string;
}

export interface IChecklistSection {
  title: string;
  items: IChecklistItem[];
}

export interface IGrowthOpportunityChecklist {
  targetRole: string;
  timeframe: string;
  data: IChecklistSection[];
}

export interface IJobMatchParams {
  skills: string;
  jobMode: string;
  salaryExpectation: string;
  Experience: string;
  Industry: string;
  Location: string;
}

export interface IJob {
  id: string;
  title: string;
  company: string;
  location: string;
  salary: string;
}
