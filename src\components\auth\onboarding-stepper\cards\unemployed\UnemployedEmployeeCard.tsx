/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import UpdateModal from '@/components/modals/UpdateModal';
import { Button } from '@/components/ui/button';
import React, { useState } from 'react';

type Props = {
  employmentDetails: {
    status: string;
    jobStartDate: string;
    profession: string;
    category: string;
    monthlySalary: string;
    pensionableSalary: string;
  };
};

const UnemployedEmployeeCard: React.FC<Props> = ({ employmentDetails }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitUpdate = (data: any) => {
    console.warn('Employment info update requested:', data);
  };

  return (
    <div className="bg-white text-left">
      <h2 className="text-lg font-medium text-gray-900 mb-4 mt-5">
        Employment
      </h2>
      <div>
        <div className="py-2 space-y-3 mb-3">
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Status:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.status}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Previous job end date:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.jobStartDate}
            </span>
          </div>
          <div className="flex justify-between ">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Profession:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.profession}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              Category:
            </span>
            <span className="text-[16px] text-neutral-500 leading-[24px] font-normal">
              {employmentDetails.category}
            </span>
          </div>
        </div>

        <Button
          onClick={handleOpenModal}
          className="w-full py-[14px] text-[18px] font-semibold mt-4"
          variant="outline"
        >
          My details require updating
        </Button>

        <UpdateModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={handleSubmitUpdate}
          type="employment"
        />
      </div>
    </div>
  );
};

export default UnemployedEmployeeCard;
