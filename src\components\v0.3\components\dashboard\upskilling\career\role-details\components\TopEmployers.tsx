'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useState } from 'react';
import useSettingsStore from '@/zustand/store/settingsStore';
import CardinalCharts from '@/components/dashboard/components/charts/CardinalChart';

const TopEmployers = () => {
  const { appearanceSettings } = useSettingsStore();
  const [jobPostData] = useState([
    { id: 1, employer: 'Energy Jobline', jobPosts: 309 },
    { id: 2, employer: 'EPAM Anywhere', jobPosts: 239 },
    { id: 3, employer: 'Airswift', jobPosts: 211 },
    { id: 4, employer: 'Mondelez International', jobPosts: 122 },
    { id: 5, employer: 'EPAM Systems', jobPosts: 107 },
  ]);

  const { brandColor } = appearanceSettings;

  return (
    <>
      <div className="flex flex-col md:flex md:flex-row gap-8">
        <Card className="w-full md:w-1/2 mt-6 bg-white rounded-2xl h-[450px] px-4 py-12 border border-blue-100">
          <h2 className="text-lg font-semibold text-blue-400 px-4 pb-4">
            Historic Job Posts
          </h2>
          <CardinalCharts brandColor={brandColor} />
        </Card>

        <Card className="w-full md:w-1/2 mt-6 bg-white rounded-2xl h-[450px] border border-blue-100">
          <CardHeader>
            <CardTitle className="text-lg font-semibold !text-[--headingTextColor]">
              Top Employers in Kazakhstan
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-y-auto h-[400px]">
            <Table>
              <TableHeader>
                <TableRow className="bg-primary-500">
                  <TableHead className="w-12 text-center !text-white">
                    #
                  </TableHead>
                  <TableHead className="!text-white">Employer</TableHead>
                  <TableHead className="text-right !text-white">
                    No. of Job Posts
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {jobPostData.map((data, index) => (
                  <TableRow
                    key={data.id}
                    className={`hover:bg-lightGray-100 transition ${data.id % 2 === 0 ? 'bg-[#F0F2F4]' : ''}`}
                  >
                    <TableCell className="text-center">
                      <div className="w-8 h-8 flex items-center justify-center rounded-full bg-primary-500 !text-white font-semibold">
                        {index + 1}
                      </div>
                    </TableCell>
                    <TableCell className="text-[14px] leading-[25.6px] font-normal text-[#6C757D]">
                      {data.employer}
                    </TableCell>
                    <TableCell className="text-[14px] leading-[25.6px] font-normal text-[#6C757D] text-center">
                      {data.jobPosts}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default TopEmployers;
