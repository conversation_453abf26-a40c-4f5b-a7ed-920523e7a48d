'use client';

import { formatDateDDMonthYYYY } from '@/utils';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import React from 'react';

interface UserInfo {
  label: string
  value: string
}

function InfoSection({
  title,
  data,
  isLast = false,
}: {
  title: string
  data: UserInfo[]
  isLast?: boolean
}) {
  return (
    <div
      className={`my-4 pb-4 ${!isLast ? 'border-b border-neutral-200' : ''}`}
    >
      <h3 className="text-neutral-900 text-[18px] font-semibold leading-[28px] mb-2">
        {title}
      </h3>
      {data.map((item, index) => (
        <div key={index} className="flex justify-between mb-2">
          <p className="text-[16px] font-semibold leading-[24px] text-neutral-500">
            {item.label}
          </p>
          <p className="text-[16px] font-normal leading-[24px] text-neutral-500">
            {item.value || 'Not provided'}
          </p>
        </div>
      ))}
    </div>
  );
}

function EmploymentInformation() {
  const user = useAuthStore((state) => state.user);

  if (!user) {
    return (
      <div className="py-4 px-4 mt-4 border border-neutral-200 rounded-md">
        <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-[#111827]">
          Employment Information
        </h4>
        <p className="text-neutral-500 mt-2">
          Please log in to view your employment information
        </p>
      </div>
    );
  }

  // Extract employment data with fallbacks for missing fields
  const employmentData = {
    status: user.employmentStatus || 'Not provided',
    startDate: user.jobStartDate
      ? formatDateDDMonthYYYY(user.jobStartDate)
      : 'Not provided',
    profession: user.profession || 'Not provided',
    category: user.employmentCategory || 'Not provided',
    salary: user.monthlySalary ? `${user.monthlySalary} KWD` : 'Not provided',
    pensionableSalary: user.pensionableSalary
      ? `${user.pensionableSalary} KWD`
      : 'Not provided',
  };

  return (
    <div className="py-4 px-4 mt-4 border border-neutral-200 rounded-md">
      <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-neutral-900">
        Employment Information
      </h4>

      <InfoSection
        title="Employment"
        data={[
          { label: 'Status:', value: employmentData.status },
          { label: 'Current job start date:', value: employmentData.startDate },
          { label: 'Profession:', value: employmentData.profession },
          { label: 'Category:', value: employmentData.category },
        ]}
      />
      <InfoSection
        title="Salary"
        data={[
          { label: 'Monthly salary:', value: employmentData.salary },
          {
            label: 'Pensionable monthly salary:',
            value: employmentData.pensionableSalary,
          },
        ]}
        isLast={true}
      />
    </div>
  );
}

export default EmploymentInformation;
