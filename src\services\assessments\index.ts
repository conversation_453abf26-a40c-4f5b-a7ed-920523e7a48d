import type { AssessmentScore, Question } from '@/types';
import axiosClient from '@/utils/axiosClient';

const API_URL = '/ONet';

class AssessmentService {
  async getCurrentAssessment(): Promise<Question[]> {
    const response = await axiosClient.get<Question[]>(
      `${API_URL}/GetCurrentAssessment`
    );
    return response.data;
  }

  async createNewAssessment() {
    const response = await axiosClient.post(`${API_URL}/CreateNewAssessment`);
    return response.data;
  }

  async updateAssessment(answers: Array<{ id: string; answerValue: number }>) {
    const response = await axiosClient.put(
      `${API_URL}/UpdateAssessment`,
      answers
    );
    return response.data;
  }

  async getAssessmentScores(): Promise<AssessmentScore[]> {
    const response = await axiosClient.get<AssessmentScore[]>(
      `${API_URL}/GetAssessmentScores`
    );
    return response.data;
  }

  async revokeAssessment() {
    const response = await axiosClient.delete(`${API_URL}/RevokeAssessment`);
    return response.data;
  }

  async exportTestResults() {
    const response = await axiosClient.get(`${API_URL}/ExportTestResults`);
    return response.data;
  }
}

const assessmentService = new AssessmentService();
export default assessmentService;
