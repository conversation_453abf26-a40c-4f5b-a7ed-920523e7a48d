'use client';

import type React from 'react';
import Image from 'next/image';
import { Briefcase, CheckIcon } from 'lucide-react';
import { Card } from '@/components/ui/card';
import FallbackImage from '@/assets/fallback_image.svg';
import type { AppliedJob } from '@/types/jobTypes';
import { formatPostedDate, sanitizeHtml } from '@/utils';
import InterviewWarmup from './InterviewWarmup';
import RoleRecommendationCard from '../RoleRecommendation';
import { Button } from '@/components/ui/button';
import AiAssistantSuggestion from '../../../ui/AiAssistantSuggestion';

interface JobDetailsProps {
  selectedJob: AppliedJob | null;
}

const AppliedJobDetails: React.FC<JobDetailsProps> = ({ selectedJob }) => {
  if (!selectedJob) {
    return (
      <Card className="p-6 h-full rounded-lg">
        <div className="flex flex-col items-center justify-center h-full text-center p-10">
          <Briefcase className="h-12 w-12 text-neutral-300 mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-1">
            No job selected
          </h3>
          <p className="text-neutral-500 max-w-md">
            Select a job from the list to view its details
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 h-full rounded-lg">
      <div className="border-b border-neutral-200 pb-6">
        <div className="items-start gap-4">
          <div className="h-[48px] w-[84px] rounded-md flex items-center justify-center overflow-hidden mb-4">
            {selectedJob?.job?.company?.logo ? (
              <Image
                src={selectedJob?.job?.company?.logo || '/placeholder.svg'}
                alt={`${selectedJob?.job?.company?.name || 'Company'} logo`}
                width={84}
                height={48}
              />
            ) : (
              <Image
                src={FallbackImage || '/placeholder.svg'}
                alt=""
                width={84}
                height={48}
              />
            )}
          </div>
          <div>
            <h2 className="text-[20px] font-semibold text-neutral-900 capitalize leading-[28px]">
              {selectedJob.job.title}
            </h2>
            <div className="flex items-center gap-2 text-neutral-500 mt-1">
              <span className="flex items-center gap-1">
                {selectedJob.job.companyName || 'N/A'}{' '}
                {selectedJob.job.location && (
                  <>
                    <span className="text-neutral-500"> | </span>
                    <span className="flex items-center gap-1">
                      {selectedJob.job.location}
                    </span>
                  </>
                )}
              </span>
            </div>
            <div className="flex items-center text-neutral-500 text-[16px] mt-1">
              {formatPostedDate(selectedJob.job.postedDate)}
            </div>
          </div>
        </div>

        <StatusTracker status={selectedJob.status} />

        <RoleRecommendationCard />

        {/* Withdraw Application Button */}
        <div className="my-6">
          <Button
            variant="outline"
            className="border-destructive-500 text-destructive-500 hover:bg-red-50 hover:border-red-400 hover:text-red-700"
          >
            Withdraw Application
          </Button>
        </div>

        {/* AI Assistant Section */}
        <AiAssistantSuggestion />
      </div>

      <div className="py-6">
        <h3 className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-4">
          Details
        </h3>
        <div className="prose prose-sm max-w-none text-neutral-500">
          {selectedJob.job.description ? (
            <div
              className="prose prose-sm max-w-none text-[--bodyTextColor]"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(selectedJob.job.description),
              }}
            />
          ) : (
            <p className="text-[16px] font-normal leading-[24px] text-neutral-500">
              No details responsibilities provided
            </p>
          )}
        </div>
      </div>

      <InterviewWarmup />
    </Card>
  );
};

const StatusTracker: React.FC<{ status: string }> = ({ status }) => {
  const steps = ['Applied', 'Shortlisted', 'Contacted', 'Hired'];

  const getStatusStep = (status: string): number => {
    switch (status.toLowerCase()) {
      case 'applied':
        return 0;
      case 'shortlisted':
        return 1;
      case 'contacted':
        return 2;
      case 'hired':
        return 3;
      default:
        return 0;
    }
  };

  const currentStep = getStatusStep(status);

  return (
    <>
      <div className="relative w-full flex mb-2 mt-5">
        <div className="absolute top-0 left-0 h-[5px] bg-neutral-200 w-full"></div>
        <div
          className="absolute top-0 left-0 h-[5px] bg-primary-500 transition-all duration-300"
          style={{
            width: (() => {
              if (currentStep === 0) return '25%';
              if (currentStep === 1) return '50%';
              if (currentStep === 2) return '75%';
              if (currentStep === 3) return '100%';
              return '0%';
            })(),
          }}
        ></div>
      </div>

      <div className="flex items-center w-full relative mt-5">
        {steps.map((step, index) => {
          const isCompleted = index <= currentStep;

          return (
            <div
              key={step}
              className="flex-1 flex flex-col items-center relative"
            >
              <div
                className={`w-6 h-6 flex items-center justify-center rounded-full border-2 z-10 ${
                  isCompleted
                    ? 'bg-primary-500 border-primary-500'
                    : 'border-neutral-200 bg-white'
                }`}
              >
                {isCompleted ? (
                  <CheckIcon className="h-3 w-3 text-white" />
                ) : (
                  <div className="w-2 h-2 bg-neutral-200 rounded-full" />
                )}
              </div>
              <span
                className={`text-[16px] mt-2 ${isCompleted ? 'text-neutral-900 font-semibold' : 'text-neutral-400'}`}
              >
                {step}
              </span>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default AppliedJobDetails;
