'use client';

import { ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import type { CareerCardData } from '@/constants/V0.3DashboardCardData';
import { useRouter } from 'next/navigation';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';

interface CareerCardProps {
  data: CareerCardData;
}

export function CareerCard({ data }: CareerCardProps) {
  const { appliedJobs, reviewedJobs } = useJobMatchesStore();
  const router = useRouter();
  const { icon, iconLabel, title, description, progress, onClick } = data;

  const handleClick = () => {
    if (onClick) {
      // If this is the job matches card and all jobs are reviewed, go directly to completion
      if (progress?.label === 'daily suggested jobs reviewed' && reviewedJobs >= 3) {
        router.push('/V03/dashboard/top-job-matches?completed=true');
      } else {
        router.push(onClick);
      }
    }
  };

  return (
    <Card
      onClick={handleClick}
      className="overflow-hidden hover:shadow-lg transition-all duration-300 group rounded-xl p-6 cursor-pointer"
    >
      <CardContent className="">
        <div className="flex items-center justify-between">
          <div className="flex items-start gap-4 flex-1 flex-col">
            <div className="rounded-lg group-hover:scale-105 transition-transform">
              <span className="text-2xl" role="img" aria-label={iconLabel}>
                {icon}
              </span>
            </div>
            <div className="flex-1 w-full space-y-3">
              <div>
                <h2 className="text-[20px] font-semibold text-neutral-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {title}
                </h2>
                <p className="text-neutral-500 font-normal dark:text-gray-300 mt-1 text-[18px] leading-[28px]">
                  {description}
                </p>
              </div>

              {progress && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-[16px]">
                    <span className="text-neutral-500 dark:text-gray-400">
                      {progress.label === 'daily suggested jobs reviewed' ? reviewedJobs : appliedJobs}/{progress.total} {progress.label}
                    </span>
                  </div>
                  <Progress
                    value={((progress.label === 'daily suggested jobs reviewed' ? reviewedJobs : appliedJobs) / progress.total) * 100}
                    className="h-2 bg-neutral-200 dark:bg-neutral-700"
                  />
                </div>
              )}
            </div>
          </div>
          <ChevronRight className="h-6 w-6 text-neutral-500 hover:text-neutral-600 cursor-pointer group-hover:translate-x-1 transition-transform" />
        </div>
      </CardContent>
    </Card>
  );
}
