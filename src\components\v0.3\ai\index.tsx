import AIAssistantIcon from '@/assets/images/ws/assistant.svg';
import Image from 'next/image';
import React from 'react';

const V03AiAssistant = ({ onToggle }: { onToggle: () => void }) => {
  return (
    <div className="fixed bottom-16 right-10 z-50 h-[15vh] flex flex-col justify-end">
      <div className="flex justify-end">
        <div
          className="relative group cursor-pointer transition-transform hover:scale-105"
          onClick={onToggle}
          title="Open AI Assistant"
        >
          <Image
            className="w-20 h-20"
            src={AIAssistantIcon || '/placeholder.svg'}
            alt="Assistant Icon"
          />
        </div>
      </div>
    </div>
  );
};

export default V03AiAssistant;
