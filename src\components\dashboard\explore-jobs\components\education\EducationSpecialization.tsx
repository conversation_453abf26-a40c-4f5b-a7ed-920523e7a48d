'use client';

import { EducationalBarCharts } from '@/components/dashboard/components/charts/BarChart';
import CardinalCharts from '@/components/dashboard/components/charts/CardinalChart';
import EducationLevelsPie from '@/components/dashboard/components/charts/EducationPieChart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useSettingsStore from '@/zustand/store/settingsStore';
import { Button } from '@headlessui/react';
import React, { useCallback, useState } from 'react';

function EducationSpecializationTrends() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<
    'Quarter' | '1 Year' | '3 Years' | 'Custom'
  >('Quarter');
  const [isChecked, setIsChecked] = useState({
    'Quarter': false,
    '1 Year': false,
    '3 Years': false,
    'Custom': false,
  });

  const handleChoiceClick = useCallback(
    (choice: 'Quarter' | '1 Year' | '3 Years' | 'Custom') => {
      setIsChecked(() => ({
        'Quarter': false,
        '1 Year': false,
        '3 Years': false,
        'Custom': false,
        [choice]: true,
      }));
      setSelectedTimeFrame(choice);
    },
    []
  );

  const [jobPostData] = useState([
    { id: 1, employer: 'Energy Jobline', jobPosts: 309 },
    { id: 2, employer: 'EPAM Anywhere', jobPosts: 239 },
    { id: 3, employer: 'Airswift', jobPosts: 211 },
    { id: 4, employer: 'Mondelez International', jobPosts: 122 },
    { id: 5, employer: 'EPAM Systems', jobPosts: 107 },
    { id: 6, employer: 'Law', jobPosts: 195 },
    { id: 7, employer: 'EPAM Systems', jobPosts: 194 },
    { id: 8, employer: 'Grant Thorntons', jobPosts: 185 },
    { id: 9, employer: 'EPAM Systems', jobPosts: 107 },
    { id: 10, employer: 'Law', jobPosts: 189 },
  ]);
  const yearSections = ['Quarter', '1 Year', '3 Years', 'Custom'];

  return (
    <div className="w-full space-y-4 mt-10 pb-1-0">
      <div className="flex w-full items-center gap-4 mb-6">
        <div className="h-[1px] w-full bg-lightGray-100"></div>
        <h4 className="text-back-100 text-xl font-semibold whitespace-nowrap">
          Educational Specialization Trends
        </h4>
        <div className="h-[1px] w-full bg-lightGray-100"></div>
      </div>

      <div className="">
        <div className="flex justify-start gap-4">
          {yearSections.map((choice) => (
            <Button
              key={choice}
              onClick={() =>
                handleChoiceClick(
                  choice as 'Quarter' | '1 Year' | '3 Years' | 'Custom'
                )
              }
              className={`px-4 py-2  transition  ${isChecked[choice as 'Quarter' | '1 Year' | '3 Years' | 'Custom'] ? 'bg-primary-500 text-white' : 'bg-transparent text-gray-700'}`}
            >
              {choice}
            </Button>
          ))}
        </div>
      </div>

      <div className="flex gap-4">
        {selectedTimeFrame && (
          <Card className="w-[45%] bg-white rounded-2xl h-[400px]">
            <CardHeader>
              <CardTitle className="text-lg font-semibold !text-[--headingTextColor]">
                Top Employers (by total job posts) {selectedTimeFrame}
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-y-auto h-[300px]">
              <Table className="">
                <TableHeader>
                  <TableRow className="bg-primary-500">
                    <TableHead className="w-12 text-center text-white">
                      #
                    </TableHead>
                    <TableHead className="text-white">Employer</TableHead>
                    <TableHead className=" text-white">
                      No. of Job Posts
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="h-2">
                  {jobPostData.map((data, index) => (
                    <TableRow
                      key={data.id}
                      className={`hover:bg-lightGray-100 transition py-0  ${data.id % 2 === 0 ? 'bg-[#F0F2F4]' : ''}`}
                    >
                      <TableCell className="text-center">
                        <div className="w-6 h-6 flex items-center justify-center rounded-full bg-primary-500 text-white font-semibold">
                          {index + 1}
                        </div>
                      </TableCell>
                      <TableCell className="text-[14px] leading-[25.6px] font-normal text-[#6C757D]">
                        {data.employer}
                      </TableCell>
                      <TableCell className="text-[14px] leading-[25.6px] font-normal text-[#6C757D] text-center">
                        {data.jobPosts}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        <Card className="w-full md:w-[55%] p-6 bg-white rounded-2xl h-[400px]">
          <CardinalCharts brandColor={brandColor} />

          <p className="text-[11.5px] font-semibold text-[#6C757D]">
            80% of job posts in the last quarter in Kazakhstan demanded a degree
            in Accounting & Finance.
          </p>
        </Card>
      </div>

      <div className="flex gap-4">
        <Card className="w-[60%] h-[400px] rounded-md">
          <h2 className="text-lg font-semibold text-blue-400  px-8 pt-8">
            Top Occupations (by demand)
          </h2>
          <div className="flex">
            <div className="flex flex-col gap-4 relative left-8 w-[3%] items-center justify-center text-[12px]">
              <p className="rotate-90">No.</p>
              <span className="rotate-90"> Job </span>
              <span className="rotate-90"> Posts</span>
            </div>
            <div className="w-[97%]">
              <EducationalBarCharts brandColor={brandColor} />
            </div>
          </div>
        </Card>

        <Card className="w-[40%] p-4 h-[400px] rounded-md">
          <h2 className="text-lg font-semibold text-blue-400 mb-4">
            Demand by Education Level
          </h2>
          <EducationLevelsPie />
        </Card>
      </div>
    </div>
  );
}

export default EducationSpecializationTrends;
