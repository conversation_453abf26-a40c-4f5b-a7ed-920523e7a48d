'use client';

import { Button } from '@/components/ui/button';

import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import InputField from '@/components/common/InputField';
import { validationMessages } from '@/constants/VALIDATION_MESSAGE';
import type { FC } from 'react';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { Loader2 } from 'lucide-react';
import useJobMatchesStore from '@/zustand/store/jobMatchesStore';

const { emailInvalid, emailRequired, mobileNumberRequired } =
  validationMessages;

const validationSchema = Yup.object().shape({
  email: Yup.string().email(emailInvalid).required(emailRequired),
  mobile: Yup.string().required(mobileNumberRequired),
});

interface BasicDetailFormProps {
  handleBasicDetails: (_values: { email?: string; mobile?: string }) => void;
}

const BasicDetailForm: FC<BasicDetailFormProps> = ({ handleBasicDetails }) => {
  const user = useAuthStore((state) => state?.user);
  const { isLoading } = useJobMatchesStore();

  return (
    <Formik
      initialValues={{ email: user?.email, mobile: user?.mobile }}
      enableReinitialize
      validationSchema={validationSchema}
      onSubmit={async (values, { setSubmitting }) => {
        try {
          handleBasicDetails(values);
        } catch (error) {
          console.error('login failed:', error);
        } finally {
          setSubmitting(false);
        }
      }}
    >
      {({ handleChange, handleBlur, values, errors }) => (
        <Form className="grid grid-cols-1 gap-4">
          <div className="space-y-1">
            <InputField
              label="Email:"
              labelClassName="!text-[--headingTextColor] font-medium"
              error={errors.email}
              name={'email'}
              type={'email'}
              placeholder="<EMAIL>"
              value={values.email}
              handleChange={handleChange}
              handleBlur={handleBlur}
            />
          </div>
          <div className="space-y-1">
            <InputField
              label="Mobile:"
              labelClassName="!text-[--headingTextColor] font-medium"
              error={errors.mobile}
              name={'mobile'}
              type={'number'}
              placeholder="+965 5000 1234"
              value={values.mobile}
              handleChange={handleChange}
              handleBlur={handleBlur}
            />
          </div>

          <Button
            type="submit"
            disabled={isLoading === 'generatingMessage'}
            className="justify-center my-4 bg-[--buttonColor] flex items-center gap-2 text-[16px] py-[14px] px-[28px] h-[48px]"
          >
            {isLoading === 'generatingMessage' && (
              <Loader2 className="w-5 h-5 animate-spin" />
            )}
            Next
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default BasicDetailForm;
