'use client';
import React from 'react';
import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import Link from 'next/link';
import V03JobCard from '@/components/v0.3/components/dashboard/career-jobs/job-card';

function Index() {
  return (
    <section className="">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Career Opportunities',
            href: '/dashboard/career-opportunities/jobs',
          },
          {
            label: 'Jobs',
            href: 'dashboard/career-opportunities/jobs',
          },
        ]}
      />

      <div className="justify-center text-center mt-16">
        <h3 className="text-neutral-900 text-[28px] font-semibold leading-[36px]">
          Explore your career opportunities
        </h3>

        <p className=" text-[18px] font-medium leading-[28px] text-neutral-500 mt-2  mx-auto">
          Your opportunities are tailored to your career profile. To improve
          your job matches, you can{' '}
          <Link
            className="text-primary-500 hover:text-primary-600 font-semibold"
            href="/dashboard/profile"
          >
            {' '}
            customise your profile.
          </Link>
        </p>
      </div>
      <div>
        <V03JobCard />
      </div>
    </section>
  );
}

export default Index;
