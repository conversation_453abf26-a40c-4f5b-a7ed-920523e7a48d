'use client';

import GraduationCap from '@/components/icons/GraduationCapIcon';
import useSettingsStore from '@/zustand/store/settingsStore';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import React from 'react';

function EducationInformation() {
  const { appearanceSettings } = useSettingsStore();
  const user = useAuthStore((state) => state.user);
  const brandColor = appearanceSettings?.brandColor;

  if (!user) {
    return (
      <div className="py-4 px-4 mt-4 border border-[#D7DAED] rounded-md">
        <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-neutral-900">
          Education Information
        </h4>
        <p className="text-neutral-500 mt-2">
          Please log in to view your education information
        </p>
      </div>
    );
  }

  const formatDegreeName = (degreeCode: string) => {
    const degreeMap: Record<string, string> = {
      master: 'Master',
      bachelor: 'Bachelor',
      doctorate: 'Doctorate',
      associate: 'Associate Degree',
      diploma: 'Diploma',
    };
    return degreeMap[degreeCode] || degreeCode;
  };

  const formatFieldOfStudy = (field: string) => {
    return field
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="py-4 px-4 mt-4 border border-[#D7DAED] rounded-md">
      <div className="flex items-center gap-3 mb-6">
        <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-neutral-900">
          Education Information
        </h4>
      </div>

      <div className="space-y-6">
        <div className="space-y-1">
          <div className="flex items-start gap-2 items-center">
            <div className="mt-0.5">
              <GraduationCap stroke={brandColor} />
            </div>
            <div>
              <h5 className="text-[18px] font-semibold leading-[24px] text-neutral-900 items-center">
                {user.highestDegree
                  ? formatDegreeName(user.highestDegree)
                  : 'Not specific'}{' '}
                of{' '}
                {(user.fieldOfStudy && formatFieldOfStudy(user.fieldOfStudy)) ||
                  'Field of Study'}
              </h5>
              {user.institution && (
                <p className="text-[16px] font-normal leading-[24px] text-neutral-500 mt-1">
                  {user.institution || 'Institution'}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EducationInformation;
