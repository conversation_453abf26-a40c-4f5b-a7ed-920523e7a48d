'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useApplicationStore } from '@/zustand/store/userActionStore';
import { useUpdateUser } from '@/mutations';
import { Formik, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { validationMessages } from '@/constants/VALIDATION_MESSAGE';

const { emailInvalid, emailRequired, mobileNumberRequired, phoneInvalid } =
  validationMessages;

export default function ContactForm() {
  const user = useAuthStore((state) => state.user);
  const setContactInfo = useApplicationStore((state) => state.setContactInfo);
  const { updateUser } = useUpdateUser();

  const [isEditing, setIsEditing] = useState(false);
  const emailRef = useRef<HTMLInputElement>(null);
  const mobileRef = useRef<HTMLInputElement>(null);

  const initialValues = {
    email: user?.email || '',
    mobile: user?.phone || user?.mobile || user?.phoneNumber || '',
    doNotShare: false,
  };

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .required(emailRequired)
      .matches(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, emailInvalid),
    mobile: Yup.string()
      .required(mobileNumberRequired)
      .test('is-valid-phone', phoneInvalid, (value) => {
        if (!value) return false;

        const digitsOnly = value.replace(/\D/g, '');

        return (
          /^\+?[0-9\s\-()]+$/.test(value) &&
          digitsOnly.length >= 7 &&
          digitsOnly.length <= 15
        );
      }),
  });

  const handleSubmit = (values: typeof initialValues) => {
    const dataToSave = {
      email: values.email,
      mobile: values.doNotShare ? '' : values.mobile,
      doNotShare: values.doNotShare,
    };

    updateUser({ userId: user?.id as string, data: dataToSave });
    setContactInfo(dataToSave);
    setIsEditing(false);
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setTimeout(() => {
      emailRef.current?.focus();
    }, 0);
  };

  return (
    <div className="w-full max-w-2xl px-6 pb-6">
      <div className="bg-white text-left space-y-4 mt-4">
        <h1 className="text-neutral-900 text-[20px] font-semibold leading-[28px]">
          Contact
        </h1>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          enableReinitialize
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, setFieldValue }) => (
            <Form className="space-y-4">
              {/* Email */}
              <div className="flex items-center justify-between w-full">
                <Label
                  htmlFor="email"
                  className="text-[16px] font-semibold leading-[24px] text-neutral-500"
                >
                  Email:
                </Label>
                <div>
                  <input
                    ref={emailRef}
                    type="email"
                    id="email"
                    name="email"
                    value={values.email}
                    onChange={handleChange}
                    readOnly={!isEditing}
                    placeholder="<EMAIL>"
                    className="flex-1 border-0 focus:border-0 focus-visible:ring-0 focus:outline-none px-0 h-auto text-right w-[250px] text-[16px] font-semibold leading-[24px] text-neutral-500"
                  />
                </div>
              </div>
              <ErrorMessage
                name="email"
                component="div"
                className="!text-red-500 !text-sm text-right"
              />
              {/* Mobile */}
              <div className="flex items-center justify-between w-full">
                <Label
                  htmlFor="mobile"
                  className="text-[16px] font-semibold leading-[24px] text-neutral-500"
                >
                  Mobile:
                </Label>
                <div>
                  <input
                    ref={mobileRef}
                    type="tel"
                    id="mobile"
                    name="mobile"
                    value={values.mobile}
                    onChange={handleChange}
                    readOnly={!isEditing}
                    placeholder="+965 5000 1234"
                    className="flex-1 border-0 focus:border-0 focus-visible:ring-0 focus:outline-none px-0 h-auto text-right w-[250px] text-[16px] font-semibold leading-[24px] text-neutral-500"
                  />
                </div>
              </div>
              <ErrorMessage
                name="mobile"
                component="div"
                className="!text-red-500 text-sm text-right"
              />
              {/* Checkbox */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="doNotShare"
                  checked={values.doNotShare}
                  onCheckedChange={(checked) =>
                    setFieldValue('doNotShare', checked as boolean)
                  }
                  className="border-neutral-100"
                />
                <Label
                  htmlFor="doNotShare"
                  className="text-neutral-700 font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Do not share my mobile number
                </Label>
              </div>

              {!isEditing ? (
                <Button
                  type="button"
                  onClick={handleEditClick}
                  variant={'outline'}
                  className="w-full text-[18px] font-normal leading-[27px]"
                >
                  Update My Contact
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button type="submit" className="flex-1">
                    Save Changes
                  </Button>
                  <Button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    variant="outline"
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}
