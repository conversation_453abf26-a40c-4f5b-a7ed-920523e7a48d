import type {
  IGrowthOpportunityChecklist,
  IGrowthOpportunitySteps,
} from '@/types/aiTypes';
import { create } from 'zustand';

interface CareerRoadMapState {
  careerRoadMap: IGrowthOpportunitySteps | null;
  setCareerRoadMap: (_careerRoadMap: IGrowthOpportunitySteps) => void;
  getCareerRoadMap: () => IGrowthOpportunitySteps | null;
  clearCareerRoadMap: () => void;
}

interface CareerRoadMapGrowthCheckListState {
  careerRoadMapChecklist: IGrowthOpportunityChecklist | null;
  setCareerRoadMapChecklist: (
    _careerRoadMapChecklist: IGrowthOpportunityChecklist
  ) => void;
  getCareerRoadMapChecklist: () => IGrowthOpportunityChecklist | null;
  clearCareerRoadMapChecklist: () => void;
}

const useAiCareerRoadMapStore = create<CareerRoadMapState>((set, get) => ({
  careerRoadMap: null,
  setCareerRoadMap: (careerRoadMap: IGrowthOpportunitySteps) =>
    set({ careerRoadMap }),
  getCareerRoadMap: () => get().careerRoadMap,
  clearCareerRoadMap: () => set({ careerRoadMap: null }),
}));

const useAiCareerRoadMapChecklistStore =
  create<CareerRoadMapGrowthCheckListState>((set, get) => ({
    careerRoadMapChecklist: null,
    setCareerRoadMapChecklist: (
      careerRoadMapChecklist: IGrowthOpportunityChecklist
    ) => set({ careerRoadMapChecklist }),
    getCareerRoadMapChecklist: () => get().careerRoadMapChecklist,
    clearCareerRoadMapChecklist: () => set({ careerRoadMapChecklist: null }),
  }));

export { useAiCareerRoadMapStore, useAiCareerRoadMapChecklistStore };
