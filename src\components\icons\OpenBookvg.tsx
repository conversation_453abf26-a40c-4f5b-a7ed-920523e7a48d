/* eslint-disable @typescript-eslint/no-empty-object-type */
import React from 'react';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {}

export const OpenBookIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="43"
      height="43"
      viewBox="0 0 43 43"
      fill="none"
      {...props}
    >
      <rect
        x="-2.56262"
        y="-2.66406"
        width="47.7972"
        height="48"
        fill="white"
      />
    </svg>
  );
};
