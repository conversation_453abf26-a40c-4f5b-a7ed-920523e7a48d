/* eslint-disable react/no-unknown-property */
'use client';

export const Loader = () => {
  return (
    <>
      <div className={`flex w-full h-full justify-center items-center`}>
        <div className="loader"></div>
      </div>
      <style jsx>{`
        .loader {
          width: 45px;
          aspect-ratio: 0.75;
          background:
            no-repeat linear-gradient(#1e506b, #1e506b) 0 50%,
            no-repeat linear-gradient(#819ead, #819ead) 50% 50%,
            no-repeat linear-gradient(#4f778c, #4f778c) 100% 50%;
          animation: l7 1s infinite linear alternate;
        }
        @keyframes l7 {
          0% {
            background-size:
              20% 50%,
              20% 50%,
              20% 50%;
          }
          20% {
            background-size:
              20% 20%,
              20% 50%,
              20% 50%;
          }
          40% {
            background-size:
              20% 100%,
              20% 20%,
              20% 50%;
          }
          60% {
            background-size:
              20% 50%,
              20% 100%,
              20% 20%;
          }
          80% {
            background-size:
              20% 50%,
              20% 50%,
              20% 100%;
          }
          100% {
            background-size:
              20% 50%,
              20% 50%,
              20% 50%;
          }
        }
      `}</style>
    </>
  );
};
