/* eslint-disable @typescript-eslint/no-empty-object-type */
import React from 'react';

interface SidebarIconProps extends React.SVGProps<SVGSVGElement> {}

export const AICloseIcon: React.FC<SidebarIconProps> = (props) => (
  <svg
    width="128"
    height="1001"
    viewBox="0 0 128 1001"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g filter="url(#filter0_d_17089_71360)">
      <mask id="path-1-inside-1_17089_71360" fill="white">
        <path d="M28 16H116V977H28V16Z" />
      </mask>
      <path d="M28 16H116V977H28V16Z" fill="white" />
      <path
        d="M28 977H29V16H28H27V977H28Z"
        fill="#D7DAE0"
        mask="url(#path-1-inside-1_17089_71360)"
      />
      <rect x="48" y="36" width="48" height="48" rx="12" fill="#043C5B" />
      <path
        d="M81.3333 48H62.6667C61.1939 48 60 49.1939 60 50.6667V69.3333C60 70.8061 61.1939 72 62.6667 72H81.3333C82.8061 72 84 70.8061 84 69.3333V50.6667C84 49.1939 82.8061 48 81.3333 48Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M68 48V72M77.3333 64L73.3333 60L77.3333 56"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_17089_71360"
        x="0"
        y="0"
        width="128"
        height="1001"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="-8" dy="4" />
        <feGaussianBlur stdDeviation="10" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.0293353 0 0 0 0 0.206258 0 0 0 0 0.471641 0 0 0 0.05 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_17089_71360"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_17089_71360"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
