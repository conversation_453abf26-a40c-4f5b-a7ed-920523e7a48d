'use client';

import { Checkbox } from '@/components/ui/checkbox';
import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import Declaration from '../../conditions/Declaration';
import TermCondition from '../../conditions/Term&Condition';

interface AgreementsCardProps {
  data: {
    termsAccepted: boolean;
    privacyPolicyAccepted: boolean;
    declarationAccepted: boolean;
  };
  updateData: (_data: Partial<AgreementsCardProps['data']>) => void;
}

const AgreementsCard: React.FC<AgreementsCardProps> = ({
  data,
  updateData,
}) => {
  const [isTermsOpen, setIsTermsOpen] = useState(false);
  const [isDeclarationOpen, setIsDeclarationOpen] = useState(false);

  const handleTermsChange = (checked: boolean) => {
    updateData({
      termsAccepted: checked,
      privacyPolicyAccepted: checked,
    });
  };

  const handleDeclarationChange = (checked: boolean) => {
    updateData({ declarationAccepted: checked });
  };

  return (
    <div className="bg-white text-left py-6 space-y-2 px-7 pb-4 rounded-lg">
      <label className="flex gap-2 items-center">
        <Checkbox
          checked={data.termsAccepted}
          onCheckedChange={handleTermsChange}
          className="rounded
                data-[state=checked]:bg-primary-500
                data-[state=checked]:border-primary-500
                data-[state=checked]:text-white
                  "
        />
        <div className="flex text-[16px] font-normal text-center text-neutral-700">
          <p>I have read and agreed to all the</p>{' '}
          <button
            onClick={() => setIsTermsOpen(true)}
            className="font-semibold text-[16px] ml-1 !text-primary-500 hover:underline"
          >
            terms and conditions
          </button>
        </div>
      </label>
      <label className="flex gap-2 items-center">
        <Checkbox
          checked={data.declarationAccepted}
          onCheckedChange={handleDeclarationChange}
          className="rounded
                data-[state=checked]:bg-primary-500
                data-[state=checked]:border-primary-500
                data-[state=checked]:!text-white
                  "
        />
        <div className="flex text-[16px] font-normal text-center text-neutral-700">
          <p>I have read the joining</p>{' '}
          <button
            onClick={() => setIsDeclarationOpen(true)}
            className="font-semibold text-[16px] ml-1 !text-primary-500 hover:underline"
          >
            declaration
          </button>
        </div>
      </label>

      {/* Terms and Conditions Modal */}
      <Dialog open={isTermsOpen} onOpenChange={setIsTermsOpen}>
        <DialogContent className="w-full max-w-4xl h-[700px] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Terms and Conditions</DialogTitle>
          </DialogHeader>
          <TermCondition />
        </DialogContent>
      </Dialog>

      {/* Joining Declaration Modal */}
      <Dialog open={isDeclarationOpen} onOpenChange={setIsDeclarationOpen}>
        <DialogContent className="w-full max-w-4xl h-[700px] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Privacy Policy</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <Declaration />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgreementsCard;
