'use client';

import DynamicBreadcrumb from '@/components/common/DynamicBreadcrumb';
import CourseListTemplate from '@/components/v0.3/components/dashboard/upskilling/all-courses/CourseListTemplate';
import React from 'react';

export default function CourseListPage() {
  return (
    <div>
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'UpSkilling',
            href: '/dashboard/upskilling/all-courses',
          },
          {
            label: 'Explore Courses',
            href: 'dashboard/upskilling',
          },
        ]}
      />
      <div>
        <CourseListTemplate />
      </div>
    </div>
  );
}
