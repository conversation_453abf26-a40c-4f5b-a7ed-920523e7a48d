/* eslint-disable no-unused-vars */
'use client';

import React, { useState } from 'react';
import { Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import useSettingsStore from '@/zustand/store/settingsStore';
import EducationLevelsPie from '@/components/dashboard/components/charts/EducationPieChart';
import SalaryBrackets from '@/components/dashboard/job-details/components/SalaryBrackets';
import StatsCard from '@/components/dashboard/job-details/components/StatsCard';
import TopSkillsDemand from '@/components/dashboard/job-details/components/TopSkillsDemand';
import TopEmployers from './TopEmployers';
import BriefcaseAdmin from '@/components/icons/BriefcaseAdmin';

export const mockJobs = [
  {
    id: 1,
    title: 'Senior AI Scientist II',
    jobCategories: 'Software Developer',
    company: 'TechCorp Solutions',
    location: 'Careem | Kuwait City, Kuwait',
    description:
      'Software developers research, analyse and evaluate requirements for existing or new software applications and operating systems, and design, develop, test and maintain software solutions to meet these requirements.',
    salaryRange: 'KWD 380-400 per month',
    jobType: 'Full-time',
    EmploymentType: 'Remote',
    postedWhen: 'Posted 4 days ago',
  },
  {
    id: 2,
    title: 'Product Designer',
    jobCategories: 'UX/UI Designer',
    company: 'Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 3,
    title: 'Project Owner',
    jobCategories: 'Project Director',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 4,
    title: 'Machine Learning Engineer',
    jobCategories: 'Cloud Architect',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 5,
    title: 'Machine Learning Engineer',
    jobCategories: 'Cyber Security Engineer',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
  {
    id: 6,
    title: 'Data Analyst Engineer',
    jobCategories: 'Data Analyst',
    company: 'AI Innovations',
    location: 'Dubai, UAE',
    description:
      'We are seeking a Machine Learning Engineer to build scalable AI solutions...',
    salaryRange: 'AED 15,000-18,000 per month',
    jobType: 'Contract',
    EmploymentType: 'Hybrid',
    postedWhen: 'Posted 2 days ago',
  },
];

const OccupationCard: React.FC = () => {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedJob, setSelectedJob] = useState(mockJobs[0]);

  return (
    <div className="flex h-auto w-full gap-4">
      <div className="w-full bg-white rounded-md p-6">
        <>
          <div className="flex justify-between">
            <h3 className="text-[32px] font-semibold  text-blue-400">
              {selectedJob.jobCategories}
            </h3>
            <Button className="hidden md:flex" variant="outline">
              <Download />
              Download
            </Button>
          </div>

          <div className="mt-4 w-full">
            <div className="flex items-center gap-2">
              <BriefcaseAdmin stroke={brandColor} />
              <h4 className="font-semibold text-[18px] text-blue-400">
                Job Description
              </h4>
            </div>
            <p className="text-[16px] w-[98%] text-neutral-900 mt-4">
              {selectedJob.description}
            </p>
            <div className="mt-6 hidden md:flex gap-4">
              <StatsCard
                title="Software Developer job posts in the quarter"
                value="741"
              />
              <StatsCard
                title="Average years of experience listed in job posts"
                value="1–3 years"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-32 mt-10">
              <EducationLevelsPie />
            </div>

            <div className="mt-8">
              <SalaryBrackets />
              <div className="md:block hidden">
                <TopEmployers />
              </div>
              <div className="mt-8 bg-neutral-50 rounded-md">
                <TopSkillsDemand />
              </div>
            </div>
          </div>
        </>

        <Button className="flex w-full md:hidden" variant="outline">
          <Download />
          Download
        </Button>
      </div>
    </div>
  );
};

export default OccupationCard;
