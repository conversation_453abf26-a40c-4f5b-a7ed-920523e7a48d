'use client';
import './globals.css';
import PageLayout from './pageLayout';
import {
  Montserrat,
  IBM_Plex_Sans,
  Inter,
  Roboto,
  Lato,
  Work_Sans,
  Open_Sans,
  Nunito,
  Manrope,
  Crimson_Pro,
} from 'next/font/google';
import useSettingsStore from '@/zustand/store/settingsStore';

const montserrat = Montserrat({ subsets: ['latin'] });
const ibmPlexSans = IBM_Plex_Sans({ subsets: ['latin'], weight: ['200'] });
const inter = Inter({ subsets: ['latin'] });
const roboto = Roboto({ subsets: ['latin'], weight: ['300'] });
const lato = Lato({ subsets: ['latin'], weight: ['300'] });
const workSans = Work_Sans({ subsets: ['latin'], weight: ['200'] });
const openSans = Open_Sans({ subsets: ['latin'], weight: ['300'] });
const nunito = Nunito({ subsets: ['latin'], weight: ['200'] });
const manrope = Manrope({ subsets: ['latin'], weight: ['200'] });
const crimsonPro = Crimson_Pro({ subsets: ['latin'], weight: ['200'] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { appearanceSettings } = useSettingsStore();
  const fontFamilyHeading = appearanceSettings.headingFont;
  const fontFamily = appearanceSettings.bodyTextFont;
  const fontFamilyClass =
    fontFamily === 'Montserrat'
      ? montserrat.className
      : fontFamily === 'IBM Plex Sans'
        ? ibmPlexSans.className
        : fontFamily === 'Inter'
          ? inter.className
          : fontFamily === 'Roboto'
            ? roboto.className
            : fontFamily === 'Lato'
              ? lato.className
              : fontFamily === 'Work Sans'
                ? workSans.className
                : fontFamily === 'Open Sans'
                  ? openSans.className
                  : fontFamily === 'Nunito'
                    ? nunito.className
                    : fontFamily === 'Manrope'
                      ? manrope.className
                      : fontFamily === 'Crimson Pro'
                        ? crimsonPro.className
                        : '';

  const fontFamilyHeadingClass =
    fontFamilyHeading === 'Montserrat'
      ? montserrat.className
      : fontFamilyHeading === 'IBM Plex Sans'
        ? ibmPlexSans.className
        : fontFamilyHeading === 'Inter'
          ? inter.className
          : fontFamilyHeading === 'Roboto'
            ? roboto.className
            : fontFamilyHeading === 'Lato'
              ? lato.className
              : fontFamilyHeading === 'Work Sans'
                ? workSans.className
                : fontFamilyHeading === 'Open Sans'
                  ? openSans.className
                  : fontFamilyHeading === 'Nunito'
                    ? nunito.className
                    : fontFamilyHeading === 'Manrope'
                      ? manrope.className
                      : fontFamilyHeading === 'Crimson Pro'
                        ? crimsonPro.className
                        : '';

  return (
    <html lang="en" className={`${fontFamilyHeadingClass}`}>
      <body className={`${fontFamilyClass}`}>
        <div className="fixed inset-0 w-full h-screen bg-no-repeat bg-cover bg-center -z-10" />
        <PageLayout>{children}</PageLayout>
      </body>
    </html>
  );
}
