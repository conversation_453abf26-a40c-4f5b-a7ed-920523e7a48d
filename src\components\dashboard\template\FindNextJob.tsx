import React, { useEffect, useState } from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import { useRouter } from 'next/navigation';
import type { Job } from '@/types/jobTypes';
import { useJobStore } from '@/zustand/store/jobStore';
import { Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import useFormatSalary from '@/hooks/useFormatSalary';
import JobIcon from '@/components/icons/JobIcon';
import { formatPostedDate, normalizeText } from '@/utils';
import FindNextJobSkeleton from '@/components/common/skeleton/FindNextSkeleton';
import FallBackImage from '@/components/icons/FallBackImage';
import useSettingsStore from '@/zustand/store/settingsStore';

function FindNextJob() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { jobs, fetchJobs, page } = useJobStore();

  const brandColor = appearanceSettings?.brandColor;

  useEffect(() => {
    const loadJobs = async () => {
      setIsLoading(true);
      try {
        await fetchJobs({ page });
      } finally {
        setIsLoading(false);
      }
    };
    loadJobs();
  }, [page, fetchJobs]);

  useEffect(() => {
    setFilteredJobs(jobs.slice(0, 3));
  }, [jobs]);

  const formatSalary = useFormatSalary();

  return (
    <div className="flex-grow">
      <SectionHeader title="Find your next job" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <Card
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              className="p-4 border border-neutral-200 rounded-lg"
            >
              <FindNextJobSkeleton />
            </Card>
          ))
        ) : filteredJobs.length > 0 ? (
          filteredJobs.map((job) => (
            <Card
              key={job.id}
              className={`cursor-pointer p-4 border border-neutral-200 hover:bg-neutral-50 transition-colors rounded-lg `}
              onClick={() => {
                router.push(
                  `/dashboard/career-opportunities/jobs/apply-for-a-job/${job.id}`
                );
              }}
            >
              <div className="items-start gap-3">
                <div className="h-[48px] w-[84px] rounded-md bg-neutral-100 flex items-center justify-center overflow-hidden mb-2">
                  {job.companyLogo ? (
                    <Image
                      src={job.company.logo || '/placeholder.svg'}
                      alt={`${job.companyName || 'Company'} logo`}
                      width={84}
                      height={48}
                    />
                  ) : (
                    <FallBackImage stroke={brandColor} />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-[16px] font-semibold line-clamp-1 text-neutral-900 leading-[24px] mb-2">
                    {job.title}
                  </h3>

                  <div className="flex flex-wrap gap-1 mb-2">
                    <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] rounded-md">
                      {formatSalary(job.monthlyFrom)}
                    </Badge>
                    <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] rounded-md capitalize">
                      {normalizeText(job.jobType || 'N/A')}
                    </Badge>
                    <Badge className="text-[14px] bg-neutral-100 text-neutral-900 font-medium leading-[16px] rounded-md capitalize">
                      {job.jobMode || 'Not specified'}
                    </Badge>
                  </div>
                  <p className="text-[16px] text-neutral-500 mt-2 font-normal leading-[24px]">
                    {job.companyName || 'Unknown Company'} |{' '}
                    {job.location || 'Location not specified'}
                  </p>
                  <p className="text-[16px] text-neutral-500 mt-2 font-normal leading-[24px]">
                    {formatPostedDate(job?.startDate)}
                  </p>
                </div>
              </div>

              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  router.push('/dashboard/career-opportunities/jobs');
                }}
                className="w-full h-[48px] py-[10px] px-[28px] justify-center mt-4"
              >
                View and Apply
              </Button>
            </Card>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-center">
            <Briefcase className="h-10 w-10 text-neutral-300 mb-2" />
            <p className="text-neutral-500">No jobs found</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => fetchJobs()}
            >
              Refresh
            </Button>
          </div>
        )}
        <ActionCard
          onClick={() => router.push('/dashboard/career-opportunities/jobs')}
          icon={<JobIcon stroke={brandColor} />}
          title={'View all recommended jobs'}
          description={
            'Discover tailored job listings that match your skills and goals.'
          }
        />
      </div>
    </div>
  );
}

export default FindNextJob;
