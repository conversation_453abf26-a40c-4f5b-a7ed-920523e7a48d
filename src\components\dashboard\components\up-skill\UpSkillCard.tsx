'use client';

import { Card, CardContent, <PERSON>Footer, CardTitle } from '@/components/ui/card';
import { formatUSD } from '@/utils';
import Image from 'next/image';
import type React from 'react';

interface UpSkillCardProps {
  trainingImage: string;
  companyLogo: string;
  paragraph: string;
  title: string;
  metaData?: string[];
  onClick?: () => void;
  courseFee?: number;
  city?: string;
}

export const UpSkillCard: React.FC<UpSkillCardProps> = ({
  trainingImage,
  companyLogo,
  title,
  paragraph,
  metaData,
  onClick,
  courseFee = 0,
  city = 'Unknown City',
}) => {
  const handleClick = () => {
    onClick?.();
  };

  const formattedPrice = formatUSD(courseFee || 0);

  const getExperienceLevelStyle = (level: string) => {
    if (level.toLowerCase().includes('beginner')) {
      return 'bg-success-100 text-neutral-900';
    } else if (level.toLowerCase().includes('intermediate')) {
      return 'bg-warning-100 text-neutral-900';
    } else if (level.toLowerCase().includes('advanced')) {
      return 'bg-destructive-100 text-neutral-900';
    }
    return 'bg-success-100 text-success-500';
  };

  return (
    <Card
      onClick={handleClick}
      className="border rounded-lg shadow-md bg-white hover:border-primary-900 hover:border-2 cursor-pointer"
    >
      <div className="p-4">
        <div className="w-full h-48 relative overflow-hidden">
          <Image
            src={trainingImage || '/placeholder.svg'}
            alt="Training image"
            fill
            className="object-cover rounded-lg"
            quality={100}
          />
        </div>
        <div className="relative mt-5 h-12 w-28">
          <div className="relative h-full w-full">
            <Image
              src={companyLogo || '/placeholder.svg'}
              alt="Company logo"
              fill
              className="object-cover rounded-lg"
              quality={100}
            />
          </div>
        </div>
      </div>
      <CardContent className="space-y-2 px-4">
        <CardTitle className="font-semibold text-neutral-900 text-[20px]">
          {title}
        </CardTitle>
        <p className="text-[16px] text-neutral-500 font-normal"> {paragraph}</p>
        <div className="text-[20px] font-semibold text-neutral-500 space-y-1">
          <p>USD {formattedPrice}</p>
        </div>
        <div className="flex w-[300px] items-center gap-2 flex-wrap">
          {metaData?.length &&
            metaData.map((item, i) => (
              <div
                key={item}
                className={`rounded w-fit px-2 py-1 ${i === 0
                    ? getExperienceLevelStyle(item)
                    : 'bg-neutral-100 text-neutral-900'
                  }`}
              >
                <p className="font-medium text-[16px]">{item}</p>
              </div>
            ))}
        </div>
      </CardContent>
      <CardFooter className="px-4 pb-4 pt-3 capitalize">
        <p>{city}</p>
      </CardFooter>
    </Card>
  );
};
