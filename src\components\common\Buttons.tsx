'use client';

import type { ReactNode } from 'react';
import React, { useRef, useState } from 'react';

interface ButtonProps {
  type?: 'submit' | undefined
  children: ReactNode
  icon?: ReactNode
  iconPosition?: 'left' | 'right'
  variant?: 'primary' | 'secondary' | 'tertiary'
  size?: 'small' | 'medium' | 'large' | 'full'
  onClick?: (_event: React.MouseEvent<HTMLButtonElement>) => void
  disabled?: boolean
  className?: string
  isLoading?: boolean
  rest?: React.ButtonHTMLAttributes<HTMLButtonElement>
}

const Button: React.FC<ButtonProps> = ({
  type,
  children,
  variant = 'primary',
  icon,
  iconPosition = 'left',
  onClick,
  disabled = false,
  size = 'medium',
  className = '',
  isLoading = false,
  ...rest
}) => {
  let elementDisable = false;
  if (disabled && !isLoading) elementDisable = true;
  else if (isLoading) elementDisable = true;
  const btnSize = {
    small: `px-3 py-2 text-[0.875rem]/[160%]`,
    medium: `px-5 py-2 text-[1rem]/[160%] ${icon && iconPosition === 'left'
        ? 'pl-4'
        : icon && iconPosition === 'right'
          ? 'pr-4'
          : ''
      }`,
    large: `px-6 py-2 text-[1.125rem]/[160%] ${icon && iconPosition === 'left'
        ? 'pl-5'
        : icon && iconPosition === 'right'
          ? 'pr-5'
          : ''
      }`,
    full: `px-6 py-2 text-[1.125rem]/[160%] ${icon && iconPosition === 'left'
        ? 'pl-5'
        : icon && iconPosition === 'right'
          ? 'pr-5'
          : ''
      }`,
  };
  const iconSize = {
    small: 'w-[16px] h-[16px]',
    medium: 'w-[24px] h-[24px]',
    large: 'w-[24px] h-[24px]',
    full: 'w-[24px] h-[24px]',
  };
  const btnRef = useRef<HTMLButtonElement>(null);
  const [isRippling, setIsRippling] = useState(false);

  const createRipple = (
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    const button = btnRef.current;
    const x = event.clientX - (button?.getBoundingClientRect().left ?? 0);
    const y = event.clientY - (button?.getBoundingClientRect().top ?? 0);
    const ripples = document.createElement('span');

    ripples.style.opacity = '0';
    ripples.style.left = `${x}px`;
    ripples.style.top = `${y}px`;
    ripples.style.position = 'absolute';
    ripples.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
    ripples.style.transform = 'translate(-50%, -50%)';
    ripples.style.borderRadius = '50%';
    ripples.style.width = `50px`; // size of the ripple
    ripples.style.height = `50px`;
    ripples.style.animation = 'ripple 0.6s linear';
    button?.appendChild(ripples);

    setIsRippling(true);

    setTimeout(() => {
      ripples.remove();
      setIsRippling(false);
    }, 1000);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;

    createRipple(event);
    onClick?.(event);
  };

  if (variant === 'primary') {
    return (
      <button
        type={type}
        ref={btnRef}
        className={`
                    ${btnSize[size]} inline-flex items-center relative self-baseline
                    overflow-hidden bg-blue-400
                    text-white rounded-full
                    hover:bg-blue-300
                    active:bg-blue-500
                    disabled:cursor-not-allowed disabled:opacity-[0.25]
                    disabled:active:bg-blue-400
                    disabled:hover:bg-blue-400
                    ${className}
                `}
        onClick={handleClick}
        disabled={disabled || elementDisable}
        {...rest}
      >
        {isLoading && (
          <svg
            aria-hidden="true"
            role="status"
            className="inline mr-2 w-4 h-4 animate-spin"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            ></path>
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="#1C64F2"
            ></path>
          </svg>
        )}
        {icon && iconPosition === 'left' ? (
          <span
            className={`inline-block ${iconSize[size]} mr-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        <span className={`inline-block`}>{children}</span>
        {icon && iconPosition === 'right' ? (
          <span
            className={`inline-block ${iconSize[size]} ml-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        {isLoading && '...'}
        {isRippling && (
          <span
            className={`absolute top-0 left-0 right-0 bottom-0 bg-gray-500 opacity-20 rounded-full`}
          />
        )}
      </button>
    );
  }

  if (variant === 'secondary') {
    return (
      <button
        ref={btnRef}
        className={`
                    ${btnSize[size]} inline-flex items-center relative self-baseline
                    overflow-hidden
                    text-blue-400 border-blue-400 border-solid border rounded-full
                    hover:bg-blue-50
                    active:bg-blue-100
                    disabled:cursor-not-allowed disabled:opacity-[0.25]
                    disabled:active:bg-transparent
                    disabled:hover:bg-transparent
                    ${className}
                `}
        onClick={handleClick}
        disabled={disabled || elementDisable}
        {...rest}
      >
        {isLoading && (
          <svg
            aria-hidden="true"
            role="status"
            className="inline mr-2 w-4 h-4 animate-spin"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            ></path>
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="#1C64F2"
            ></path>
          </svg>
        )}
        {icon && iconPosition === 'left' ? (
          <span
            className={`inline-block ${iconSize[size]} mr-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        <span className={`inline-block`}>{children}</span>
        {icon && iconPosition === 'right' ? (
          <span
            className={`inline-block ${iconSize[size]} ml-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        {isLoading && '...'}
        {isRippling && (
          <span
            className={`absolute top-0 left-0 right-0 bottom-0 bg-gray-500 opacity-20 rounded-full`}
          />
        )}
      </button>
    );
  }

  if (variant === 'tertiary') {
    return (
      <button
        ref={btnRef}
        className={`
                    ${btnSize[size]} items-center justify-center flex relative self-baseline
                    overflow-hidden bg-transparent
                    text-blue-400 rounded-full
                    hover:bg-blue-50
                    active:bg-blue-100
                    disabled:cursor-not-allowed disabled:opacity-[0.25]
                    disabled:active:bg-transparent
                    disabled:hover:bg-transparent
                    ${className}
                `}
        onClick={handleClick}
        disabled={disabled || elementDisable}
        {...rest}
      >
        {isLoading && (
          <svg
            aria-hidden="true"
            role="status"
            className="inline mr-2 w-4 h-4 animate-spin"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            ></path>
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="#1C64F2"
            ></path>
          </svg>
        )}
        {icon && iconPosition === 'left' ? (
          <span
            className={`inline-block ${iconSize[size]} mr-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        <span className={`inline-block`}>{children}</span>
        {icon && iconPosition === 'right' ? (
          <span
            className={`inline-block ${iconSize[size]} ml-2 stroke-blue-400`}
          >
            {icon}
          </span>
        ) : null}
        {isRippling && (
          <span
            className={`absolute top-0 left-0 right-0 bottom-0 bg-gray-500 opacity-20 rounded-full`}
          />
        )}
        {isLoading && '...'}
      </button>
    );
  }
};

export default Button;
