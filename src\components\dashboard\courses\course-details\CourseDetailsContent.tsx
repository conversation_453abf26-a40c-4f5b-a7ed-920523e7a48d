'use client';

import { useState, useEffect } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import type { Course } from '@/types/courseType';
import Image from 'next/image';
import FallbackImage from '@/assets/fallback_image.svg';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckIcon, Inbox, Loader2 } from 'lucide-react';
import { useEnrollmentActions } from '@/hooks/useEnrollment';
import ApplyIcon from '@/assets/images/apply.svg';
import { formatDate, normalizeText } from '@/utils';

interface CourseDetailsContentProps {
  course: Course;
}

export default function CourseDetailsContent({
  course,
}: CourseDetailsContentProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const {
    enrollment,
    isLoadingEnrollment,
    enrollInCourse,
    isEnrolling,
    completeCourse,
    isCompleting,
  } = useEnrollmentActions(course.id || '');
  const [localCompletionStatus, setLocalCompletionStatus] = useState(false);

  // Properly synchronize local state with enrollment status
  useEffect(() => {
    if (!isLoadingEnrollment && enrollment) {
      setLocalCompletionStatus(enrollment.status === 'Completed');
    } else {
      setLocalCompletionStatus(false);
    }
  }, [enrollment, isLoadingEnrollment]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const getDurationInWeeks = () => {
    if (course.isSelfPaced) return 'Self-paced';

    if (course.duration && course.durationUnit) {
      if (course.durationUnit === 'Weeks') return `${course.duration} Weeks`;
      if (course.durationUnit === 'Days')
        return `${Math.ceil(Number.parseInt(course.duration) / 7)} Weeks`;
      if (course.durationUnit === 'Months')
        return `${Number.parseInt(course.duration) * 4} Weeks`;
    }

    return 'N/A';
  };

  const getExperienceLevelColor = () => {
    switch (course.experienceLevel?.toLowerCase()) {
      case 'beginner':
        return 'bg-success-100 text-neutral-900 font-medium';
      case 'intermediate':
        return 'bg-warning-100 text-neutral-900 font-medium';
      case 'advanced':
        return 'bg-destructive-100 text-neutral-900 font-medium';
      default:
        return 'bg-neutral-900 text-white font-medium';
    }
  };

  const handleComplete = async () => {
    try {
      await completeCourse();
      setLocalCompletionStatus(true);
    } catch (error) {
      console.error('Failed to complete course:', error);
    }
  };

  const handleApply = () => {
    if (course.courseLink) {
      window.open(course.courseLink, '_blank');
    }
  };

  const getLocationType = () => {
    if (course.deliveryMode === 'Online') return 'Online';
    if (course.deliveryMode === 'Hybrid') return 'Hybrid';
    return course.city || 'In Person';
  };

  return (
    <div>
      <div className="flex gap-4">
        <Card className="space-y-8 mt-5 w-[65%] p-6 rounded-lg">
          <div>
            <div className="relative">
              <Image
                src={course.partner?.logo || FallbackImage}
                alt={course.name}
                width={200}
                height={200}
                className={`w-[150px] h-[48px] object-cover rounded-md`}
              />
            </div>
            <div className="">
              <h2 className="text-[28px] text-neutral-900 font-semibold">
                {course.name}
              </h2>
            </div>
            <div className="">
              <h2 className="text-[16px] text-neutral-900 font-normal">
                {course.partner?.name}
              </h2>
            </div>
            <h2 className="text-xl font-semibold mb-3 text-neutral-900 leading-[32px] mt-4">
              Description
            </h2>
            <div className="text-neutral-500 whitespace-pre-line text-[16px] font-normal leading-[24px]">
              {course?.description}
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3 text-neutral-900 leading-[32px]">
              What You&apos;ll learn
            </h2>
            {course.learningObjective ? (
              <ul className="list-disc pl-5 space-y-2 text-neutral-700 text-[16px]">
                {course.learningObjective
                  .split('\n')
                  .filter(Boolean)
                  .map((objective) => (
                    <li key={objective}>{objective}</li>
                  ))}
              </ul>
            ) : (
              <p className="text-neutral-500">
                No learning objectives provided.
              </p>
            )}
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3 text-neutral-900 leading-[32px]">
              Program Outline
            </h2>
            <Accordion
              type="multiple"
              value={expandedSections}
              className="border border-neutral-100 bg-neutral-50 rounded-md"
            >
              {course.programOutlines?.map((outline, index) => (
                <AccordionItem
                  key={outline.key}
                  value={`section-${index}`}
                  className={
                    index !== course.programOutlines.length - 1
                      ? 'border-b'
                      : ''
                  }
                >
                  <AccordionTrigger
                    onClick={() => toggleSection(`section-${index}`)}
                    className="px-4 hover:no-underline bg-neutral-50 border-b hover:bg-gray-50"
                  >
                    <div className="text-left font-medium">
                      Module {index + 1}: {outline.key}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pt-2 pb-4 text-neutral-700 bg-white">
                    {outline.value}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3">Course Skills</h2>
            <div className="flex flex-wrap gap-2">
              {course.skillsCovered && course.skillsCovered.length > 0 ? (
                course.skillsCovered.map((skill) => (
                  <Badge
                    key={skill.key}
                    variant="secondary"
                    className="text-[14px] py-1.5 px-3 text-neutral-900 rounded-md w-auto bg-neutral-100"
                  >
                    {skill.key}
                    {/* {skill.value && (
                      <span className="text-[16px]">({skill.value})</span>
                    )} */}
                  </Badge>
                ))
              ) : (
                <p className="text-neutral-500">
                  No skills have been added to this course.
                </p>
              )}
            </div>
          </div>
        </Card>

        <div className="lg:col-span-1 w-[35%] mt-5">
          <div className="bg-white rounded-lg border shadow-sm p-5 space-y-5">
            <div className="">
              <Image
                src={course.coverImage || FallbackImage}
                alt={course.name}
                width={200}
                height={200}
                className={`w-full h-[266.947px] object-cover rounded-md`}
              />
            </div>

            <div className="space-y-3">
              <div className="font-semibold text-[24px] text-neutral-700 leading-[24px]">
                USD ${course.courseFee}
              </div>

              <div className="space-y-4">
                <div className="items-center space-x-4">
                  <span
                    className={`px-3 py-[6px] rounded text-[16px] ${getExperienceLevelColor()}`}
                  >
                    {course.experienceLevel || 'N/A'}
                  </span>
                  <span className="ml-2 bg-neutral-100 px-3 py-[6px] rounded text-[16px]">
                    {course.deliveryMode || 'N/A'}
                  </span>
                </div>

                <div className="flex text-[16px] font-medium space-x-4 text-neutral-900">
                  <div className="bg-neutral-100 px-3 py-[6px] rounded text-[16px] font-medium">
                    Next Start: {formatDate(course.startDate) || 'N/A'}
                  </div>
                  <div className="flex items-center">
                    <span className="bg-neutral-100 px-3 py-[6px] rounded text-[16px] font-medium">
                      {getDurationInWeeks()}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-2 text-[16px] text-neutral-500 font-normal capitalize">
                <span> {normalizeText(getLocationType())}</span>
              </div>
            </div>

            <div className="space-y-4">
              <Button className="w-full" onClick={handleApply}>
                Apply Now
                <Image src={ApplyIcon || '/placeholder.svg'} alt="" />
              </Button>

              {isLoadingEnrollment ? (
                <Button variant="outline" className="w-full" disabled>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Checking status...
                </Button>
              ) : (
                <>
                  {enrollment?.status !== 'Enrolled' &&
                    enrollment?.status !== 'Completed' && (
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => enrollInCourse()}
                        disabled={isEnrolling}
                      >
                        {isEnrolling ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Inbox className="mr-2 !size-6" />
                            Mark as Enrolled
                          </>
                        )}
                      </Button>
                    )}
                </>
              )}

              {isLoadingEnrollment ? (
                <Button variant="outline" className="w-full" disabled>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading status...
                </Button>
              ) : localCompletionStatus ? (
                <Button variant="ghost" className="w-full">
                  <CheckIcon className="mr-2 text-primary-500" />
                  Completed
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleComplete}
                  disabled={isCompleting || !enrollment}
                >
                  {isCompleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="mr-2 text-[--buttonColor]" />
                      Mark as Completed
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
