'use client';

import { Card, CardContent } from '@/components/ui/card';
import LitBulb from '@/components/icons/LitBulb';
import RecommendedCoursesCard from '../components/recommended-courses';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Checkbox } from '@/components/ui/checkbox';
import ExternalLink from '@/components/icons/ExternalLink';
import GoBackArrow from '../../components/go-back-arrow';
import useAiSkillDetailsStore from '@/zustand/store/ai/aiSkillDetailsStore';
import { Loader } from '@/components/common/Loader';
import { useParams } from 'next/navigation';

function UpskillingRecommendationDetailsTemplate() {
  const { skillDetails, isGenerating } = useAiSkillDetailsStore();
  const params = useParams();
  const skillName = params?.id ? decodeURIComponent(params.id as string) : '';

  if (!skillDetails || isGenerating) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 flex-col">
        <Loader />
        <span className="text-neutral-500 !text-white relative bottom-[20rem]">
          {skillName
            ? `Generating Upskilling Plan for ${skillName}...`
            : 'Generating Upskilling Plan...'
          }
        </span>
      </div>
    );
  }

  const skillName = skillDetails.skill;
  const description = skillDetails.headline;
  const whyLearnReasons = skillDetails.whyLearn || [];
  const freeResources = skillDetails.courses || [];

  return (
    <Card className="bg-white border border-neutral-200 w-full h-fit rounded-[20px]">
      <div className="border-b border-neutral-200 h-20 py-3 px-8 grid grid-cols-3 items-center">
        <GoBackArrow />
        <h1 className="text-[16px] font-medium leading-[24px] text-neutral-900 text-center">
          Upskilling Plan
        </h1>
      </div>
      <CardContent className="p-8 space-y-8">
        <div className="">
          <h3
            className="text-[24px] font-semibold leading-[32px]"
            style={{ color: 'var(--headingTextColor)' }}
          >
            Learn {skillName}
          </h3>
          <p
            className="text-[16px] font-normal"
            style={{ color: 'var(--bodyTextColor)' }}
          >
            {description}
          </p>
        </div>

        <Card className="bg-white border border-neutral-200 w-full h-fit rounded-[20px] p-4 space-y-2">
          <div className="flex items-center">
            <LitBulb />
            <h2
              style={{
                background:
                  'var(--Gradient-Orange, linear-gradient(135deg, #FF0016 0%, #E58500 100%))',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
              className="uppercase font-bold text-[16px] leading-[20px]"
            >
              Why learn this skill?
            </h2>
          </div>
          <ol className="list-disc px-4">
            {whyLearnReasons.reasons.map((reason, idx) => (
              <li
                key={idx}
                className="text-[16px] font-normal leading-[24px]"
                style={{ color: 'var(--bodyTextColor)' }}
              >
                {typeof reason === 'string'
                  ? reason
                  : (reason as { title?: string })?.title || 'title'}
              </li>
            ))}
          </ol>
        </Card>

        <RecommendedCoursesCard />

        <Button
          variant="outline"
          className="px-5 py-2.5 bg-transparent border-primary-500 text-primary-500 font-medium text-[16px]"
        >
          <Link href={'/V03/dashboard/upskilling/all-courses/courseList'}>
            View all {skillName} courses
          </Link>
        </Button>

        {/* Free Resources Section */}
        {freeResources.length > 0 && (
          <div className="space-y-4">
            <div className="">
              <h3
                className="text-[18px] font-semibold leading-[28px]"
                style={{ color: 'var(--headingTextColor)' }}
              >
                Free Resources Learning Checklist
              </h3>
              <p
                className="text-[16px] font-normal leading-[24px]"
                style={{ color: 'var(--bodyTextColor)' }}
              >
                {
                  'Use these hands-on resources to build confidence, sharpen real-world skills, and create projects you can showcase. Each one is designed to help you level up faster and stand out to employers.'
                }
              </p>
            </div>
            <div className="space-y-4">
              {freeResources.map((resource, idx) => (
                <Card
                  key={idx}
                  className="bg-white border border-neutral-200 w-full h-fit rounded-[20px] p-3 space-y-2 flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <Checkbox />
                    <p
                      className="text-[16px] font-medium leading-[24px] underline"
                      style={{
                        textDecorationStyle: 'solid',
                        textDecorationSkipInk: 'none',
                        textDecorationThickness: 'auto',
                        textUnderlineOffset: 'auto',
                        textUnderlinePosition: 'from-font',
                        fontStyle: 'normal',
                      }}
                    >
                      {typeof resource === 'string'
                        ? resource
                        : resource.title || 'Resource'}
                    </p>
                  </div>
                  {typeof resource === 'object' && resource.link && (
                    <a
                      href={resource.link}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink />
                    </a>
                  )}
                  {typeof resource === 'string' && <ExternalLink />}
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Show message if no free resources are available */}
        {freeResources.length === 0 && (
          <div className="space-y-4">
            <div className="">
              <h3
                className="text-[18px] font-semibold leading-[28px]"
                style={{ color: 'var(--headingTextColor)' }}
              >
                Free Resources Learning Checklist
              </h3>
              <p
                className="text-[16px] font-normal leading-[24px]"
                style={{ color: 'var(--bodyTextColor)' }}
              >
                Free learning resources will be available here once generated.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default UpskillingRecommendationDetailsTemplate;
