import { useCallback } from 'react';

export function useDownloadFile() {
  const downloadFile = useCallback(
    (base64Content: string, fileName: string) => {
      const link = document.createElement('a');
      link.href = base64Content;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    []
  );

  return { downloadFile };
}
