'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { AdminValuesCategories } from '@/constants';
import { useAdminValues } from '@/queries';
import type { Topic } from '@/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface CategoryTabsProps {
  activeCategory: string;
  onCategoryChange: (_category: string) => void;
}

export default function CategoryTabs({
  activeCategory,
  onCategoryChange,
}: CategoryTabsProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const trainingTopics = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  const mainTopics: Topic[] =
    trainingTopics.data?.data?.data?.customValues || [];

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  const checkScroll = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft <
        container.scrollWidth - container.clientWidth - 10
      );
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
      }
    };
  }, []);

  return (
    <div className="relative mb-6">
      <div className="flex items-center">
        {showLeftArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 z-10 text-white bg-[--buttonColor] shadow-md rounded-full h-12 w-12"
            onClick={scrollLeft}
          >
            <ChevronLeft className="h-10 w-10" />
          </Button>
        )}

        <div
          ref={scrollContainerRef}
          className="flex overflow-x-auto pb-2 scrollbar-hide mx-4 scroll-smooth"
          style={{ msOverflowStyle: 'none', scrollbarWidth: 'none' }}
        >
          <Tabs
            value={activeCategory}
            onValueChange={onCategoryChange}
            className="w-full"
          >
            <TabsList className="w-full justify-start overflow-x-auto scrollbar-hide mx-4 scroll-smooth bg-transparent">
              {mainTopics.map((category) => (
                <TabsTrigger
                  key={category?.id}
                  value={category?.id}
                  className={`px-4 whitespace-nowrap mr-2 rounded-md data-[state=active]:border-primary-500 data-[state=active]:bg-neutral-50 data-[state=active]:text-primary-500 text-gray-700 hover:bg-transparent`}
                >
                  {category?.label === 'all'
                    ? 'All Categories'
                    : category?.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        {showRightArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 z-10 bg-[--buttonColor] text-white shadow-md rounded-full h-12 w-12"
            onClick={scrollRight}
          >
            <ChevronRight className="h-10 w-10" />
          </Button>
        )}
      </div>
    </div>
  );
}
