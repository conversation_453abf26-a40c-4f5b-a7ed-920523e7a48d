'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';

import Title from '@/components/ui/title';
import Text from '@/components/ui/text';
import { Card } from '@/components/ui/card';
import IntroductionSvgTest from '@/components/icons/svg/IntroductionSvgTest';
import { Button } from '@/components/ui/button';
import WarningIcon from '@/components/icons/WarningSvgIcon';
import Link from 'next/link';

const DiscoverCareer: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const progress = searchParams.get('progress');

  const navigateToTestView = () => {
    router.push(`/dashboard/career-guidance/career-test/test`);
  };

  return (
    <div
      className={`p-4 relative z-10 bg-transparent min-h-full md:p-8 lg:p-8 bg-white rounded-lg w-[1200px]`}
    >
      <div className={`!p-0`}>
        <h1 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
          {' '}
          Career Test
        </h1>
        <div>
          <div className="w-[321px] h-[240px] relative bottom-24">
            <IntroductionSvgTest />
          </div>

          <Title className={`hidden text-blue-400 mt-16 lg:block`} variant="h4">
            Discover Your Ideal Career
          </Title>

          <div className="w-[85%]">
            <Text className={`text-neutral-600 my-3 md:my-5 w-full`}>
              The Career Test tool will help you discover the type of work
              activities and occupations that you would like and find exciting.
            </Text>
            <Text className={`text-neutral-600 w-full`}>
              You will identify the broad interest areas that resonate with you
              most and use your results to explore the diverse world of
              occupations.
            </Text>
          </div>

          <div className={`flex`}>
            <Card
              className={`rounded-lg bg-neutral-50  mt-4  self-start lg:mr-5 py-4 px-6`}
            >
              <div className="flex gap-2">
                <WarningIcon />
                <h1
                  className={`text-neutral-700 mb-1 font-semibold text-[16px]`}
                >
                  Disclaimer:
                </h1>
              </div>
              <h1 className={`text-neutral-600 text-[17px] leading-[24px]`}>
                This page includes information from the{' '}
                <Link
                  className={`text-primary-500 font-medium underline hover:text-primary-600 active:text-primary-300`}
                  target="_blank"
                  href="https://www.onetcenter.org/tools.html"
                >
                  O*NET Career Exploration Tools
                </Link>{' '}
                by the U.S. Department of Labor, Employment and Training
                Administration (USDOL/ETA). Used under the{' '}
                <a
                  className={`text-primary-500 font-medium underline hover:text-primary-600 active:text-primary-300`}
                  target="_blank"
                  href="https://www.onetcenter.org/license_tools.html"
                >
                  O*NET Tools Developer License
                </a>
                {/*
                 */}
                . O*NET® is a trademark of USDOL/ETA. Whiteshield Career
                Navigator has modified all or some of this information.
                USDOL/ETA has not approved, endorsed, or tested these
                modifications.
              </h1>
            </Card>
          </div>

          <Button
            onClick={navigateToTestView}
            className={`hidden text-center my-8 md:inline-flex py-[14px] px-[28px] h-[48px]`}
          >
            {Number(progress) ? 'Resume' : 'Begin'} Career Test{' '}
            {Number(progress) ? `${progress}%` : ''}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DiscoverCareer;
