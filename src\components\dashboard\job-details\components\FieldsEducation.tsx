import TrendIcon from '@/components/icons/TrendSvg';
import useSettingsStore from '@/zustand/store/settingsStore';
import React from 'react';

function FieldsEducation() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;
  return (
    <div>
      <div className="flex items-center gap-2 mb-3">
        <TrendIcon stroke={brandColor} />
        <h2 className="text-lg font-semibold text-black-100">
          Fields of Education
        </h2>
      </div>
      <ul className="list-disc px-4 text-gray-20 text-[16px] font-normal space-y-1 ml-4">
        <li>Electricity and energy</li>
        <li>Building and civil engineering</li>
        <li>Mechanics and metal trades</li>
      </ul>
    </div>
  );
}

export default FieldsEducation;
