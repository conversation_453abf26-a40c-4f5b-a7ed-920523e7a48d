'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { RxCaretLeft, RxCaretRight } from 'react-icons/rx';
import Stepper from './Stepper';
import AssistantTooltip from '@/components/common/AssistantTooltip';
import PersonalInfoCard from './cards/PersonalInfoCard';
import EmployeeCard from './cards/EmployeeCard';
import EducationInfoCard from './cards/EducationInfoCard';
import ProfessionalInfoCard from './cards/ProfessionalInfoCard';
import AgreementsCard from './cards/AgreementsCard';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/zustand/store/useAuthStore';
import { useUpdateUser } from '@/mutations';
import { useNotificationStore } from '@/zustand/store/notificationStore';
import type { CustomValues } from '@/types/customValues';

interface FormData {
  educationInfo: {
    highestDegree: string;
    institution: string;
    fieldOfStudy: string;
    subSpecialization: string;
  };
  professionalInfo: {
    skills: CustomValues[];
    certifications: CustomValues[];
    yearsOfExperience: Record<string, string>;
  };
  agreements: {
    termsAccepted: boolean;
    privacyPolicyAccepted: boolean;
    declarationAccepted: boolean;
  };
}

interface EmploymentDetails {
  first_name: string;
  last_name: string;
  status: string;
  jobStartDate: string;
  profession: string;
  category: string;
  monthlySalary: string;
  pensionableSalary: string;
}

const employmentDetails: EmploymentDetails = {
  first_name: 'Career',
  last_name: 'Navigator',
  status: 'Full time',
  jobStartDate: '32 October 2021',
  profession: 'Project Manager',
  category: 'Private',
  monthlySalary: '$380',
  pensionableSalary: '$380',
};

const steps = [
  {
    title: 'Personal Information',
    description:
      'First, let’s take a look at your personal information. Here’s what I’ve retrieved:',
  },
  {
    title: 'Employment Information',
    description:
      'Now, let’s move on to your employment details. Here’s the information I found:',
  },
  {
    title: 'Education Information',
    description:
      'Let’s add your education details now! I’ll need the following info:',
  },
  {
    title: 'Professional Information',
    description:
      'Next, let’s fill in your professional details. Here’s what I need from you:',
  },
  {
    title: 'Agreements',
    description:
      'We’re almost done! The last step is to review and agree to the necessary terms and policies.',
  },
];

const WelcomeOnboardTemplate: React.FC = () => {
  const { push } = useRouter();
  const { showNotification } = useNotificationStore();
  const { user, setUser, token, setToken } = useAuthStore();

  const [activeStep, setActiveStep] = React.useState(0);
  const [formData, setFormData] = React.useState<FormData>({
    educationInfo: {
      highestDegree: '',
      institution: '',
      fieldOfStudy: '',
      subSpecialization: '',
    },
    professionalInfo: {
      skills: [],
      certifications: [],
      yearsOfExperience: {},
    },
    agreements: {
      termsAccepted: false,
      privacyPolicyAccepted: false,
      declarationAccepted: false,
    },
  });

  const [validationErrors, setValidationErrors] = React.useState<
    Record<string, string>
  >({});

  const validateCurrentStep = (): boolean => {
    const errors: Record<string, string> = {};

    if (activeStep === 2) {
      const { highestDegree, institution, fieldOfStudy } =
        formData.educationInfo;
      if (!highestDegree) errors.highestDegree = 'Highest degree is required';
      if (!institution) errors.institution = 'Institution is required';
      if (!fieldOfStudy) errors.fieldOfStudy = 'Field of study is required';
    }

    if (activeStep === 3) {
      const { skills, yearsOfExperience } = formData.professionalInfo;
      if (!skills || skills.length === 0) errors.skills = 'Skills are required';
      if (!yearsOfExperience || Object.keys(yearsOfExperience).length === 0)
        errors.yearsOfExperience = 'Years of experience is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = (): void => {
    if (!validateCurrentStep()) {
      showNotification('Please fill all required fields', 'error');
      return;
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = (): void => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setValidationErrors({});
  };

  const updateFormData = <K extends keyof FormData>(
    section: K,
    data: Partial<FormData[K]>
  ): void => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        ...data,
      },
    }));

    // Clear validation error for the field being updated
    const fieldName = Object.keys(data)[0];
    if (fieldName && validationErrors[fieldName]) {
      const newErrors = { ...validationErrors };
      delete newErrors[fieldName];
      setValidationErrors(newErrors);
    }
  };

  const { updateUser, isLoading, isError, error } = useUpdateUser();

  useEffect(() => {
    if (token) {
      sessionStorage.setItem('auth_token', token);
    }
  }, [token]);

  const onSubmit = async (): Promise<void> => {
    if (
      !formData.agreements.declarationAccepted ||
      !formData.agreements.termsAccepted
    ) {
      showNotification('You must accept both agreements to continue', 'error');
      return;
    }

    const savedToken = sessionStorage.getItem('auth_token');
    if (!savedToken || !user?.id) {
      showNotification('Authentication error. Please try again.', 'error');
      return;
    }

    try {
      updateUser({
        userId: user.id,
        data: {
          ...formData.educationInfo,
          ...formData.professionalInfo,
          id: user.id,
          userName: user.userName,
          email: user.email,
        },
      });

      setUser({
        ...user,
        ...formData.educationInfo,
        ...formData.professionalInfo,
      });

      setToken(savedToken);
      sessionStorage.removeItem('auth_token');
      push('/dashboard');
    } catch (err) {
      showNotification('Failed to update user information', 'error');
      console.error('Error updating user:', err);
    }
  };

  return (
    <div className="max-w-[42rem] mx-auto space-y-4 mb-20">
      <h1 className="text-[28px] font-semibold text-neutral-900 mb-28 mt-10 leading-[36px]">
        {steps[activeStep]?.title}
      </h1>

      <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
        <p className="mt-2 text-[18px] font-normal text-neutral-700 text-left leading-[28px] px-6 pt-6">
          {steps[activeStep]?.description}
        </p>

        {activeStep === 0 && (
          <PersonalInfoCard employmentDetails={employmentDetails} />
        )}
        {activeStep === 1 && (
          <EmployeeCard employmentDetails={employmentDetails} />
        )}
        {activeStep === 2 && (
          <EducationInfoCard
            data={formData.educationInfo}
            updateData={(data) => updateFormData('educationInfo', data)}
            errors={validationErrors}
          />
        )}
        {activeStep === 3 && (
          <ProfessionalInfoCard
            data={formData.professionalInfo}
            updateData={(data) => updateFormData('professionalInfo', data)}
            errors={validationErrors}
          />
        )}
        {activeStep === 4 && (
          <AgreementsCard
            data={formData.agreements}
            updateData={(data) => updateFormData('agreements', data)}
          />
        )}
      </AssistantTooltip>

      {isError && (
        <p className="text-destructive-500">
          {error?.message ||
            'An error occurred while updating your information'}
        </p>
      )}

      <div className="mt-6 flex justify-between space-x-4 max-w-2xl mx-auto">
        {activeStep !== 0 && (
          <Button
            onClick={handleBack}
            variant="outline"
            className="px-4 py-2 flex items-center"
          >
            <RxCaretLeft />
            Back
          </Button>
        )}

        {activeStep === 0 && <div />}

        <Stepper currentStep={activeStep} steps={steps.length} />

        <Button
          onClick={activeStep === steps.length - 1 ? onSubmit : handleNext}
          disabled={activeStep === steps.length || isLoading}
          className="px-4 py-2  flex items-center"
        >
          {isLoading ? (
            'Submitting...'
          ) : (
            <>
              {activeStep === steps.length - 1 ? 'Continue' : 'Next'}
              <RxCaretRight />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default WelcomeOnboardTemplate;
